import React from 'react';
import { Route, Routes } from 'react-router-dom';
import MarketingMenu from '../components/marketing/MarketingMenu.jsx';
import FacebookAdsManager from '../components/marketing/FacebookAdsManager.jsx';
import WhatsAppAdsManager from '../components/marketing/WhatsAppAdsManager.jsx';
import InstagramAdsManager from '../components/marketing/InstagramAdsManager.jsx';

const MarketingRoutes = () => {
  return (
    <Routes>
      <Route path="/marketing" element={<MarketingMenu />} />
      <Route path="/marketing/facebook" element={<FacebookAdsManager />} />
      <Route path="/marketing/whatsapp" element={<WhatsAppAdsManager />} />
      <Route path="/marketing/instagram" element={<InstagramAdsManager />} />
    </Routes>
  );
};

export default MarketingRoutes;