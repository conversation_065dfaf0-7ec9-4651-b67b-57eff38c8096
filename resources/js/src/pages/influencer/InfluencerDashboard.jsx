import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  UsersIcon,
  ShoppingBagIcon,
  CurrencyRupeeIcon,
  ChartBarIcon,
  LinkIcon,
  ArrowPathIcon,
  ClockIcon,
  BanknotesIcon,
  PhotoIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerDashboard() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [dashboardData, setDashboardData] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          overview: {
            totalReferrals: {
              value: 128,
              change: 15.2,
              trend: 'up'
            },
            totalSignups: {
              value: 42,
              change: 8.3,
              trend: 'up'
            },
            totalSales: {
              value: 18,
              change: 12.5,
              trend: 'up'
            },
            totalEarnings: {
              value: 12450,
              change: 10.2,
              trend: 'up'
            }
          },
          recentReferrals: [
            { id: 1, name: 'Rahul Sharma', date: '2023-10-25', status: 'signed_up', commission: 0 },
            { id: 2, name: 'Priya Patel', date: '2023-10-24', status: 'purchased', commission: 1500 },
            { id: 3, name: 'Amit Kumar', date: '2023-10-23', status: 'visited', commission: 0 },
            { id: 4, name: 'Neha Singh', date: '2023-10-22', status: 'purchased', commission: 1200 },
            { id: 5, name: 'Vikram Joshi', date: '2023-10-21', status: 'signed_up', commission: 0 }
          ],
          pendingPayouts: {
            available: 2700,
            pending: 1500,
            nextPayoutDate: '2023-11-07'
          },
          referralsByPlatform: [
            { platform: 'Instagram', count: 65 },
            { platform: 'Facebook', count: 32 },
            { platform: 'WhatsApp', count: 18 },
            { platform: 'YouTube', count: 8 },
            { platform: 'Twitter', count: 5 }
          ],
          performanceByMonth: [
            { month: 'Jan', referrals: 8, signups: 3, sales: 1 },
            { month: 'Feb', referrals: 12, signups: 5, sales: 2 },
            { month: 'Mar', referrals: 15, signups: 6, sales: 3 },
            { month: 'Apr', referrals: 10, signups: 4, sales: 1 },
            { month: 'May', referrals: 14, signups: 5, sales: 2 },
            { month: 'Jun', referrals: 18, signups: 7, sales: 3 },
            { month: 'Jul', referrals: 22, signups: 8, sales: 4 },
            { month: 'Aug', referrals: 25, signups: 10, sales: 5 },
            { month: 'Sep', referrals: 28, signups: 12, sales: 6 },
            { month: 'Oct', referrals: 32, signups: 15, sales: 8 },
            { month: 'Nov', referrals: 0, signups: 0, sales: 0 },
            { month: 'Dec', referrals: 0, signups: 0, sales: 0 }
          ]
        };

        setDashboardData(mockData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 'visited':
        return { color: 'bg-gray-100 text-gray-800', text: 'Visited' };
      case 'signed_up':
        return { color: 'bg-blue-100 text-blue-800', text: 'Signed Up' };
      case 'purchased':
        return { color: 'bg-green-100 text-green-800', text: 'Purchased' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
    }
  };

  // Get stats cards data
  const getStatsCards = () => {
    if (!dashboardData) return [];

    return [
      {
        name: 'Total Referrals',
        value: dashboardData.overview.totalReferrals.value,
        change: dashboardData.overview.totalReferrals.change,
        trend: dashboardData.overview.totalReferrals.trend,
        icon: LinkIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Signups',
        value: dashboardData.overview.totalSignups.value,
        change: dashboardData.overview.totalSignups.change,
        trend: dashboardData.overview.totalSignups.trend,
        icon: UsersIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Total Sales',
        value: dashboardData.overview.totalSales.value,
        change: dashboardData.overview.totalSales.change,
        trend: dashboardData.overview.totalSales.trend,
        icon: ShoppingBagIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Earnings',
        value: formatCurrency(dashboardData.overview.totalEarnings.value),
        change: dashboardData.overview.totalEarnings.change,
        trend: dashboardData.overview.totalEarnings.trend,
        icon: CurrencyRupeeIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Influencer Dashboard</div>
          <h1 className="welcome-title">
            Welcome, <span style={{ color: '#25D366' }}>{currentUser?.name || 'Influencer'}</span>
          </h1>
          <p className="welcome-subtitle">
            Track your referrals, monitor your earnings, and manage your promotional content all in one place.
          </p>
        </div>
      </div>

      {/* Time Range Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Performance Overview</h2>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 90 Days</option>
              <option value="year">Last 12 Months</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 500);
              }}
            >
              <ArrowPathIcon className="h-5 w-5" />
              Refresh
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              {dashboardData && getStatsCards().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={item.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                        {item.change}%
                      </span>
                      <span className="text-gray-500 ml-1">vs. previous period</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pending Payouts */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Earnings Summary</h2>
                <Link to="/influencer/withdrawals" className="btn btn-primary">
                  <BanknotesIcon style={{ width: '16px', height: '16px' }} />
                  Request Withdrawal
                </Link>
              </div>
              {dashboardData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Available for Withdrawal</p>
                      <p className="text-2xl font-bold text-green-600">{formatCurrency(dashboardData.pendingPayouts.available)}</p>
                      <Link to="/influencer/withdrawals" className="text-sm text-green-600 mt-2 hover:underline">
                        Request Withdrawal
                      </Link>
                    </div>
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Pending Clearance</p>
                      <p className="text-2xl font-bold text-blue-600">{formatCurrency(dashboardData.pendingPayouts.pending)}</p>
                      <div className="flex items-center text-sm text-gray-500 mt-2">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        <span>7-day holding period</span>
                      </div>
                    </div>
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Next Payout Date</p>
                      <p className="text-2xl font-bold text-purple-600">{formatDate(dashboardData.pendingPayouts.nextPayoutDate)}</p>
                      <p className="text-sm text-gray-500 mt-2">Minimum withdrawal: ₹100</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Recent Referrals */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Recent Referrals</h2>
                <Link to="/influencer/performance" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  View All
                </Link>
              </div>
              {dashboardData && (
                <div className="card" style={{ padding: '0' }}>
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Date</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Commission</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dashboardData.recentReferrals.map((referral) => {
                        const statusBadge = getStatusBadge(referral.status);
                        return (
                          <tr key={referral.id} className="border-b hover:bg-gray-50">
                            <td className="px-6 py-4">
                              <div className="font-medium text-gray-900">{referral.name}</div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-500">{formatDate(referral.date)}</div>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color}`}>
                                {statusBadge.text}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm font-medium text-gray-900">
                                {referral.commission > 0 ? formatCurrency(referral.commission) : '-'}
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Referrals by Platform */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Referrals by Platform</h2>
              </div>
              {dashboardData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="space-y-4">
                    {dashboardData.referralsByPlatform.map((platform) => (
                      <div key={platform.platform} className="flex items-center">
                        <div className="w-32 text-sm font-medium">{platform.platform}</div>
                        <div className="flex-1">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-green-600 h-2.5 rounded-full"
                              style={{
                                width: `${(platform.count / Math.max(...dashboardData.referralsByPlatform.map(p => p.count))) * 100}%`,
                                backgroundColor: '#25D366'
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-16 text-right text-sm font-medium">{platform.count}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Quick Actions</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link to="/influencer/referral-links" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                      <LinkIcon className="h-6 w-6" style={{ color: '#25D366' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Generate Links</h3>
                      <p className="text-sm text-gray-500">Create custom referral links</p>
                    </div>
                  </div>
                </Link>
                <Link to="/influencer/promo-material" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                      <PhotoIcon className="h-6 w-6" style={{ color: '#075E54' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Promo Material</h3>
                      <p className="text-sm text-gray-500">Access marketing resources</p>
                    </div>
                  </div>
                </Link>
                <Link to="/influencer/content" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                      <DocumentTextIcon className="h-6 w-6" style={{ color: '#25D366' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Share Content</h3>
                      <p className="text-sm text-gray-500">Upload your promotional content</p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
