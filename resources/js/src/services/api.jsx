import axios from 'axios';
import { API_URL } from '../config';

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors (token expired, etc.)
    if (error.response && error.response.status === 401) {
      // Clear local storage and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  getCurrentUser: () => api.get('/auth/me'),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
};

// User API calls
export const userAPI = {
  getAllUsers: () => api.get('/users'),
  getUserById: (id) => api.get(`/users/${id}`),
  updateProfile: (profileData) => api.put('/users/profile', profileData),
  deleteUser: (id) => api.delete(`/users/${id}`),
};

// Store API calls
export const storeAPI = {
  getStoreInfo: () => api.get('/stores/info'),
  updateStoreInfo: (storeData) => {
    // Check if storeData is FormData (for file uploads)
    if (storeData instanceof FormData) {
      return api.put('/stores/info', storeData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } else {
      // If it's a regular JSON object
      return api.put('/stores/info', storeData);
    }
  },
  getStoreByUrl: (storeUrl) => api.get(`/stores/url/${storeUrl}`),
  verifyStore: (storeId) => api.put(`/stores/${storeId}/verify`),
  getAllStores: () => api.get('/stores'),
};

// Product API calls
export const productAPI = {
  createProduct: (productData) => {
    // Check if productData is FormData (for file uploads)
    const headers = productData instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } : {};

    return api.post('/products', productData, { headers });
  },
  getAllProducts: (queryParams) => api.get(`/products${queryParams ? `?${queryParams}` : ''}`),
  getProductById: (id) => api.get(`/products/${id}`),
  updateProduct: (id, productData) => {
    // Check if productData is FormData (for file uploads)
    const headers = productData instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } : {};

    return api.put(`/products/${id}`, productData, { headers });
  },
  deleteProduct: (id) => api.delete(`/products/${id}`),
  getProductsByStoreId: (storeId) => {
    // Add a cache buster to prevent browser caching
    const cacheBuster = `?_cb=${Date.now()}`;

    return api.get(`/products/store/${storeId}${cacheBuster}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*'
      },
      timeout: 15000, // Increased timeout for reliability
      withCredentials: false // Disable credentials for this request
    });
  },
};

// Service API calls
export const serviceAPI = {
  createService: (serviceData) => {
    // Check if serviceData is FormData (for file uploads)
    const headers = serviceData instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } : {};

    return api.post('/services', serviceData, { headers });
  },
  getAllServices: (queryParams) => api.get(`/services${queryParams ? `?${queryParams}` : ''}`),
  getServiceById: (id) => api.get(`/services/${id}`),
  updateService: (id, serviceData) => {
    // Check if serviceData is FormData (for file uploads)
    const headers = serviceData instanceof FormData ?
      { 'Content-Type': 'multipart/form-data' } : {};

    return api.put(`/services/${id}`, serviceData, { headers });
  },
  deleteService: (id) => api.delete(`/services/${id}`),
  getServicesByStoreId: (storeId) => {
    // Add a cache buster to prevent browser caching
    const cacheBuster = `?_cb=${Date.now()}`;

    return api.get(`/services/store/${storeId}${cacheBuster}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*'
      },
      timeout: 15000, // Increased timeout for reliability
      withCredentials: false // Disable credentials for this request
    });
  },
};

// Order API calls
export const orderAPI = {
  createOrder: (orderData) => api.post('/orders', orderData),
  getAllOrders: () => api.get('/orders'),
  getOrderById: (id) => api.get(`/orders/${id}`),
  updateOrderStatus: (id, statusData) => api.put(`/orders/${id}/status`, statusData),
};

// Chat Flow API calls
export const chatFlowAPI = {
  createChatFlow: (chatFlowData) => api.post('/chat-flows', chatFlowData),
  getAllChatFlows: () => api.get('/chat-flows'),
  getChatFlowById: (id) => api.get(`/chat-flows/${id}`),
  updateChatFlow: (id, chatFlowData) => api.put(`/chat-flows/${id}`, chatFlowData),
  deleteChatFlow: (id) => api.delete(`/chat-flows/${id}`),
  getDefaultChatFlowByStoreUrl: (storeUrl) => api.get(`/chat-flows/store/${storeUrl}`),
};

// Dashboard API calls
export const dashboardAPI = {
  getVendorStats: () => api.get('/dashboard/stats'),
};

// Analytics API calls
export const analyticsAPI = {
  getAnalyticsOverview: (timeRange) => api.get(`/analytics/overview${timeRange ? `?timeRange=${timeRange}` : ''}`),
  getMonthlySales: (year) => api.get(`/analytics/monthly-sales${year ? `?year=${year}` : ''}`),
  getTopProducts: (timeRange) => api.get(`/analytics/top-products${timeRange ? `?timeRange=${timeRange}` : ''}`),
  getCustomerAcquisition: () => api.get('/analytics/customer-acquisition'),
};

// Payment Method API calls
export const paymentMethodAPI = {
  getAllPaymentMethods: () => api.get('/payments/methods'),
  getPaymentMethodById: (id) => api.get(`/payments/methods/${id}`),
  createPaymentMethod: (paymentMethodData) => api.post('/payments/methods', paymentMethodData),
  updatePaymentMethod: (id, paymentMethodData) => api.put(`/payments/methods/${id}`, paymentMethodData),
  deletePaymentMethod: (id) => api.delete(`/payments/methods/${id}`),
};

// Customer API calls
export const customerAPI = {
  getAllCustomers: (queryParams) => {
    return api.get(`/customers${queryParams ? `?${queryParams}` : ''}`);
  },
  getCustomerById: (id) => api.get(`/customers/${id}`),
  createCustomer: (customerData) => api.post('/customers', customerData),
  updateCustomer: (id, customerData) => api.put(`/customers/${id}`, customerData),
  deleteCustomer: (id) => api.delete(`/customers/${id}`),
  getCustomerStats: () => api.get('/customers/stats'),
};

// Subscription API calls
export const subscriptionAPI = {
  getAllPlans: () => api.get('/subscriptions/plans'),
  getPlanById: (id) => api.get(`/subscriptions/plans/${id}`),
  getCurrentSubscription: () => api.get('/subscriptions/current'),
  createSubscription: (subscriptionData) => api.post('/subscriptions', subscriptionData),
  updateSubscriptionStatus: (id, status) => api.put(`/subscriptions/${id}/status`, { status })
};

export default api;