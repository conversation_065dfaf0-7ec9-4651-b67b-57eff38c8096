import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  UserIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  LinkIcon,
  ArrowPathIcon,
  PhotoIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerProfile() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    profilePicture: '',
    socialLinks: {
      instagram: '',
      facebook: '',
      twitter: '',
      youtube: '',
      website: ''
    },
    platforms: [
      { id: 1, name: 'Instagram', followers: 0, isActive: false },
      { id: 2, name: 'Facebook', followers: 0, isActive: false },
      { id: 3, name: 'Twitter', followers: 0, isActive: false },
      { id: 4, name: 'YouTube', followers: 0, isActive: false },
      { id: 5, name: 'WhatsApp', followers: 0, isActive: false }
    ]
  });

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          id: 1,
          name: 'Rahul Sharma',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          bio: 'Digital content creator specializing in tech reviews and lifestyle content. I help brands connect with their audience through authentic storytelling.',
          profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
          socialLinks: {
            instagram: 'https://instagram.com/rahulsharma',
            facebook: 'https://facebook.com/rahulsharma',
            twitter: 'https://twitter.com/rahulsharma',
            youtube: 'https://youtube.com/rahulsharma',
            website: 'https://rahulsharma.com'
          },
          platforms: [
            { id: 1, name: 'Instagram', followers: 15000, isActive: true },
            { id: 2, name: 'Facebook', followers: 8500, isActive: true },
            { id: 3, name: 'Twitter', followers: 5200, isActive: true },
            { id: 4, name: 'YouTube', followers: 12000, isActive: true },
            { id: 5, name: 'WhatsApp', followers: 2000, isActive: false }
          ]
        };
        
        setProfileData(mockData);
        setFormData(mockData);
      } catch (error) {
        console.error('Error fetching profile data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle social link changes
  const handleSocialLinkChange = (platform, value) => {
    setFormData({
      ...formData,
      socialLinks: {
        ...formData.socialLinks,
        [platform]: value
      }
    });
  };

  // Handle platform changes
  const handlePlatformChange = (platformId, field, value) => {
    const updatedPlatforms = formData.platforms.map(platform => 
      platform.id === platformId ? { ...platform, [field]: value } : platform
    );
    
    setFormData({
      ...formData,
      platforms: updatedPlatforms
    });
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      // In a real implementation, this would be an API call
      // For now, we'll just update the local state
      setProfileData(formData);
      setIsEditing(false);
      // Show success message
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setFormData(profileData);
    setIsEditing(false);
  };

  // Handle profile picture upload
  const handleProfilePictureUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData({
          ...formData,
          profilePicture: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle remove profile picture
  const handleRemoveProfilePicture = () => {
    setFormData({
      ...formData,
      profilePicture: ''
    });
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Influencer Profile</div>
          <h1 className="welcome-title">
            Your <span style={{ color: '#25D366' }}>Profile</span>
          </h1>
          <p className="welcome-subtitle">
            Manage your personal information, social media links, and platform details.
          </p>
        </div>
      </div>

      {loading ? (
        <div className="stats-section">
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading profile data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Profile Header */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Profile Information</h2>
              {!isEditing ? (
                <button
                  className="btn btn-primary"
                  onClick={() => setIsEditing(true)}
                >
                  <PencilIcon style={{ width: '16px', height: '16px' }} />
                  Edit Profile
                </button>
              ) : (
                <div className="flex space-x-2">
                  <button
                    className="btn btn-outline"
                    onClick={handleCancelEdit}
                    disabled={saving}
                  >
                    <XMarkIcon style={{ width: '16px', height: '16px' }} />
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleSaveProfile}
                    disabled={saving}
                  >
                    {saving ? (
                      <ArrowPathIcon style={{ width: '16px', height: '16px' }} className="animate-spin" />
                    ) : (
                      <CheckIcon style={{ width: '16px', height: '16px' }} />
                    )}
                    Save Changes
                  </button>
                </div>
              )}
            </div>

            <div className="card" style={{ padding: '24px' }}>
              <div className="flex flex-col md:flex-row gap-8">
                {/* Profile Picture */}
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <div className="w-40 h-40 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                      {formData.profilePicture ? (
                        <img
                          src={formData.profilePicture}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <UserIcon className="h-20 w-20 text-gray-400" />
                      )}
                    </div>
                    {isEditing && (
                      <div className="absolute bottom-0 right-0">
                        <label htmlFor="profile-picture-upload" className="cursor-pointer">
                          <div className="bg-green-600 text-white rounded-full p-2 shadow-md hover:bg-green-700">
                            <PencilIcon className="h-5 w-5" />
                          </div>
                          <input
                            id="profile-picture-upload"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleProfilePictureUpload}
                          />
                        </label>
                      </div>
                    )}
                  </div>
                  {isEditing && formData.profilePicture && (
                    <button
                      type="button"
                      onClick={handleRemoveProfilePicture}
                      className="mt-2 text-sm text-red-600 hover:text-red-800"
                    >
                      Remove Photo
                    </button>
                  )}
                </div>

                {/* Profile Details */}
                <div className="flex-1">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Enter your full name"
                        />
                      ) : (
                        <p className="text-gray-900">{profileData?.name}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      {isEditing ? (
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Enter your email address"
                        />
                      ) : (
                        <p className="text-gray-900">{profileData?.email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      {isEditing ? (
                        <input
                          type="text"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Enter your phone number"
                        />
                      ) : (
                        <p className="text-gray-900">{profileData?.phone}</p>
                      )}
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bio
                      </label>
                      {isEditing ? (
                        <textarea
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          rows="4"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Tell us about yourself and your content"
                        ></textarea>
                      ) : (
                        <p className="text-gray-900">{profileData?.bio}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social Media Links */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Social Media Links</h2>
            </div>

            <div className="card" style={{ padding: '24px' }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Instagram
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                      <LinkIcon className="h-5 w-5" />
                    </span>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.socialLinks.instagram}
                        onChange={(e) => handleSocialLinkChange('instagram', e.target.value)}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://instagram.com/username"
                      />
                    ) : (
                      <div className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 bg-gray-50">
                        {profileData?.socialLinks.instagram || 'Not provided'}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Facebook
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                      <LinkIcon className="h-5 w-5" />
                    </span>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.socialLinks.facebook}
                        onChange={(e) => handleSocialLinkChange('facebook', e.target.value)}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://facebook.com/username"
                      />
                    ) : (
                      <div className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 bg-gray-50">
                        {profileData?.socialLinks.facebook || 'Not provided'}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Twitter
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                      <LinkIcon className="h-5 w-5" />
                    </span>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.socialLinks.twitter}
                        onChange={(e) => handleSocialLinkChange('twitter', e.target.value)}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://twitter.com/username"
                      />
                    ) : (
                      <div className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 bg-gray-50">
                        {profileData?.socialLinks.twitter || 'Not provided'}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    YouTube
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                      <LinkIcon className="h-5 w-5" />
                    </span>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.socialLinks.youtube}
                        onChange={(e) => handleSocialLinkChange('youtube', e.target.value)}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://youtube.com/channel/username"
                      />
                    ) : (
                      <div className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 bg-gray-50">
                        {profileData?.socialLinks.youtube || 'Not provided'}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                      <LinkIcon className="h-5 w-5" />
                    </span>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.socialLinks.website}
                        onChange={(e) => handleSocialLinkChange('website', e.target.value)}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://yourwebsite.com"
                      />
                    ) : (
                      <div className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 bg-gray-50">
                        {profileData?.socialLinks.website || 'Not provided'}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Platform Details */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Platform Details</h2>
              <p className="text-sm text-gray-500">Specify which platforms you use and your follower count</p>
            </div>

            <div className="card" style={{ padding: '0' }}>
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Platform</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Followers</th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Active</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.platforms.map((platform) => (
                    <tr key={platform.id} className="border-b">
                      <td className="px-6 py-4">
                        <div className="font-medium text-gray-900">{platform.name}</div>
                      </td>
                      <td className="px-6 py-4">
                        {isEditing ? (
                          <input
                            type="number"
                            value={platform.followers}
                            onChange={(e) => handlePlatformChange(platform.id, 'followers', parseInt(e.target.value) || 0)}
                            className="w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            min="0"
                          />
                        ) : (
                          <div className="text-sm text-gray-900">{platform.followers.toLocaleString()}</div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        {isEditing ? (
                          <input
                            type="checkbox"
                            checked={platform.isActive}
                            onChange={(e) => handlePlatformChange(platform.id, 'isActive', e.target.checked)}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                        ) : (
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${platform.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                            {platform.isActive ? 'Yes' : 'No'}
                          </span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
