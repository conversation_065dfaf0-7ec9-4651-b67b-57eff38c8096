import React from 'react';
import { PlusIcon, InformationCircleIcon, ShoppingCartIcon, BriefcaseIcon } from '@heroicons/react/24/outline';

/**
 * FlowSidebar component for the Chat Flow Builder
 * Provides controls and instructions for the flow builder
 */
const FlowSidebar = ({
  isOpen,
  onAddMessage,
  onAddProductCatalog,
  onAddServiceCatalog,
  flowName,
  setFlowName,
  flowDescription,
  setFlowDescription,
  onSave
}) => {
  if (!isOpen) return null;

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto p-4 flex flex-col">
      {/* Flow Name */}
      <div className="mb-4">
        <label htmlFor="flowName" className="block text-sm font-medium text-gray-700 mb-1">
          Flow Name *
        </label>
        <input
          type="text"
          id="flowName"
          value={flowName}
          onChange={(e) => setFlowName(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
          placeholder="Enter flow name"
          required
        />
      </div>

      {/* Flow Description */}
      <div className="mb-6">
        <label htmlFor="flowDescription" className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          id="flowDescription"
          value={flowDescription || ''}
          onChange={(e) => setFlowDescription(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
          placeholder="Enter flow description (optional)"
          rows="3"
        />
      </div>

      {/* Add Message Button */}
      <button
        onClick={onAddMessage}
        className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center mb-3"
      >
        <PlusIcon className="h-5 w-5 mr-2" />
        Add Message
      </button>

      {/* Add Product Catalog Button */}
      <button
        onClick={onAddProductCatalog}
        className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center mb-3"
      >
        <ShoppingCartIcon className="h-5 w-5 mr-2" />
        Add Product Catalog
      </button>

      {/* Add Service Catalog Button */}
      <button
        onClick={onAddServiceCatalog}
        className="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center mb-6"
      >
        <BriefcaseIcon className="h-5 w-5 mr-2" />
        Add Service Catalog
      </button>

      {/* Save Flow Button */}
      <button
        onClick={onSave}
        className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors mb-6"
      >
        Save Flow
      </button>

      {/* Instructions */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
        <div className="flex items-center mb-2">
          <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
          <h3 className="text-sm font-medium text-gray-700">How to Build a Flow</h3>
        </div>
        <ol className="text-xs text-gray-600 space-y-2 ml-4 list-decimal">
          <li>Click "Add Message" to create a new message node</li>
          <li>Drag nodes to position them on the canvas</li>
          <li>Click on a node to edit its content and add buttons</li>
          <li>Connect nodes by dragging from the <span className="inline-flex items-center"><span className="w-3 h-3 bg-green-500 rounded-full mr-1"></span> green socket</span> on the right side to another node</li>
          <li>Only Quick Reply buttons can be connected to other nodes</li>
          <li>Each node can have up to 3 buttons</li>
          <li>Click on a connection to delete it</li>
          <li>Enter a flow name and click "Save Flow" when done</li>
        </ol>
      </div>

      {/* Visual Guide */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
        <div className="flex items-center mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
          </svg>
          <h3 className="text-sm font-medium text-gray-700">Connection Guide</h3>
        </div>
        <div className="text-xs text-gray-600 space-y-4">
          <div className="flex items-center justify-center bg-white p-3 rounded-md border border-gray-200">
            <div className="flex flex-col items-center">
              <div className="relative w-24 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-2">
                Message 1
                <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-green-500 border-2 border-white"></div>
              </div>
              <div className="text-center text-xs text-gray-500">Drag from green socket</div>
            </div>

            <div className="mx-4 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </div>

            <div className="flex flex-col items-center">
              <div className="relative w-24 h-12 bg-gray-100 rounded-md flex items-center justify-center mb-2">
                Message 2
                <div className="absolute -left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-green-500 border-2 border-white"></div>
              </div>
              <div className="text-center text-xs text-gray-500">Drop on any node</div>
            </div>
          </div>
        </div>
      </div>

      {/* Button Types */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
        <div className="flex items-center mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
          </svg>
          <h3 className="text-sm font-medium text-gray-700">Button Types</h3>
        </div>
        <div className="space-y-3">
          <div className="flex items-start">
            <div className="flex-shrink-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full border border-green-300 flex items-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              Quick Reply
            </div>
            <div className="text-xs text-gray-600">
              <p>Continues the conversation within the flow</p>
              <p className="text-green-600 font-medium">Can be connected to other messages</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full border border-blue-300 flex items-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
              </svg>
              URL
            </div>
            <div className="text-xs text-gray-600">
              <p>Opens an external website in the browser</p>
              <p className="text-red-600 font-medium">Cannot be connected to other messages</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full border border-purple-300 flex items-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              Call
            </div>
            <div className="text-xs text-gray-600">
              <p>Opens the phone dialer with the specified number</p>
              <p className="text-red-600 font-medium">Cannot be connected to other messages</p>
            </div>
          </div>
        </div>
        <div className="mt-3 text-xs text-gray-500 border-t border-gray-200 pt-2">
          <p className="font-medium">Maximum 3 buttons per message</p>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <div className="flex items-center mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          <h3 className="text-sm font-medium text-gray-700">Tips</h3>
        </div>
        <ul className="text-xs text-gray-600 space-y-2 ml-4 list-disc">
          <li>Use the mouse wheel to zoom in and out</li>
          <li>Hold the mouse button and drag to pan the canvas</li>
          <li>Each Quick Reply button can connect to only one message</li>
          <li>First add buttons to a message, then connect them to other messages</li>
          <li>Click on a connection to delete it</li>
        </ul>
      </div>

      <div className="mt-auto pt-4 text-xs text-gray-500 text-center">
        WhaMart Chat Flow Builder
      </div>
    </div>
  );
};

export default FlowSidebar;
