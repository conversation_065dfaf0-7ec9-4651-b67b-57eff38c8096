<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $store1 = \App\Models\Store::where('name', 'Vendor One\'s Gadget Store')->first();
        $store2 = \App\Models\Store::where('name', 'Vendor Two\'s Fashion Hub')->first();

        if ($store1) {
            \App\Models\Product::create([
                'store_id' => $store1->id,
                'name' => 'Smartphone X',
                'description' => 'Latest model with advanced features.',
                'price' => 699.99,
                'stock' => 50,
            ]);

            \App\Models\Product::create([
                'store_id' => $store1->id,
                'name' => 'Wireless Headphones',
                'description' => 'Noise-cancelling with long battery life.',
                'price' => 199.99,
                'stock' => 100,
            ]);
        }

        if ($store2) {
            \App\Models\Product::create([
                'store_id' => $store2->id,
                'name' => 'Designer T-Shirt',
                'description' => '100% cotton, available in all sizes.',
                'price' => 49.99,
                'stock' => 200,
            ]);

            \App\Models\Product::create([
                'store_id' => $store2->id,
                'name' => 'Leather Jacket',
                'description' => 'Stylish and durable, perfect for all seasons.',
                'price' => 299.99,
                'stock' => 30,
            ]);
        }
    }
}
