/* MessageTemplate styles */
.message-template {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.template-header {
  padding: 8px 12px 4px;
  font-weight: 600;
  font-size: 14px;
  color: #128C7E;
}

.template-image {
  width: calc(100% - 12px);
  padding: 0 6px;
  margin: 0 auto;
  overflow: hidden;
}

.template-img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  border-radius: 4px;
}

.template-content {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.4;
  color: #111;
  word-break: break-word;
}

.template-footer {
  padding: 4px 12px 8px;
  font-size: 12px;
  color: #8c8c8c;
  font-style: italic;
}

.template-divider {
  height: 0.5px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 0;
  width: 100%;
}

.template-button {
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  text-align: center;
}

.template-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #128C7E;
  font-size: 14px;
}

/* Button type specific styles */
.button-content.quick-reply {
  color: #128C7E;
}

.button-content.url {
  color: #0078FF;
}

.button-content.call {
  color: #9C27B0;
}
