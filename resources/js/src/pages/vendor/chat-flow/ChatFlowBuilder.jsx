import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { ReactFlowProvider } from 'reactflow';
import { ArrowLeftIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

// Import our components
import FlowBuilder from '../../../components/chat-flow/FlowBuilder';
import { chatFlowAPI } from '../../../services/api';

/**
 * ChatFlowBuilder component
 * Provides a page for creating and editing chat flows
 */
export default function ChatFlowBuilder() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = !!id;

  // State variables
  const [initialNodes, setInitialNodes] = useState([]);
  const [initialEdges, setInitialEdges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [flowName, setFlowName] = useState('');
  const [flowDescription, setFlowDescription] = useState('');

  // Load existing flow data if editing
  useEffect(() => {
    const fetchChatFlow = async () => {
      if (!isEditing) {
        // Initialize with a start node for new flows
        const initialNode = {
          id: '1',
          type: 'message',
          position: { x: 250, y: 100 },
          data: {
            id: 1,
            content: 'Welcome to our store! How can I help you today?',
            buttons: [
              { type: 'quick_reply', text: 'View Products', value: 'products' },
              { type: 'quick_reply', text: 'Check Order', value: 'order' }
            ],
            header: 'Welcome',
            footer: 'Reply to continue',
            isFirst: true
          }
        };

        setInitialNodes([initialNode]);
        setInitialEdges([]);
        setFlowName('New Chat Flow');
        return;
      }

      try {
        setLoading(true);
        const response = await chatFlowAPI.getChatFlowById(id);
        const chatFlow = response.data.chatFlow;

        // Chat flow data fetched successfully

        if (!chatFlow) {
          setError('Chat flow not found');
          setLoading(false);
          return;
        }

        setFlowName(chatFlow.name);
        setFlowDescription(chatFlow.description || '');

        // Check if flowData exists and has nodes and edges
        if (chatFlow.flowData && typeof chatFlow.flowData === 'object') {
          // Handle case where flowData might be a string (JSON)
          let parsedFlowData = chatFlow.flowData;

          if (typeof chatFlow.flowData === 'string') {
            try {
              parsedFlowData = JSON.parse(chatFlow.flowData);
              // Flow data parsed successfully
            } catch (parseErr) {
              // Error parsing flow data
              parsedFlowData = { nodes: [], edges: [] };
            }
          }

          // Ensure we have nodes and edges arrays
          const nodes = Array.isArray(parsedFlowData.nodes) ? parsedFlowData.nodes : [];
          const edges = Array.isArray(parsedFlowData.edges) ? parsedFlowData.edges : [];

          // Flow nodes and edges retrieved

          if (nodes.length > 0) {
            // Convert nodes to ReactFlow format if needed
            const formattedNodes = nodes.map(node => {
              // Ensure node has all required properties
              return {
                id: node.id || `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                type: node.type || 'message',
                position: node.position || { x: 250, y: 100 },
                data: {
                  ...(node.data || {}),
                  id: node.id || `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  content: node.data?.content || 'Message content',
                  buttons: node.data?.buttons || [],
                  header: node.data?.header || '',
                  footer: node.data?.footer || '',
                  isFirst: node.data?.isFirst || false,
                  // These handlers will be replaced by the actual handlers in FlowBuilder
                  onEdit: () => {},
                  onDelete: () => {}
                }
              };
            });

            // Nodes formatted for ReactFlow
            setInitialNodes(formattedNodes);
            setInitialEdges(edges);
          } else {
            // No nodes found in flow data, using default node
            // If no nodes, initialize with a default node
            const initialNode = {
              id: '1',
              type: 'message',
              position: { x: 250, y: 100 },
              data: {
                id: 1,
                content: 'Welcome to our store! How can I help you today?',
                buttons: [
                  { type: 'quick_reply', text: 'View Products', value: 'products' },
                  { type: 'quick_reply', text: 'Check Order', value: 'order' }
                ],
                header: 'Welcome',
                footer: 'Reply to continue',
                isFirst: true
              }
            };

            setInitialNodes([initialNode]);
            setInitialEdges([]);
          }
        } else {
          // No valid flow data found, using default node
          // If no flow data, initialize with a default node
          const initialNode = {
            id: '1',
            type: 'message',
            position: { x: 250, y: 100 },
            data: {
              id: 1,
              content: 'Welcome to our store! How can I help you today?',
              buttons: [
                { type: 'quick_reply', text: 'View Products', value: 'products' },
                { type: 'quick_reply', text: 'Check Order', value: 'order' }
              ],
              header: 'Welcome',
              footer: 'Reply to continue',
              isFirst: true
            }
          };

          setInitialNodes([initialNode]);
          setInitialEdges([]);
        }

        setError(null);
      } catch (err) {
        setError('Failed to load chat flow. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchChatFlow();
  }, [id, isEditing]);

  // Handle saving the flow
  const handleSaveFlow = async (flowData) => {
    try {
      setLoading(true);

      // Validate flow data
      if (!flowData.name || !flowData.name.trim()) {
        setError('Please provide a name for your chat flow');
        setLoading(false);
        return;
      }

      // Get nodes from the correct location in the data structure
      const nodes = flowData.flowData?.nodes || flowData.nodes || [];

      if (nodes.length === 0) {
        setError('Your chat flow must have at least one message');
        setLoading(false);
        return;
      }

      // Validate image data in nodes
      let hasImageIssues = false;
      const validatedNodes = nodes.map(node => {
        // Skip nodes without data
        if (!node || !node.data) {
          return node;
        }

        // Process image data if present
        if (node.data.image) {
          // If image is not a string or is not a valid data URL, set to null
          if (typeof node.data.image !== 'string' || !node.data.image.startsWith('data:image/')) {
            hasImageIssues = true;
            node.data.image = null;
          }
        }

        return node;
      });

      // Continue even if there were image issues

      // Prepare data for API
      const chatFlowData = {
        name: flowData.name,
        description: flowData.description || '',
        flowData: {
          nodes: validatedNodes,
          edges: flowData.flowData?.edges || flowData.edges || []
        },
        isDefault: false // Set default value
      };

      // Ensure the data can be properly serialized
      try {
        const serialized = JSON.stringify(chatFlowData);

        // Check if the serialized data is too large (most APIs have limits)
        // Increased to 40MB to match server's 50MB limit (leaving some buffer)
        if (serialized.length > 40000000) { // 40MB limit
          setError('The chat flow is too large. Please reduce the number of messages or the size of images.');
          setLoading(false);
          return;
        }
      } catch (serializeError) {
        setError('There was an error preparing the flow data. Please check for invalid content.');
        setLoading(false);
        return;
      }

      let response;
      if (isEditing) {
        // Update existing flow
        response = await chatFlowAPI.updateChatFlow(id, chatFlowData);
      } else {
        // Create new flow
        response = await chatFlowAPI.createChatFlow(chatFlowData);
      }

      setError(null);
      navigate('/vendor/chat-flow');
    } catch (err) {
      // Provide more specific error messages based on the error
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        if (err.response.status === 413) {
          setError('The chat flow is too large. Please reduce the number of messages or the size of images.');
        } else if (err.response.data && err.response.data.message) {
          setError(`Server error: ${err.response.data.message}`);
        } else {
          setError(`Server error (${err.response.status}). Please try again later.`);
        }
      } else if (err.request) {
        // The request was made but no response was received
        setError('No response from server. Please check your internet connection and try again.');
      } else {
        // Something happened in setting up the request that triggered an Error
        setError('Failed to save chat flow: ' + err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-white flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between shadow-sm z-10">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/vendor/chat-flow')}
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <h1 className="text-xl font-medium text-gray-900">
            {isEditing ? `Edit: ${flowName}` : 'Create New Chat Flow'}
          </h1>
          {loading && (
            <div className="ml-4 text-sm text-gray-500 flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isEditing ? 'Updating...' : 'Creating...'}
            </div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-b border-red-200 px-6 py-3 flex items-start">
          <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Main content */}
      <div className="flex-1 overflow-hidden">
        <ReactFlowProvider>
          <FlowBuilder
            initialNodes={initialNodes}
            initialEdges={initialEdges}
            onSave={handleSaveFlow}
            initialFlowName={flowName}
            initialFlowDescription={flowDescription}
          />
        </ReactFlowProvider>
      </div>
    </div>
  );
}
