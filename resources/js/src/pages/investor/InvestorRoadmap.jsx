import { useState, useEffect } from 'react';
import {
  Puzzle<PERSON>ieceIcon,
  CalendarIcon,
  CheckIcon,
  ClockIcon,
  BeakerIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorRoadmap() {
  const [loading, setLoading] = useState(true);
  const [roadmapData, setRoadmapData] = useState({
    quarters: [],
    features: [],
    milestones: []
  });
  const [activeQuarter, setActiveQuarter] = useState('Q3 2023');
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [feedbackText, setFeedbackText] = useState('');
  const [feedbackPriority, setFeedbackPriority] = useState('medium');

  // Fetch roadmap data
  useEffect(() => {
    const fetchRoadmapData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          quarters: ['Q3 2023', 'Q4 2023', 'Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'],
          features: [
            {
              id: 1,
              name: 'AI-Powered Chat Recommendations',
              description: 'Intelligent product recommendations based on customer chat interactions to increase conversion rates.',
              status: 'in-development',
              eta: 'Q3 2023',
              priority: 'high',
              progress: 75,
              benefits: [
                'Increase conversion rates by 15-20%',
                'Personalized product suggestions',
                'Improved customer experience'
              ],
              updates: [
                { date: '2023-07-01', text: 'Development started' },
                { date: '2023-07-15', text: 'AI model training completed' },
                { date: '2023-08-01', text: 'Integration with chat flow builder in progress' }
              ]
            },
            {
              id: 2,
              name: 'Multi-language Support',
              description: 'Support for multiple Indian languages to expand market reach to non-English speaking vendors and customers.',
              status: 'planning',
              eta: 'Q4 2023',
              priority: 'medium',
              progress: 30,
              benefits: [
                'Expand to non-English speaking markets',
                'Increase vendor base by 40%',
                'Support for 10 major Indian languages'
              ],
              updates: [
                { date: '2023-07-10', text: 'Requirements gathering completed' },
                { date: '2023-07-25', text: 'Translation service integration research in progress' }
              ]
            },
            {
              id: 3,
              name: 'Advanced Analytics Dashboard',
              description: 'Enhanced analytics with customer behavior insights, conversion funnels, and revenue forecasting.',
              status: 'in-development',
              eta: 'Q3 2023',
              priority: 'high',
              progress: 60,
              benefits: [
                'Deeper insights into customer behavior',
                'Improved decision making for vendors',
                'Revenue forecasting capabilities'
              ],
              updates: [
                { date: '2023-06-15', text: 'Development started' },
                { date: '2023-07-10', text: 'Basic metrics implementation completed' },
                { date: '2023-07-30', text: 'Working on advanced visualization components' }
              ]
            },
            {
              id: 4,
              name: 'Integrated Payment Gateway',
              description: 'Direct payment processing within WhaMart platform instead of external QR codes.',
              status: 'research',
              eta: 'Q1 2024',
              priority: 'medium',
              progress: 15,
              benefits: [
                'Streamlined checkout process',
                'Reduced cart abandonment',
                'Improved transaction tracking'
              ],
              updates: [
                { date: '2023-07-20', text: 'Initial research on payment gateway options started' }
              ]
            },
            {
              id: 5,
              name: 'Mobile App for Vendors',
              description: 'Native mobile application for vendors to manage their stores, orders, and analytics on the go.',
              status: 'planning',
              eta: 'Q2 2024',
              priority: 'medium',
              progress: 20,
              benefits: [
                'Improved vendor engagement',
                'Real-time order management',
                'Push notifications for important updates'
              ],
              updates: [
                { date: '2023-07-15', text: 'Requirements gathering started' },
                { date: '2023-07-28', text: 'Technology stack evaluation in progress' }
              ]
            },
            {
              id: 6,
              name: 'Inventory Management System',
              description: 'Advanced inventory tracking with low stock alerts, automatic reordering, and inventory forecasting.',
              status: 'planned',
              eta: 'Q4 2023',
              priority: 'high',
              progress: 10,
              benefits: [
                'Prevent stockouts and overselling',
                'Optimize inventory levels',
                'Reduce inventory carrying costs'
              ],
              updates: [
                { date: '2023-07-25', text: 'Initial planning phase started' }
              ]
            },
            {
              id: 7,
              name: 'Customer Loyalty Program',
              description: 'Reward system for repeat customers with points, tiers, and special offers to increase retention.',
              status: 'planned',
              eta: 'Q1 2024',
              priority: 'medium',
              progress: 5,
              benefits: [
                'Increase customer retention by 25%',
                'Higher customer lifetime value',
                'Improved brand loyalty'
              ],
              updates: [
                { date: '2023-07-20', text: 'Concept development initiated' }
              ]
            },
            {
              id: 8,
              name: 'WhatsApp Business API Integration',
              description: 'Direct integration with WhatsApp Business API for enhanced messaging capabilities and automation.',
              status: 'research',
              eta: 'Q2 2024',
              priority: 'high',
              progress: 15,
              benefits: [
                'Official WhatsApp verification',
                'Advanced messaging features',
                'Higher message delivery rates'
              ],
              updates: [
                { date: '2023-07-15', text: 'Initial discussions with WhatsApp Business team' },
                { date: '2023-07-30', text: 'API documentation review in progress' }
              ]
            }
          ],
          milestones: [
            {
              id: 1,
              name: 'Beta Launch of AI Recommendations',
              date: '2023-09-15',
              description: 'Limited beta release of AI-powered product recommendations to select vendors.'
            },
            {
              id: 2,
              name: 'Advanced Analytics Dashboard Release',
              date: '2023-10-01',
              description: 'Full release of enhanced analytics dashboard to all vendors.'
            },
            {
              id: 3,
              name: 'Multi-language Support Launch',
              date: '2023-12-15',
              description: 'Launch of support for 5 major Indian languages with additional languages to follow.'
            },
            {
              id: 4,
              name: 'Inventory Management System Release',
              date: '2024-01-15',
              description: 'Release of comprehensive inventory management system for all vendors.'
            },
            {
              id: 5,
              name: 'Payment Gateway Integration',
              date: '2024-03-01',
              description: 'Launch of integrated payment processing within the WhaMart platform.'
            }
          ]
        };

        setRoadmapData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching roadmap data:', error);
        setLoading(false);
      }
    };

    fetchRoadmapData();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-development':
        return 'bg-blue-100 text-blue-800';
      case 'planning':
        return 'bg-yellow-100 text-yellow-800';
      case 'research':
        return 'bg-purple-100 text-purple-800';
      case 'planned':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return CheckIcon;
      case 'in-development':
        return ArrowPathIcon;
      case 'planning':
        return ClockIcon;
      case 'research':
        return BeakerIcon;
      case 'planned':
        return PuzzlePieceIcon;
      default:
        return PuzzlePieceIcon;
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter features by quarter
  const getFeaturesByQuarter = (quarter) => {
    return roadmapData?.features.filter(feature => feature.eta === quarter) || [];
  };

  // Handle feedback button click
  const handleFeedbackClick = (feature) => {
    setSelectedFeature(feature);
    setShowFeedbackModal(true);
    setFeedbackText('');
    setFeedbackPriority('medium');
  };

  // Handle feedback submission
  const handleFeedbackSubmit = () => {
    // In a real implementation, this would send the feedback to an API
    console.log('Feedback submitted:', {
      featureId: selectedFeature.id,
      feedback: feedbackText,
      priority: feedbackPriority
    });

    // Close the modal
    setShowFeedbackModal(false);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Product Roadmap</div>
          <h1 className="welcome-title">
            Upcoming <span style={{ color: '#25D366' }}>Features</span>
          </h1>
          <p className="welcome-subtitle">
            Explore upcoming features, development progress, and product roadmap.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Export Controls */}
        <div className="flex justify-end mb-6">
          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Roadmap
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading roadmap data...</p>
          </div>
        ) : (
          <>
            {/* Quarter Tabs */}
            <div className="flex border-b mb-6 overflow-x-auto">
              {roadmapData?.quarters.map((quarter) => (
                <button
                  key={quarter}
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeQuarter === quarter
                      ? 'border-b-2 border-green-500 text-green-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveQuarter(quarter)}
                >
                  {quarter}
                </button>
              ))}
            </div>

            {/* Features for Selected Quarter */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Features for {activeQuarter}</h2>
              </div>

              {getFeaturesByQuarter(activeQuarter).length === 0 ? (
                <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                  <p className="text-gray-500">No features planned for this quarter.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {getFeaturesByQuarter(activeQuarter).map((feature) => {
                    const StatusIcon = getStatusIcon(feature.status);

                    return (
                      <div key={feature.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{feature.name}</h3>
                            <div className="flex flex-wrap gap-2 mt-2">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full flex items-center ${getStatusColor(feature.status)}`}>
                                <StatusIcon className="h-3 w-3 mr-1" />
                                {feature.status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </span>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(feature.priority)}`}>
                                {feature.priority.charAt(0).toUpperCase() + feature.priority.slice(1)} Priority
                              </span>
                              <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 flex items-center">
                                <CalendarIcon className="h-3 w-3 mr-1" />
                                ETA: {feature.eta}
                              </span>
                            </div>
                          </div>
                          <button
                            className="btn btn-outline btn-sm"
                            onClick={() => handleFeedbackClick(feature)}
                          >
                            <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                            Provide Feedback
                          </button>
                        </div>

                        <p className="text-sm text-gray-600 mb-4">{feature.description}</p>

                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm text-gray-500">Development Progress</span>
                            <span className="text-sm font-medium text-gray-900">{feature.progress}%</span>
                          </div>
                          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-green-500 rounded-full"
                              style={{ width: `${feature.progress}%` }}
                            ></div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Key Benefits</h4>
                            <ul className="space-y-1 pl-5 list-disc">
                              {feature.benefits.map((benefit, index) => (
                                <li key={index} className="text-sm text-gray-600">{benefit}</li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Updates</h4>
                            <div className="space-y-3">
                              {feature.updates.map((update, index) => (
                                <div key={index} className="flex">
                                  <div className="flex-shrink-0 h-4 w-4 rounded-full bg-green-100 flex items-center justify-center mt-1 mr-2">
                                    <div className="h-2 w-2 rounded-full bg-green-600"></div>
                                  </div>
                                  <div>
                                    <p className="text-xs text-gray-500">{formatDate(update.date)}</p>
                                    <p className="text-sm text-gray-600">{update.text}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Key Milestones */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Key Milestones</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="relative">
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                  <div className="space-y-8">
                    {roadmapData?.milestones.map((milestone) => (
                      <div key={milestone.id} className="relative pl-10">
                        <div className="absolute left-0 top-0 bg-green-100 rounded-full w-8 h-8 flex items-center justify-center">
                          <div className="bg-green-500 rounded-full w-4 h-4"></div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">{formatDate(milestone.date)}</p>
                          <h3 className="text-lg font-medium text-gray-900 mt-1">{milestone.name}</h3>
                          <p className="text-sm text-gray-600 mt-1">{milestone.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* All Planned Features */}
            <div>
              <div className="section-header">
                <h2 className="section-title">All Planned Features</h2>
              </div>
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Feature
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Priority
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          ETA
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Progress
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {roadmapData?.features.map((feature) => {
                        const StatusIcon = getStatusIcon(feature.status);

                        return (
                          <tr key={feature.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{feature.name}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full flex items-center w-fit ${getStatusColor(feature.status)}`}>
                                <StatusIcon className="h-3 w-3 mr-1" />
                                {feature.status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full w-fit ${getPriorityColor(feature.priority)}`}>
                                {feature.priority.charAt(0).toUpperCase() + feature.priority.slice(1)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {feature.eta}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                                  <div
                                    className="bg-green-500 h-2 rounded-full"
                                    style={{ width: `${feature.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-500">{feature.progress}%</span>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && selectedFeature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">Provide Feedback</h3>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setShowFeedbackModal(false)}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4">
              <h4 className="text-md font-medium text-gray-900 mb-2">{selectedFeature.name}</h4>
              <p className="text-sm text-gray-600 mb-4">{selectedFeature.description}</p>

              <div className="space-y-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Feedback
                  </label>
                  <textarea
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Share your thoughts, suggestions, or requirements for this feature..."
                    required
                  ></textarea>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority for Your Business
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="priority-high"
                        name="priority"
                        type="radio"
                        checked={feedbackPriority === 'high'}
                        onChange={() => setFeedbackPriority('high')}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                      />
                      <label htmlFor="priority-high" className="ml-3 block text-sm font-medium text-gray-700">
                        High - Critical for my business
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="priority-medium"
                        name="priority"
                        type="radio"
                        checked={feedbackPriority === 'medium'}
                        onChange={() => setFeedbackPriority('medium')}
                        className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300"
                      />
                      <label htmlFor="priority-medium" className="ml-3 block text-sm font-medium text-gray-700">
                        Medium - Important but not urgent
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="priority-low"
                        name="priority"
                        type="radio"
                        checked={feedbackPriority === 'low'}
                        onChange={() => setFeedbackPriority('low')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label htmlFor="priority-low" className="ml-3 block text-sm font-medium text-gray-700">
                        Low - Nice to have
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  className="btn btn-outline mr-3"
                  onClick={() => setShowFeedbackModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleFeedbackSubmit}
                  disabled={!feedbackText.trim()}
                >
                  <PaperAirplaneIcon className="h-5 w-5 mr-2" />
                  Submit Feedback
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
