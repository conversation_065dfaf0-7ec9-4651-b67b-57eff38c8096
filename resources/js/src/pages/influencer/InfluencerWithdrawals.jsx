import { useState, useEffect } from 'react';
import { 
  BanknotesIcon,
  ArrowPathIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerWithdrawals() {
  const [loading, setLoading] = useState(false);
  const [withdrawalData, setWithdrawalData] = useState(null);
  const [withdrawalHistory, setWithdrawalHistory] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isRequestingWithdrawal, setIsRequestingWithdrawal] = useState(false);
  const [withdrawalAmount, setWithdrawalAmount] = useState('');
  const [withdrawalMethod, setWithdrawalMethod] = useState('bank_transfer');
  const [paymentDetails, setPaymentDetails] = useState({
    accountNumber: '',
    ifscCode: '',
    accountName: '',
    bankName: '',
    upiId: '',
    paytmNumber: ''
  });
  const [error, setError] = useState('');

  // Fetch withdrawal data
  useEffect(() => {
    const fetchWithdrawalData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          availableBalance: 2700,
          pendingBalance: 1500,
          minimumWithdrawal: 100,
          withdrawalFee: 0,
          nextPayoutDate: '2023-11-07',
          paymentMethods: [
            { id: 'bank_transfer', name: 'Bank Transfer' },
            { id: 'upi', name: 'UPI' },
            { id: 'paytm', name: 'Paytm' }
          ]
        };
        
        const mockHistory = [
          { id: 1, amount: 1200, status: 'completed', date: '2023-10-15', method: 'bank_transfer', reference: 'WD123456' },
          { id: 2, amount: 800, status: 'completed', date: '2023-09-20', method: 'upi', reference: 'WD123455' },
          { id: 3, amount: 1500, status: 'processing', date: '2023-10-25', method: 'bank_transfer', reference: 'WD123457' },
          { id: 4, amount: 500, status: 'rejected', date: '2023-08-10', method: 'paytm', reference: 'WD123454', rejectionReason: 'Invalid payment details' },
          { id: 5, amount: 1000, status: 'completed', date: '2023-07-25', method: 'bank_transfer', reference: 'WD123453' }
        ];
        
        setWithdrawalData(mockData);
        setWithdrawalHistory(mockHistory);
        setTotalPages(Math.ceil(mockHistory.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching withdrawal data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWithdrawalData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return { color: 'bg-green-100 text-green-800', text: 'Completed', icon: CheckCircleIcon };
      case 'processing':
        return { color: 'bg-blue-100 text-blue-800', text: 'Processing', icon: ClockIcon };
      case 'rejected':
        return { color: 'bg-red-100 text-red-800', text: 'Rejected', icon: XCircleIcon };
      case 'pending':
        return { color: 'bg-yellow-100 text-yellow-800', text: 'Pending', icon: ExclamationCircleIcon };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown', icon: ExclamationCircleIcon };
    }
  };

  // Get payment method name
  const getPaymentMethodName = (methodId) => {
    if (!withdrawalData) return methodId;
    const method = withdrawalData.paymentMethods.find(m => m.id === methodId);
    return method ? method.name : methodId;
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle withdrawal request
  const handleWithdrawalRequest = () => {
    setIsRequestingWithdrawal(true);
    setError('');
    setWithdrawalAmount('');
    setWithdrawalMethod('bank_transfer');
    setPaymentDetails({
      accountNumber: '',
      ifscCode: '',
      accountName: '',
      bankName: '',
      upiId: '',
      paytmNumber: ''
    });
  };

  // Handle withdrawal amount change
  const handleAmountChange = (e) => {
    setWithdrawalAmount(e.target.value);
  };

  // Handle payment method change
  const handleMethodChange = (e) => {
    setWithdrawalMethod(e.target.value);
  };

  // Handle payment details change
  const handlePaymentDetailsChange = (e) => {
    const { name, value } = e.target;
    setPaymentDetails({
      ...paymentDetails,
      [name]: value
    });
  };

  // Handle submit withdrawal
  const handleSubmitWithdrawal = () => {
    // Validate amount
    const amount = parseFloat(withdrawalAmount);
    if (isNaN(amount) || amount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (amount < withdrawalData.minimumWithdrawal) {
      setError(`Minimum withdrawal amount is ${formatCurrency(withdrawalData.minimumWithdrawal)}`);
      return;
    }

    if (amount > withdrawalData.availableBalance) {
      setError('Withdrawal amount exceeds available balance');
      return;
    }

    // Validate payment details
    if (withdrawalMethod === 'bank_transfer') {
      if (!paymentDetails.accountNumber || !paymentDetails.ifscCode || !paymentDetails.accountName || !paymentDetails.bankName) {
        setError('Please fill in all bank details');
        return;
      }
    } else if (withdrawalMethod === 'upi') {
      if (!paymentDetails.upiId) {
        setError('Please enter your UPI ID');
        return;
      }
    } else if (withdrawalMethod === 'paytm') {
      if (!paymentDetails.paytmNumber) {
        setError('Please enter your Paytm number');
        return;
      }
    }

    // In a real implementation, this would be an API call
    // For now, we'll just show a success message
    alert(`Withdrawal request for ${formatCurrency(amount)} submitted successfully!`);
    
    // Add the new withdrawal to history
    const newWithdrawal = {
      id: Date.now(),
      amount,
      status: 'pending',
      date: new Date().toISOString().split('T')[0],
      method: withdrawalMethod,
      reference: `WD${Math.floor(Math.random() * 1000000)}`
    };
    
    setWithdrawalHistory([newWithdrawal, ...withdrawalHistory]);
    setTotalPages(Math.ceil((withdrawalHistory.length + 1) / 10) || 1);
    
    // Update available balance
    setWithdrawalData({
      ...withdrawalData,
      availableBalance: withdrawalData.availableBalance - amount,
      pendingBalance: withdrawalData.pendingBalance + amount
    });
    
    // Close the form
    setIsRequestingWithdrawal(false);
  };

  // Handle cancel withdrawal
  const handleCancelWithdrawal = () => {
    setIsRequestingWithdrawal(false);
    setError('');
  };

  // Get paginated history
  const paginatedHistory = withdrawalHistory.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Earnings Management</div>
          <h1 className="welcome-title">
            Withdrawal <span style={{ color: '#25D366' }}>Requests</span>
          </h1>
          <p className="welcome-subtitle">
            Request withdrawals of your earnings and track the status of your previous withdrawal requests.
          </p>
        </div>
      </div>

      {loading ? (
        <div className="stats-section">
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading withdrawal data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Balance Summary */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Balance Summary</h2>
              <button
                className="btn btn-primary"
                onClick={handleWithdrawalRequest}
                disabled={withdrawalData?.availableBalance < withdrawalData?.minimumWithdrawal}
              >
                <BanknotesIcon style={{ width: '16px', height: '16px' }} />
                Request Withdrawal
              </button>
            </div>

            {withdrawalData && (
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">Available for Withdrawal</p>
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(withdrawalData.availableBalance)}</p>
                    {withdrawalData.availableBalance < withdrawalData.minimumWithdrawal ? (
                      <p className="text-xs text-red-500 mt-2">
                        Minimum withdrawal: {formatCurrency(withdrawalData.minimumWithdrawal)}
                      </p>
                    ) : (
                      <button
                        onClick={handleWithdrawalRequest}
                        className="text-sm text-green-600 mt-2 hover:underline"
                      >
                        Request Withdrawal
                      </button>
                    )}
                  </div>
                  <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">Pending Clearance</p>
                    <p className="text-2xl font-bold text-blue-600">{formatCurrency(withdrawalData.pendingBalance)}</p>
                    <div className="flex items-center text-xs text-gray-500 mt-2">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      <span>7-day holding period</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500 mb-1">Next Payout Date</p>
                    <p className="text-2xl font-bold text-purple-600">{formatDate(withdrawalData.nextPayoutDate)}</p>
                    <p className="text-xs text-gray-500 mt-2">
                      Withdrawal fee: {withdrawalData.withdrawalFee > 0 ? formatCurrency(withdrawalData.withdrawalFee) : 'Free'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Withdrawal Request Form */}
          {isRequestingWithdrawal && (
            <div className="stats-section">
              <div className="section-header">
                <h2 className="section-title">Request Withdrawal</h2>
              </div>

              <div className="card" style={{ padding: '24px' }}>
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
                    {error}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Withdrawal Amount
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">₹</span>
                      </div>
                      <input
                        type="number"
                        name="amount"
                        value={withdrawalAmount}
                        onChange={handleAmountChange}
                        className="focus:ring-green-500 focus:border-green-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                        placeholder="0.00"
                        min={withdrawalData?.minimumWithdrawal}
                        max={withdrawalData?.availableBalance}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">INR</span>
                      </div>
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Available balance: {formatCurrency(withdrawalData?.availableBalance)}
                    </p>
                    <p className="mt-1 text-xs text-gray-500">
                      Minimum withdrawal: {formatCurrency(withdrawalData?.minimumWithdrawal)}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Payment Method
                    </label>
                    <select
                      name="method"
                      value={withdrawalMethod}
                      onChange={handleMethodChange}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm rounded-md"
                    >
                      {withdrawalData?.paymentMethods.map((method) => (
                        <option key={method.id} value={method.id}>
                          {method.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {withdrawalMethod === 'bank_transfer' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Account Number
                        </label>
                        <input
                          type="text"
                          name="accountNumber"
                          value={paymentDetails.accountNumber}
                          onChange={handlePaymentDetailsChange}
                          className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          IFSC Code
                        </label>
                        <input
                          type="text"
                          name="ifscCode"
                          value={paymentDetails.ifscCode}
                          onChange={handlePaymentDetailsChange}
                          className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Account Holder Name
                        </label>
                        <input
                          type="text"
                          name="accountName"
                          value={paymentDetails.accountName}
                          onChange={handlePaymentDetailsChange}
                          className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Bank Name
                        </label>
                        <input
                          type="text"
                          name="bankName"
                          value={paymentDetails.bankName}
                          onChange={handlePaymentDetailsChange}
                          className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </>
                  )}

                  {withdrawalMethod === 'upi' && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        UPI ID
                      </label>
                      <input
                        type="text"
                        name="upiId"
                        value={paymentDetails.upiId}
                        onChange={handlePaymentDetailsChange}
                        className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        placeholder="example@upi"
                      />
                    </div>
                  )}

                  {withdrawalMethod === 'paytm' && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Paytm Number
                      </label>
                      <input
                        type="text"
                        name="paytmNumber"
                        value={paymentDetails.paytmNumber}
                        onChange={handlePaymentDetailsChange}
                        className="mt-1 focus:ring-green-500 focus:border-green-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                        placeholder="10-digit mobile number"
                      />
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleCancelWithdrawal}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSubmitWithdrawal}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                  >
                    Submit Request
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Withdrawal History */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Withdrawal History</h2>
              <button
                className="btn btn-outline"
                onClick={() => {
                  setLoading(true);
                  setTimeout(() => setLoading(false), 500);
                }}
              >
                <ArrowPathIcon className="h-5 w-5" />
                Refresh
              </button>
            </div>

            {withdrawalHistory.length === 0 ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>No withdrawal history found.</p>
              </div>
            ) : (
              <div className="card" style={{ padding: '0' }}>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Reference</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Date</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Amount</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Method</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedHistory.map((withdrawal) => {
                      const statusBadge = getStatusBadge(withdrawal.status);
                      const StatusIcon = statusBadge.icon;
                      return (
                        <tr key={withdrawal.id} className="border-b hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="font-medium text-gray-900">{withdrawal.reference}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-500">{formatDate(withdrawal.date)}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900">{formatCurrency(withdrawal.amount)}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-500">{getPaymentMethodName(withdrawal.method)}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color} flex items-center`}>
                                <StatusIcon className="h-4 w-4 mr-1" />
                                {statusBadge.text}
                              </span>
                              {withdrawal.status === 'rejected' && withdrawal.rejectionReason && (
                                <span className="ml-2 text-xs text-red-500" title={withdrawal.rejectionReason}>
                                  <ExclamationCircleIcon className="h-4 w-4" />
                                </span>
                              )}
                            </div>
                            {withdrawal.status === 'rejected' && withdrawal.rejectionReason && (
                              <div className="text-xs text-red-500 mt-1">{withdrawal.rejectionReason}</div>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">{Math.min(currentPage * 10, withdrawalHistory.length)}</span> of{' '}
                        <span className="font-medium">{withdrawalHistory.length}</span> withdrawals
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Withdrawal Information */}
          <div className="stats-section">
            <div className="section-header">
              <h2 className="section-title">Withdrawal Information</h2>
            </div>

            <div className="card" style={{ padding: '24px' }}>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Withdrawal Process</h3>
                  <p className="text-sm text-gray-500">
                    Withdrawals are processed within 1-3 business days. Once approved, the funds will be transferred to your specified payment method.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Minimum Withdrawal</h3>
                  <p className="text-sm text-gray-500">
                    The minimum withdrawal amount is {formatCurrency(withdrawalData?.minimumWithdrawal || 100)}. You must have at least this amount in your available balance to request a withdrawal.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Holding Period</h3>
                  <p className="text-sm text-gray-500">
                    Earnings from sales are subject to a 7-day holding period before they become available for withdrawal. This is to allow for potential refunds or disputes.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Methods</h3>
                  <p className="text-sm text-gray-500">
                    We currently support the following payment methods: Bank Transfer, UPI, and Paytm. Please ensure your payment details are accurate to avoid delays.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
