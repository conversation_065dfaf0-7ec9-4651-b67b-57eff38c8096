import { useState, useEffect } from 'react';
import {
  GlobeAltIcon,
  MapPinIcon,
  BuildingStorefrontIcon,
  UsersIcon,
  ArrowUpIcon,
  DocumentArrowDownIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorMarketExpansion() {
  const [loading, setLoading] = useState(true);
  const [expansionData, setExpansionData] = useState({
    currentMarkets: {
      totalCities: 0,
      activeCities: []
    },
    expansionPlans: {
      plannedCities: 0,
      upcomingExpansions: []
    },
    marketPenetration: {
      overall: 0,
      byCity: []
    },
    expansionStrategy: {
      phases: [],
      keyFactors: []
    },
    marketSizing: {
      totalAddressableMarket: 0,
      servicableAddressableMarket: 0,
      servicableObtainableMarket: 0,
      currentMarketShare: 0,
      targetMarketShare: {
        oneYear: 0,
        threeYear: 0,
        fiveYear: 0
      }
    }
  });

  // Fetch market expansion data
  useEffect(() => {
    const fetchExpansionData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          currentMarkets: {
            totalCities: 12,
            activeCities: [
              { name: 'Mumbai', vendors: 32, revenue: 31250, growth: 15 },
              { name: 'Delhi', vendors: 28, revenue: 27500, growth: 12 },
              { name: 'Bangalore', vendors: 22, revenue: 21875, growth: 18 },
              { name: 'Hyderabad', vendors: 15, revenue: 15000, growth: 20 },
              { name: 'Chennai', vendors: 12, revenue: 12500, growth: 14 },
              { name: 'Kolkata', vendors: 10, revenue: 10000, growth: 10 },
              { name: 'Pune', vendors: 9, revenue: 6875, growth: 16 },
              { name: 'Ahmedabad', vendors: 8, revenue: 6250, growth: 15 },
              { name: 'Jaipur', vendors: 6, revenue: 4500, growth: 12 },
              { name: 'Lucknow', vendors: 5, revenue: 3750, growth: 10 },
              { name: 'Chandigarh', vendors: 4, revenue: 3000, growth: 8 },
              { name: 'Indore', vendors: 3, revenue: 2500, growth: 9 }
            ]
          },
          expansionPlans: {
            plannedCities: 8,
            upcomingExpansions: [
              { name: 'Surat', targetDate: '2023-09-30', potentialVendors: 15, estimatedRevenue: 12000 },
              { name: 'Bhopal', targetDate: '2023-10-31', potentialVendors: 12, estimatedRevenue: 9500 },
              { name: 'Nagpur', targetDate: '2023-11-30', potentialVendors: 10, estimatedRevenue: 8000 },
              { name: 'Kochi', targetDate: '2023-12-31', potentialVendors: 14, estimatedRevenue: 11000 },
              { name: 'Coimbatore', targetDate: '2024-01-31', potentialVendors: 11, estimatedRevenue: 8500 },
              { name: 'Visakhapatnam', targetDate: '2024-02-28', potentialVendors: 9, estimatedRevenue: 7000 },
              { name: 'Guwahati', targetDate: '2024-03-31', potentialVendors: 8, estimatedRevenue: 6500 },
              { name: 'Bhubaneswar', targetDate: '2024-04-30', potentialVendors: 7, estimatedRevenue: 5500 }
            ]
          },
          marketPenetration: {
            overall: 1.38,
            byCity: [
              { name: 'Mumbai', penetration: 2.1, potential: 8.5 },
              { name: 'Delhi', penetration: 1.8, potential: 7.5 },
              { name: 'Bangalore', penetration: 2.3, potential: 9.0 },
              { name: 'Hyderabad', penetration: 1.5, potential: 6.5 },
              { name: 'Chennai', penetration: 1.2, potential: 5.8 },
              { name: 'Kolkata', penetration: 0.9, potential: 5.0 },
              { name: 'Pune', penetration: 1.4, potential: 6.0 },
              { name: 'Ahmedabad', penetration: 1.1, potential: 5.5 }
            ]
          },
          expansionStrategy: {
            phases: [
              {
                name: 'Phase 1: Tier 1 Cities',
                status: 'Completed',
                description: 'Establish presence in major metropolitan areas with high digital adoption.',
                cities: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata']
              },
              {
                name: 'Phase 2: Tier 2 Cities',
                status: 'In Progress',
                description: 'Expand to secondary cities with growing digital commerce adoption.',
                cities: ['Pune', 'Ahmedabad', 'Jaipur', 'Lucknow', 'Chandigarh', 'Indore', 'Surat', 'Bhopal']
              },
              {
                name: 'Phase 3: Regional Hubs',
                status: 'Planned',
                description: 'Target regional economic centers to create network effects.',
                cities: ['Nagpur', 'Kochi', 'Coimbatore', 'Visakhapatnam', 'Guwahati', 'Bhubaneswar']
              },
              {
                name: 'Phase 4: Tier 3 Cities',
                status: 'Future',
                description: 'Penetrate smaller cities with emerging digital potential.',
                cities: ['Dehradun', 'Mysore', 'Thiruvananthapuram', 'Jodhpur', 'Mangalore', 'Jammu']
              }
            ],
            keyFactors: [
              'Digital adoption rate',
              'Smartphone penetration',
              'Local business density',
              'Competitive landscape',
              'Logistics infrastructure',
              'Internet connectivity'
            ]
          },
          marketSizing: {
            totalAddressableMarket: 500000000,
            servicableAddressableMarket: 150000000,
            servicableObtainableMarket: 50000000,
            currentMarketShare: 1.38,
            targetMarketShare: {
              oneYear: 2.5,
              threeYear: 5.0,
              fiveYear: 10.0
            }
          }
        };

        setExpansionData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching expansion data:', error);
        setLoading(false);
      }
    };

    fetchExpansionData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Geographic Expansion</div>
          <h1 className="welcome-title">
            Market <span style={{ color: '#25D366' }}>Expansion</span>
          </h1>
          <p className="welcome-subtitle">
            Track current market presence and view upcoming expansion plans.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Export Controls */}
        <div className="flex justify-end mb-6">
          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Report
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading market expansion data...</p>
          </div>
        ) : (
          <>
            {/* Market Overview */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                  <GlobeAltIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Active Cities</h3>
                  <p className="stat-value">{expansionData.currentMarkets.totalCities}</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                  <MapPinIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Planned Expansion</h3>
                  <p className="stat-value">{expansionData.expansionPlans.plannedCities}</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                  <BuildingStorefrontIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Market Penetration</h3>
                  <p className="stat-value">{formatPercentage(expansionData.marketPenetration.overall)}</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                  <UsersIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Total Vendors</h3>
                  <p className="stat-value">
                    {expansionData.currentMarkets.activeCities.reduce((acc, city) => acc + city.vendors, 0)}
                  </p>
                </div>
              </div>
            </div>

            {/* Current Markets */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Current Markets</h2>
              </div>
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          City
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vendors
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Monthly Revenue
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Growth Rate
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Market Penetration
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {expansionData.currentMarkets.activeCities.map((city) => {
                        const penetrationData = expansionData.marketPenetration.byCity.find(c => c.name === city.name);

                        return (
                          <tr key={city.name}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {city.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {city.vendors}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatCurrency(city.revenue)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                                <span className="text-green-500">{formatPercentage(city.growth)}</span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {penetrationData ? formatPercentage(penetrationData.penetration) : 'N/A'}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Upcoming Expansions */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Upcoming Expansions</h2>
              </div>
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          City
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Target Launch Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Potential Vendors
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Estimated Revenue
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {expansionData.expansionPlans.upcomingExpansions.map((city) => {
                        const today = new Date();
                        const targetDate = new Date(city.targetDate);
                        const daysRemaining = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));

                        let status;
                        if (daysRemaining <= 30) {
                          status = { label: 'Imminent', color: 'bg-green-100 text-green-800' };
                        } else if (daysRemaining <= 90) {
                          status = { label: 'Upcoming', color: 'bg-blue-100 text-blue-800' };
                        } else {
                          status = { label: 'Planned', color: 'bg-gray-100 text-gray-800' };
                        }

                        return (
                          <tr key={city.name}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {city.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(city.targetDate)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {city.potentialVendors}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatCurrency(city.estimatedRevenue)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${status.color}`}>
                                {status.label}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Market Penetration */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Market Penetration</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Current vs Potential Penetration</h3>
                  <div className="space-y-4">
                    {expansionData.marketPenetration.byCity.map((city) => (
                      <div key={city.name} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-900">{city.name}</span>
                          <span className="text-sm text-gray-500">
                            {formatPercentage(city.penetration)} / {formatPercentage(city.potential)}
                          </span>
                        </div>
                        <div className="relative pt-1">
                          <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                            <div
                              className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
                              style={{ width: `${(city.penetration / city.potential) * 100}%` }}
                            ></div>
                          </div>
                          <div className="absolute top-0 left-0 h-2 border-r-2 border-dashed border-blue-500" style={{ width: `${city.potential}%` }}></div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex items-center justify-end">
                    <div className="flex items-center mr-4">
                      <div className="w-3 h-3 bg-green-500 mr-2"></div>
                      <span className="text-xs text-gray-600">Current Penetration</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 border-r-2 border-dashed border-blue-500 mr-2"></div>
                      <span className="text-xs text-gray-600">Potential Penetration</span>
                    </div>
                  </div>
                </div>

                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Market Sizing</h3>
                  <div className="space-y-6">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Total Addressable Market (TAM)</p>
                      <p className="text-xl font-bold text-gray-900">{formatCurrency(expansionData.marketSizing.totalAddressableMarket)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Serviceable Addressable Market (SAM)</p>
                      <p className="text-xl font-bold text-gray-900">{formatCurrency(expansionData.marketSizing.servicableAddressableMarket)}</p>
                      <div className="relative pt-1">
                        <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                          <div
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"
                            style={{ width: `${(expansionData.marketSizing.servicableAddressableMarket / expansionData.marketSizing.totalAddressableMarket) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatPercentage(Math.round((expansionData.marketSizing.servicableAddressableMarket / expansionData.marketSizing.totalAddressableMarket) * 100))} of TAM
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Serviceable Obtainable Market (SOM)</p>
                      <p className="text-xl font-bold text-gray-900">{formatCurrency(expansionData.marketSizing.servicableObtainableMarket)}</p>
                      <div className="relative pt-1">
                        <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                          <div
                            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
                            style={{ width: `${(expansionData.marketSizing.servicableObtainableMarket / expansionData.marketSizing.servicableAddressableMarket) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatPercentage(Math.round((expansionData.marketSizing.servicableObtainableMarket / expansionData.marketSizing.servicableAddressableMarket) * 100))} of SAM
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Expansion Strategy */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Expansion Strategy</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Phased Expansion Plan</h3>
                    <div className="space-y-6">
                      {expansionData.expansionStrategy.phases.map((phase, index) => (
                        <div key={index} className="border-l-4 pl-4" style={{ borderColor: phase.status === 'Completed' ? '#25D366' : phase.status === 'In Progress' ? '#3B82F6' : '#9CA3AF' }}>
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="text-md font-medium text-gray-900">{phase.name}</h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              phase.status === 'Completed'
                                ? 'bg-green-100 text-green-800'
                                : phase.status === 'In Progress'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-800'
                            }`}>
                              {phase.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{phase.description}</p>
                          <div className="flex flex-wrap gap-2">
                            {phase.cities.map((city, cityIndex) => (
                              <span key={cityIndex} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {city}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Key Expansion Factors</h3>
                    <ul className="space-y-2">
                      {expansionData.expansionStrategy.keyFactors.map((factor, index) => (
                        <li key={index} className="flex items-start">
                          <div className="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                            <span className="text-green-600 text-xs font-bold">{index + 1}</span>
                          </div>
                          <span className="text-gray-700">{factor}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="card mt-6" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Target Market Share</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-gray-500">Current</span>
                          <span className="text-sm font-medium text-gray-900">{formatPercentage(expansionData.marketSizing.currentMarketShare)}</span>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${expansionData.marketSizing.currentMarketShare}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-gray-500">1 Year Target</span>
                          <span className="text-sm font-medium text-gray-900">{formatPercentage(expansionData.marketSizing.targetMarketShare.oneYear)}</span>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 rounded-full"
                            style={{ width: `${expansionData.marketSizing.targetMarketShare.oneYear}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-gray-500">3 Year Target</span>
                          <span className="text-sm font-medium text-gray-900">{formatPercentage(expansionData.marketSizing.targetMarketShare.threeYear)}</span>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 rounded-full"
                            style={{ width: `${expansionData.marketSizing.targetMarketShare.threeYear}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm text-gray-500">5 Year Target</span>
                          <span className="text-sm font-medium text-gray-900">{formatPercentage(expansionData.marketSizing.targetMarketShare.fiveYear)}</span>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 rounded-full"
                            style={{ width: `${expansionData.marketSizing.targetMarketShare.fiveYear}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
