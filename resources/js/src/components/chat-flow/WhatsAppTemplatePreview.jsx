import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import '../store-theme/styles/StoreTheme.css';
import '../store-theme/styles/background.css';
import './styles/MessageTemplate.css';

/**
 * WhatsAppTemplatePreview component
 * Shows a preview of how a message template will look in WhatsApp
 */
const WhatsAppTemplatePreview = ({ isOpen, onClose, message }) => {
  if (!isOpen) return null;

  // Format time as HH:MM AM/PM
  const formatTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Get button icon based on type
  const getButtonIcon = (type) => {
    switch (type) {
      case 'quick_reply':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
        );
      case 'url':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
            <polyline points="15 3 21 3 21 9"></polyline>
            <line x1="10" y1="14" x2="21" y2="3"></line>
          </svg>
        );
      case 'call':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="mobile-frame">
        <div className="phone-content">
          {/* WhatsApp-style header */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            padding: '12px 12px',
            backgroundColor: 'white',
            color: 'black',
            height: '64px',
            zIndex: 10,
            position: 'relative',
            overflow: 'hidden',
            borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            {/* Store info */}
            <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: '#f0f0f0',
                marginRight: '8px',
                overflow: 'hidden'
              }}>
                <img
                  src="/logo192.png"
                  alt="Store logo"
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                />
              </div>
              <div style={{ fontWeight: 600, fontSize: '16px' }}>
                Store Name
                <img
                  src="/verified-badge.webp"
                  alt="Verified"
                  style={{
                    width: '16px',
                    height: '16px',
                    marginLeft: '6px',
                    display: 'inline-block',
                    verticalAlign: 'middle'
                  }}
                />
              </div>
            </div>
          </div>

          {/* Chat body with background */}
          <div className="flex-1 overflow-y-auto relative">
            {/* WhatsApp doodle background */}
            <div className="absolute inset-0 bg-whatsapp-pattern opacity-15"></div>

            {/* Overlay */}
            <div className="absolute inset-0 bg-[#E6DEB2] opacity-15"></div>

            {/* Message content */}
            <div className="p-4 relative z-10">
              {/* Message bubble */}
              <div className="chat-message incoming">
                <div className="message-bubble with-buttons" style={{ borderTopLeftRadius: 0, position: 'relative', overflow: 'visible' }}>
                  {/* Explicit message tail */}
                  <div style={{
                    content: '',
                    position: 'absolute',
                    top: 0,
                    left: -12,
                    width: 20,
                    height: 20,
                    backgroundColor: 'white',
                    clipPath: 'polygon(100% 0, 0 0, 100% 100%)',
                    zIndex: 0
                  }}></div>
                  {/* Header */}
                  {(message?.header || null) && (
                    <div className="message-header">
                      {message.header || 'PRODUCT ANNOUNCEMENT'}
                    </div>
                  )}

                  {/* Image */}
                  {(message?.image || null) && (
                    <div className="message-image">
                      <img
                        src={(() => {
//                           // Removed console.log

                          // Direct string handling
                          if (typeof message.image === 'string') {
                            if (message.image.startsWith('data:image/')) {
//                               // Removed console.log
                              return message.image;
                            }
                            return message.image;
                          }

                          // Handle File or Blob
                          if (message.image instanceof Blob || message.image instanceof File) {
//                             // Removed console.log
                            return URL.createObjectURL(message.image);
                          }

                          // Object handling
                          if (typeof message.image === 'object') {
//                             // Removed console.log

                            // Try to find any property that might contain a data URL
                            for (const key of Object.keys(message.image)) {
                              if (typeof message.image[key] === 'string' && message.image[key].startsWith('data:image/')) {
//                                 // Removed console.log
                                return message.image[key];
                              }
                            }

                            // Check common properties
                            if (message.image.data && typeof message.image.data === 'string') {
//                               // Removed console.log
                              return message.image.data;
                            }

                            if (message.image.url && typeof message.image.url === 'string') {
//                               // Removed console.log
                              return message.image.url;
                            }

                            if (message.image.src && typeof message.image.src === 'string') {
//                               // Removed console.log
                              return message.image.src;
                            }

                            // Try to extract from JSON string
                            try {
                              const jsonString = JSON.stringify(message.image);
//                               // Removed console.log

                              // Look for data:image pattern in the JSON string
                              const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                              if (dataUrlMatch && dataUrlMatch[1]) {
//                                 // Removed console.log
                                return dataUrlMatch[1];
                              }

                              // Try to find any data URL in the string
                              const allDataUrlMatches = jsonString.match(/data:image\/[^"]+/g);
                              if (allDataUrlMatches && allDataUrlMatches.length > 0) {
//                                 // Removed console.log
                                return allDataUrlMatches[0];
                              }
                            } catch (err) {
//                               // Removed console.error
                            }
                          }

                          // Fallback to placeholder
                          return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
                        })()}
                        alt="Message attachment"
                        className="message-img"
                        style={{ borderRadius: '4px' }}
                        onError={(e) => {
//                           // Removed console.error
                          e.target.onerror = null;

                          // Last resort: try to directly extract the image data
                          if (typeof message.image === 'object') {
                            try {
                              // Try all possible properties that might contain the image data
                              const props = Object.keys(message.image);
//                               // Removed console.log

                              for (const prop of props) {
                                const value = message.image[prop];
                                if (typeof value === 'string' && value.startsWith('data:image/')) {
//                                   // Removed console.log
                                  e.target.src = value;
                                  return;
                                }
                              }

                              // Try to extract from JSON string with a more flexible approach
                              const jsonString = JSON.stringify(message.image);
                              const dataUrlRegex = /data:image\/[^"',}\]]+/g;
                              const matches = jsonString.match(dataUrlRegex);

                              if (matches && matches.length > 0) {
//                                 // Removed console.log
                                e.target.src = matches[0];
                                return;
                              }
                            } catch (err) {
//                               // Removed console.error
                            }
                          }

                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4=';
                        }}
                        onLoad={() => {}}
                      />
                    </div>
                  )}

                  {/* Message content */}
                  <div className="message-content">
                    {message?.content || 'Welcome to our store! We offer various products and services.'}
                  </div>

                  {/* Product Catalog Preview (if applicable) */}
                  {message?.isProductCatalog && (
                    <div className="product-catalog-preview mt-2">
                      <div className="flex items-center justify-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                        <span className="text-sm font-medium">Product Catalog</span>
                      </div>

                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-2">
                        <div className="text-xs text-blue-600 mb-2 font-medium">
                          Category: {message.categoryFilter === 'all' ? 'All Products' : message.categoryFilter}
                        </div>
                        <div className="text-xs text-green-600 mb-2">
                          Products found: 4
                        </div>
                        <div className="text-xs text-center text-gray-500">
                          Your products will be displayed here in the actual chat.
                        </div>
                      </div>

                      <div className="text-xs text-center mt-2 text-gray-500 italic">
                        (Customers can select products to view details)
                      </div>
                    </div>
                  )}

                  {/* Footer */}
                  {(message?.footer || null) && (
                    <div className="message-footer">
                      {message.footer || 'Terms and conditions apply'}
                    </div>
                  )}

                  {/* CTA Buttons */}
                  {(message?.buttons || [
                    { type: 'quick_reply', text: 'Browse Products' },
                    { type: 'url', text: 'Visit Our Website' }
                  ]).map((button, index) => (
                    <React.Fragment key={index}>
                      <div className="message-divider" style={{ height: '0.5px', backgroundColor: 'rgba(0, 0, 0, 0.08)' }}></div>
                      <div className="message-button" style={{ textAlign: 'center' }}>
                        <div className="button-content" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                          {getButtonIcon(button.type)}
                          <span>{button.text}</span>
                        </div>
                      </div>
                    </React.Fragment>
                  ))}

                  {/* Message metadata */}
                  <div className="message-metadata">
                    <span className="message-time">{formatTime()}</span>
                    <span className="message-status">
                      <div className="double-check read">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-3 h-3 status-icon first-check" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="20 6 9 17 4 12"></polyline>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-3 h-3 status-icon second-check" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="20 6 9 17 4 12"></polyline>
                        </svg>
                      </div>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md"
      >
        <XMarkIcon className="h-6 w-6 text-gray-500" />
      </button>
    </div>
  );
};

export default WhatsAppTemplatePreview;
