<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\PaymentVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PaymentVerificationController extends Controller
{
    /**
     * Get payment verification history for an order.
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $order = Order::findOrFail($request->order_id);
        $this->authorize('view', $order);

        $verifications = PaymentVerification::where('order_id', $order->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json(['paymentVerifications' => $verifications]);
    }

    /**
     * Create a new payment verification request (customer submits proof).
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,id',
            'amount' => 'required|numeric',
            'payment_method' => 'required|string',
            'reference_id' => 'nullable|string',
            'customer_notes' => 'nullable|string',
            'payment_proof' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $order = Order::findOrFail($request->input('order_id'));
        // A customer can submit proof for an order they can update.
        $this->authorize('update', $order);

        $path = null;
        if ($request->hasFile('payment_proof')) {
            $path = $request->file('payment_proof')->store('payment_proofs', 'public');
        }

        $verification = PaymentVerification::create([
            'order_id' => $order->id,
            'amount' => $request->input('amount'),
            'payment_method' => $request->input('payment_method'),
            'reference_id' => $request->input('reference_id'),
            'customer_notes' => $request->input('customer_notes'),
            'payment_proof_url' => $path ? Storage::url($path) : null,
            'status' => 'pending',
        ]);

        $order->update(['payment_status' => 'pending_verification']);

        return response()->json([
            'message' => 'Payment verification request created successfully',
            'paymentVerification' => $verification
        ], 201);
    }

    /**
     * Verify or reject a payment (vendor only).
     */
    public function update(Request $request, PaymentVerification $paymentVerification)
    {
        $this->authorize('update', $paymentVerification);

        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:verified,rejected',
            'vendor_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $paymentVerification->update([
            'status' => $request->input('status'),
            'vendor_notes' => $request->input('vendor_notes'),
            'verified_by' => Auth::id(),
            'verified_at' => now(),
        ]);

        $order = $paymentVerification->order;
        if ($request->input('status') === 'verified') {
            $order->update(['payment_status' => 'paid']);
        } else if ($request->input('status') === 'rejected') {
            $order->update(['payment_status' => 'failed']);
        }

        return response()->json([
            'message' => 'Payment ' . $request->input('status') . ' successfully',
            'paymentVerification' => $paymentVerification
        ]);
    }
}
