import { Link } from 'react-router-dom';

/**
 * Reusable Button component that can be rendered as a button or Link
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Button variant (primary, secondary, outline, text)
 * @param {string} props.size - Button size (sm, md, lg)
 * @param {string} props.to - If provided, renders as Link to this path
 * @param {boolean} props.fullWidth - Whether button should take full width
 * @param {React.ReactNode} props.children - Button content
 * @param {Object} props.rest - Additional props passed to button/Link
 */
export default function Button({ 
  variant = 'primary', 
  size = 'md', 
  to, 
  fullWidth = false,
  children, 
  ...rest 
}) {
  // Base classes for all button variants
  const baseClasses = "inline-flex items-center justify-center font-medium rounded-md focus:outline-none transition-colors";
  
  // Size classes
  const sizeClasses = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-2.5 text-base",
    lg: "px-6 py-3 text-lg"
  };
  
  // Variant classes
  const variantClasses = {
    primary: "bg-whatsapp-default text-white hover:bg-whatsapp-dark shadow-sm",
    secondary: "bg-whatsapp-teal text-white hover:bg-opacity-90 shadow-sm",
    outline: "border border-whatsapp-default text-whatsapp-default hover:bg-whatsapp-light hover:bg-opacity-20",
    text: "text-whatsapp-default hover:bg-whatsapp-light hover:bg-opacity-20",
    white: "bg-white text-whatsapp-teal hover:bg-gray-50 shadow-sm"
  };
  
  // Width classes
  const widthClasses = fullWidth ? "w-full" : "";
  
  // Combine all classes
  const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClasses}`;
  
  // Render as Link if 'to' prop is provided, otherwise as button
  if (to) {
    return (
      <Link to={to} className={classes} {...rest}>
        {children}
      </Link>
    );
  }
  
  return (
    <button className={classes} {...rest}>
      {children}
    </button>
  );
}
