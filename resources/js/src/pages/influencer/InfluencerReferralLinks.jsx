import { useState, useEffect } from 'react';
import { 
  LinkIcon,
  ArrowPathIcon,
  PlusIcon,
  XMarkIcon,
  CheckIcon,
  ClipboardIcon,
  QrCodeIcon,
  ShareIcon,
  TrashIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerReferralLinks() {
  const [loading, setLoading] = useState(false);
  const [referralLinks, setReferralLinks] = useState([]);
  const [isCreatingLink, setIsCreatingLink] = useState(false);
  const [isEditingLink, setIsEditingLink] = useState(false);
  const [selectedLink, setSelectedLink] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    platform: '',
    customSlug: '',
    utmSource: '',
    utmMedium: '',
    utmCampaign: ''
  });
  const [error, setError] = useState('');
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeLink, setQRCodeLink] = useState('');

  // Fetch referral links
  useEffect(() => {
    const fetchReferralLinks = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockLinks = [
          {
            id: 1,
            name: 'Instagram Bio Link',
            platform: 'Instagram',
            url: 'whamart.com/ref/rahul123',
            customSlug: 'rahul123',
            utmSource: 'instagram',
            utmMedium: 'social',
            utmCampaign: 'bio',
            fullUrl: 'whamart.com/ref/rahul123?utm_source=instagram&utm_medium=social&utm_campaign=bio',
            clicks: 156,
            signups: 42,
            sales: 18,
            createdAt: '2023-09-15'
          },
          {
            id: 2,
            name: 'Facebook Post',
            platform: 'Facebook',
            url: 'whamart.com/ref/rahul-fb',
            customSlug: 'rahul-fb',
            utmSource: 'facebook',
            utmMedium: 'social',
            utmCampaign: 'post',
            fullUrl: 'whamart.com/ref/rahul-fb?utm_source=facebook&utm_medium=social&utm_campaign=post',
            clicks: 89,
            signups: 24,
            sales: 10,
            createdAt: '2023-09-20'
          },
          {
            id: 3,
            name: 'WhatsApp Status',
            platform: 'WhatsApp',
            url: 'whamart.com/ref/rahul-wa',
            customSlug: 'rahul-wa',
            utmSource: 'whatsapp',
            utmMedium: 'social',
            utmCampaign: 'status',
            fullUrl: 'whamart.com/ref/rahul-wa?utm_source=whatsapp&utm_medium=social&utm_campaign=status',
            clicks: 65,
            signups: 18,
            sales: 8,
            createdAt: '2023-09-25'
          },
          {
            id: 4,
            name: 'YouTube Description',
            platform: 'YouTube',
            url: 'whamart.com/ref/rahul-yt',
            customSlug: 'rahul-yt',
            utmSource: 'youtube',
            utmMedium: 'video',
            utmCampaign: 'description',
            fullUrl: 'whamart.com/ref/rahul-yt?utm_source=youtube&utm_medium=video&utm_campaign=description',
            clicks: 42,
            signups: 12,
            sales: 5,
            createdAt: '2023-10-01'
          },
          {
            id: 5,
            name: 'Twitter Post',
            platform: 'Twitter',
            url: 'whamart.com/ref/rahul-tw',
            customSlug: 'rahul-tw',
            utmSource: 'twitter',
            utmMedium: 'social',
            utmCampaign: 'tweet',
            fullUrl: 'whamart.com/ref/rahul-tw?utm_source=twitter&utm_medium=social&utm_campaign=tweet',
            clicks: 28,
            signups: 8,
            sales: 3,
            createdAt: '2023-10-05'
          }
        ];
        
        setReferralLinks(mockLinks);
      } catch (error) {
        console.error('Error fetching referral links:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReferralLinks();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Auto-generate custom slug from name if not manually set
    if (name === 'name' && !formData.customSlug) {
      const slug = value.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
      setFormData(prev => ({
        ...prev,
        customSlug: slug
      }));
    }
  };

  // Handle create link
  const handleCreateLink = () => {
    setIsCreatingLink(true);
    setIsEditingLink(false);
    setSelectedLink(null);
    setError('');
    setFormData({
      name: '',
      platform: '',
      customSlug: '',
      utmSource: '',
      utmMedium: '',
      utmCampaign: ''
    });
  };

  // Handle edit link
  const handleEditLink = (link) => {
    setIsCreatingLink(false);
    setIsEditingLink(true);
    setSelectedLink(link);
    setError('');
    setFormData({
      name: link.name,
      platform: link.platform,
      customSlug: link.customSlug,
      utmSource: link.utmSource,
      utmMedium: link.utmMedium,
      utmCampaign: link.utmCampaign
    });
  };

  // Handle delete link
  const handleDeleteLink = (linkId) => {
    if (window.confirm('Are you sure you want to delete this referral link?')) {
      // In a real implementation, this would be an API call
      const updatedLinks = referralLinks.filter(link => link.id !== linkId);
      setReferralLinks(updatedLinks);
    }
  };

  // Handle save link
  const handleSaveLink = () => {
    // Validate form
    if (!formData.name) {
      setError('Link name is required');
      return;
    }

    if (!formData.platform) {
      setError('Platform is required');
      return;
    }

    if (!formData.customSlug) {
      setError('Custom slug is required');
      return;
    }

    // Check if slug is unique
    const slugExists = referralLinks.some(link => 
      link.customSlug === formData.customSlug && 
      (!selectedLink || link.id !== selectedLink.id)
    );

    if (slugExists) {
      setError('This custom slug is already in use. Please choose another one.');
      return;
    }

    // Generate full URL
    const baseUrl = `whamart.com/ref/${formData.customSlug}`;
    let fullUrl = baseUrl;
    
    const utmParams = [];
    if (formData.utmSource) utmParams.push(`utm_source=${formData.utmSource}`);
    if (formData.utmMedium) utmParams.push(`utm_medium=${formData.utmMedium}`);
    if (formData.utmCampaign) utmParams.push(`utm_campaign=${formData.utmCampaign}`);
    
    if (utmParams.length > 0) {
      fullUrl += `?${utmParams.join('&')}`;
    }

    if (isCreatingLink) {
      // Create new link
      const newLink = {
        id: Date.now(),
        name: formData.name,
        platform: formData.platform,
        url: baseUrl,
        customSlug: formData.customSlug,
        utmSource: formData.utmSource,
        utmMedium: formData.utmMedium,
        utmCampaign: formData.utmCampaign,
        fullUrl: fullUrl,
        clicks: 0,
        signups: 0,
        sales: 0,
        createdAt: new Date().toISOString().split('T')[0]
      };
      
      setReferralLinks([...referralLinks, newLink]);
    } else if (isEditingLink && selectedLink) {
      // Update existing link
      const updatedLinks = referralLinks.map(link => 
        link.id === selectedLink.id ? 
        {
          ...link,
          name: formData.name,
          platform: formData.platform,
          url: baseUrl,
          customSlug: formData.customSlug,
          utmSource: formData.utmSource,
          utmMedium: formData.utmMedium,
          utmCampaign: formData.utmCampaign,
          fullUrl: fullUrl
        } : 
        link
      );
      
      setReferralLinks(updatedLinks);
    }
    
    // Reset form
    setIsCreatingLink(false);
    setIsEditingLink(false);
    setSelectedLink(null);
    setError('');
  };

  // Handle cancel
  const handleCancel = () => {
    setIsCreatingLink(false);
    setIsEditingLink(false);
    setSelectedLink(null);
    setError('');
  };

  // Copy link to clipboard
  const handleCopyLink = (link) => {
    navigator.clipboard.writeText(link);
    alert('Link copied to clipboard!');
  };

  // Show QR code
  const handleShowQRCode = (link) => {
    setQRCodeLink(link);
    setShowQRCode(true);
  };

  // Close QR code modal
  const handleCloseQRCode = () => {
    setShowQRCode(false);
    setQRCodeLink('');
  };

  // Share link
  const handleShareLink = (link) => {
    if (navigator.share) {
      navigator.share({
        title: 'Check out this store on WhaMart',
        text: 'I found this amazing store on WhaMart. Check it out!',
        url: link
      })
      .catch(error => console.log('Error sharing:', error));
    } else {
      handleCopyLink(link);
    }
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Link Management</div>
          <h1 className="welcome-title">
            Referral <span style={{ color: '#25D366' }}>Links</span>
          </h1>
          <p className="welcome-subtitle">
            Create and manage your custom referral links to track performance across different platforms.
          </p>
        </div>
      </div>

      {/* Referral Links Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Your Referral Links</h2>
          <button
            className="btn btn-primary"
            onClick={handleCreateLink}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Create New Link
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading referral links...</p>
          </div>
        ) : (
          <>
            {/* Create/Edit Link Form */}
            {(isCreatingLink || isEditingLink) && (
              <div className="card mb-6" style={{ padding: '24px' }}>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isCreatingLink ? 'Create New Referral Link' : 'Edit Referral Link'}
                </h3>
                
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
                    {error}
                  </div>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Link Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="e.g. Instagram Bio Link"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Platform *
                    </label>
                    <select
                      name="platform"
                      value={formData.platform}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      required
                    >
                      <option value="">Select Platform</option>
                      <option value="Instagram">Instagram</option>
                      <option value="Facebook">Facebook</option>
                      <option value="WhatsApp">WhatsApp</option>
                      <option value="YouTube">YouTube</option>
                      <option value="Twitter">Twitter</option>
                      <option value="Website">Website</option>
                      <option value="Email">Email</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Custom Slug *
                    </label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                        whamart.com/ref/
                      </span>
                      <input
                        type="text"
                        name="customSlug"
                        value={formData.customSlug}
                        onChange={handleInputChange}
                        className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="your-custom-slug"
                        required
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Use only letters, numbers, and hyphens. No spaces or special characters.
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      UTM Source
                    </label>
                    <input
                      type="text"
                      name="utmSource"
                      value={formData.utmSource}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="e.g. instagram"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      UTM Medium
                    </label>
                    <input
                      type="text"
                      name="utmMedium"
                      value={formData.utmMedium}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="e.g. social"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      UTM Campaign
                    </label>
                    <input
                      type="text"
                      name="utmCampaign"
                      value={formData.utmCampaign}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="e.g. summer_sale"
                    />
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveLink}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                  >
                    {isCreatingLink ? 'Create Link' : 'Save Changes'}
                  </button>
                </div>
              </div>
            )}

            {/* Referral Links List */}
            {referralLinks.length === 0 ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>You haven't created any referral links yet.</p>
                <button
                  className="btn btn-primary mt-4"
                  onClick={handleCreateLink}
                >
                  <PlusIcon style={{ width: '16px', height: '16px' }} />
                  Create Your First Link
                </button>
              </div>
            ) : (
              <div className="space-y-6">
                {referralLinks.map((link) => (
                  <div key={link.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                    <div className="flex flex-col md:flex-row gap-6">
                      {/* Link Info */}
                      <div className="md:w-2/3">
                        <div className="flex items-center mb-2">
                          <h3 className="text-lg font-medium text-gray-900">{link.name}</h3>
                          <span className="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                            {link.platform}
                          </span>
                        </div>
                        
                        <div className="mb-4">
                          <div className="flex items-center">
                            <LinkIcon className="h-5 w-5 text-gray-400 mr-1" />
                            <span className="text-sm text-gray-600 mr-2 truncate">{link.fullUrl}</span>
                            <button
                              onClick={() => handleCopyLink(link.fullUrl)}
                              className="text-xs text-green-600 hover:text-green-700"
                              title="Copy Link"
                            >
                              <ClipboardIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="text-xs text-gray-500">
                          Created on {formatDate(link.createdAt)}
                        </div>
                      </div>
                      
                      {/* Link Stats */}
                      <div className="md:w-1/3">
                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div className="bg-gray-50 p-3 rounded-lg text-center">
                            <p className="text-xs text-gray-500">Clicks</p>
                            <p className="text-lg font-bold">{link.clicks}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg text-center">
                            <p className="text-xs text-gray-500">Signups</p>
                            <p className="text-lg font-bold">{link.signups}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg text-center">
                            <p className="text-xs text-gray-500">Sales</p>
                            <p className="text-lg font-bold">{link.sales}</p>
                          </div>
                        </div>
                        
                        <div className="flex justify-between">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEditLink(link)}
                              className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                              title="Edit Link"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleDeleteLink(link.id)}
                              className="p-2 text-red-600 hover:text-red-900 rounded-full hover:bg-gray-100"
                              title="Delete Link"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                          
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleShowQRCode(link.fullUrl)}
                              className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                              title="Generate QR Code"
                            >
                              <QrCodeIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleShareLink(link.fullUrl)}
                              className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                              title="Share Link"
                            >
                              <ShareIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* QR Code Modal */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">QR Code</h3>
              <button
                onClick={handleCloseQRCode}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="bg-white p-4 rounded-lg shadow-md mb-4">
                {/* In a real implementation, this would be a generated QR code */}
                <div className="w-64 h-64 bg-gray-200 flex items-center justify-center">
                  <QrCodeIcon className="h-32 w-32 text-gray-400" />
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-4 text-center">
                Scan this QR code or share it with your audience to track visits to your referral link.
              </p>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    // In a real implementation, this would download the QR code image
                    alert('QR code download would start here in a real implementation.');
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Download QR Code
                </button>
                <button
                  onClick={handleCloseQRCode}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
