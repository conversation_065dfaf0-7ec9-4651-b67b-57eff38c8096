<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Store;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Get analytics overview for the authenticated vendor's store.
     */
    public function getAnalyticsOverview(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for the user.'], 404);
        }

        // Calculate lifetime stats for the dashboard cards
        $totalRevenue = Order::where('store_id', $store->id)
            ->whereIn('status', ['completed', 'processing', 'shipped'])
            ->sum('total_amount');

        $totalCustomers = Customer::where('store_id', $store->id)->count();

        $totalProducts = \App\Models\Product::where('store_id', $store->id)->count();

        $activeChats = \App\Models\ChatFlow::where('store_id', $store->id)->where('is_active', true)->count();

        return response()->json([
            'totalRevenue' => round($totalRevenue, 2),
            'totalCustomers' => $totalCustomers,
            'totalProducts' => $totalProducts,
            'activeChats' => $activeChats,
        ]);
    }

    /**
     * Get monthly sales data for charts.
     */
    public function getMonthlySales(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for the user.'], 404);
        }

        $year = $request->query('year', Carbon::now()->year);

        $monthlySales = Order::where('store_id', $store->id)
            ->where('status', '!=', 'cancelled')
            ->whereYear('created_at', $year)
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as revenue, COUNT(id) as orders')
            ->groupBy('month')
            ->orderBy('month', 'ASC')
            ->get()
            ->keyBy('month');

        $formattedMonthlySales = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthData = $monthlySales->get($i);
            $formattedMonthlySales[] = [
                'month' => Carbon::create()->month($i)->format('M'),
                'revenue' => $monthData ? (float) $monthData->revenue : 0,
                'orders' => $monthData ? (int) $monthData->orders : 0,
            ];
        }

        return response()->json([
            'monthlySales' => $formattedMonthlySales,
            'year' => (int) $year,
        ]);
    }

    /**
     * Get top selling products.
     */
    public function getTopProducts(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for the user.'], 404);
        }

        $timeRange = $request->query('timeRange', 'month');
        [$startDate, $endDate] = $this->getDateRanges($timeRange);

        $topProducts = OrderItem::whereHas('order', function ($query) use ($store, $startDate, $endDate) {
                $query->where('store_id', $store->id)
                    ->where('status', '!=', 'cancelled')
                    ->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->with('product:id,image_url')
            ->selectRaw('product_id, product_name, SUM(quantity) as totalSold, SUM(subtotal) as totalRevenue')
            ->groupBy('product_id', 'product_name')
            ->orderByDesc('totalSold')
            ->limit(10)
            ->get();

        $formattedTopProducts = $topProducts->map(function ($item) {
            return [
                'id' => $item->product_id,
                'name' => $item->product_name,
                'imageUrl' => $item->product->image_url ?? null,
                'sales' => (int) $item->totalSold,
                'revenue' => (float) $item->totalRevenue,
            ];
        });

        return response()->json([
            'topProducts' => $formattedTopProducts,
        ]);
    }

    /**
     * Get customer acquisition data.
     */
    public function getCustomerAcquisition(Request $request)
    {
        // For now, return mock data as we don't track acquisition sources yet
        return response()->json([
            'customerAcquisition' => [
                'organic' => 65,
                'direct' => 20,
                'referral' => 10,
                'social' => 5,
            ],
        ]);
    }

    /**
     * Helper function to get date ranges based on the time range string.
     */
    private function getDateRanges(string $timeRange): array
    {
        $endDate = Carbon::now();

        if ($timeRange === 'week') {
            $startDate = Carbon::now()->subDays(7);
            $previousEndDate = $startDate->copy()->subDay();
            $previousStartDate = $previousEndDate->copy()->subDays(7);
        } elseif ($timeRange === 'year') {
            $startDate = Carbon::now()->subYear();
            $previousEndDate = $startDate->copy()->subDay();
            $previousStartDate = $previousEndDate->copy()->subYear();
        } else { // Default to 'month'
            $startDate = Carbon::now()->subDays(30);
            $previousEndDate = $startDate->copy()->subDay();
            $previousStartDate = $previousEndDate->copy()->subDays(30);
        }

        return [$startDate, $endDate, $previousStartDate, $previousEndDate];
    }
}
