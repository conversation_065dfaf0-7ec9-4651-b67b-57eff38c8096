import { createContext, useContext, useState, useEffect } from 'react';

const PreloaderContext = createContext();

export function usePreloader() {
  return useContext(PreloaderContext);
}

export function PreloaderProvider({ children }) {
  const [loading, setLoading] = useState(true);

  // Hide preloader after initial load
  useEffect(() => {
    // Hide the preloader after a short delay to ensure the app has loaded
    const timer = setTimeout(() => {
      setLoading(false);

      // Also hide the initial HTML preloader if it still exists
      const initialPreloader = document.getElementById('initial-preloader');
      if (initialPreloader) {
        initialPreloader.style.opacity = '0';
        setTimeout(() => {
          initialPreloader.style.display = 'none';
        }, 300);
      }
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Show preloader
  const showPreloader = () => {
    setLoading(true);
  };

  // Hide preloader with debounce to prevent flickering
  const hidePreloader = () => {
    // Small delay to prevent flickering if multiple components call hidePreloader in sequence
    setTimeout(() => {
      setLoading(false);
    }, 100);
  };

  const value = {
    loading,
    showPreloader,
    hidePreloader
  };

  return (
    <PreloaderContext.Provider value={value}>
      {children}
    </PreloaderContext.Provider>
  );
}
