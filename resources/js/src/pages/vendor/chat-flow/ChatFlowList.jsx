import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  PencilSquareIcon,
  TrashIcon,
  ChatBubbleLeftRightIcon,
  MagnifyingGlassIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { chatFlowAPI } from '../../../services/api';

/**
 * ChatFlowList component
 * Displays a list of chat flows and allows creating, editing, and deleting flows
 */
export default function ChatFlowList() {
  // State for chat flows
  const [chatFlows, setChatFlows] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [flowToDelete, setFlowToDelete] = useState(null);

  // Fetch chat flows from API
  useEffect(() => {
    const fetchChatFlows = async () => {
      try {
        setLoading(true);
        const response = await chatFlowAPI.getAllChatFlows();
        setChatFlows(response.data.chatFlows || []);
        setError(null);
      } catch (err) {
//         // Removed console.error
        setError('Failed to load chat flows. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchChatFlows();
  }, []);

  // Filter chat flows based on search query
  const filteredChatFlows = chatFlows.filter(flow =>
    flow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (flow.description && flow.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Open delete confirmation modal
  const confirmDeleteFlow = (flow) => {
    setFlowToDelete(flow);
    setDeleteModalOpen(true);
  };

  // Handle delete chat flow
  const handleDeleteFlow = async () => {
    if (!flowToDelete) return;

    try {
      setLoading(true);
      await chatFlowAPI.deleteChatFlow(flowToDelete.id);
      setChatFlows(chatFlows.filter(flow => flow.id !== flowToDelete.id));
      setDeleteModalOpen(false);
      setFlowToDelete(null);
    } catch (err) {
//       // Removed console.error
      setError('Failed to delete chat flow. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Chat Flow Management</div>
          <h1 className="welcome-title">
            WhatsApp Chat Flows
          </h1>
          <p className="welcome-subtitle">
            Create and manage automated chat flows for your WhatsApp store. Design interactive conversations with customers using our drag-and-drop builder.
          </p>
        </div>
      </div>

      {/* Chat Flows Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Your Chat Flows</h2>
          <div className="flex items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search chat flows..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <Link to="/vendor/chat-flow/builder">
              <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2">
                <PlusIcon className="h-5 w-5" />
                Create New Flow
              </button>
            </Link>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading && !error ? (
          <div className="mt-6 text-center p-12 border border-dashed border-gray-300 rounded-lg">
            <div className="animate-pulse flex flex-col items-center">
              <div className="rounded-full bg-gray-200 h-12 w-12 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2.5"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3 mb-2.5"></div>
              <div className="h-3 bg-gray-200 rounded w-1/5"></div>
            </div>
          </div>
        ) : filteredChatFlows.length > 0 ? (
          <div className="grid grid-cols-1 gap-6 mt-6">
            {filteredChatFlows.map((flow) => (
              <div key={flow.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-visible">
                <div className="p-6 flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 p-3 bg-green-100 rounded-lg">
                      <ChatBubbleLeftRightIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{flow.name}</h3>
                      <p className="mt-1 text-sm text-gray-500">{flow.description}</p>
                      <div className="mt-2 flex items-center gap-4">
                        <span className="text-xs text-gray-500">Last modified: {formatDate(flow.lastModified)}</span>
                        <span className="text-xs text-gray-500">{flow.nodesCount || 0} nodes</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Link to={`/vendor/chat-flow/builder/${flow.id}`}>
                      <button className="p-2 text-gray-500 hover:text-green-600 hover:bg-gray-100 rounded-full">
                        <PencilSquareIcon className="h-5 w-5" />
                      </button>
                    </Link>
                    <button
                      className="p-2 text-gray-500 hover:text-red-500 hover:bg-gray-100 rounded-full"
                      onClick={() => confirmDeleteFlow(flow)}
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="mt-6 text-center p-12 border border-dashed border-gray-300 rounded-lg">
            <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No chat flows found</h3>
            <p className="mt-2 text-sm text-gray-500">
              {searchQuery ? 'No chat flows match your search criteria.' : 'Get started by creating your first chat flow.'}
            </p>
            {!searchQuery && (
              <Link to="/vendor/chat-flow/builder" className="mt-6 inline-block">
                <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2">
                  <PlusIcon className="h-5 w-5" />
                  Create New Flow
                </button>
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Quick Guide Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Guide</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900">What are Chat Flows?</h3>
            <p className="mt-2 text-sm text-gray-500">
              Chat flows are automated conversation paths that guide customers through interactions with your WhatsApp store. They help provide consistent customer service and automate common inquiries.
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900">How to Create a Flow</h3>
            <p className="mt-2 text-sm text-gray-500">
              Use our drag-and-drop builder to create nodes for messages, questions, and actions. Connect them to define the conversation path. Test your flow before publishing.
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900">Best Practices</h3>
            <p className="mt-2 text-sm text-gray-500">
              Keep flows simple and focused on specific customer needs. Use clear language and provide easy navigation options. Test thoroughly before publishing.
            </p>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Chat Flow</h3>
            <p className="text-sm text-gray-500 mb-6">
              Are you sure you want to delete the chat flow "{flowToDelete?.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setDeleteModalOpen(false);
                  setFlowToDelete(null);
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteFlow}
                className="px-4 py-2 text-sm font-medium text-white bg-red-500 rounded-md hover:bg-red-600"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
