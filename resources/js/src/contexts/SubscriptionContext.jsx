import { createContext, useContext, useState } from 'react';

const SubscriptionContext = createContext();

export function useSubscription() {
  return useContext(SubscriptionContext);
}

export function SubscriptionProvider({ children }) {
  const [selectedPlan, setSelectedPlan] = useState(null);

  const selectPlan = (plan) => {
    setSelectedPlan(plan);
  };

  const value = {
    selectedPlan,
    selectPlan
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}
