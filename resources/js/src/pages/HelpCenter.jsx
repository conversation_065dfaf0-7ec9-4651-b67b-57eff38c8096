import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

export default function HelpCenter() {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens (same as Home.jsx)
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529'
  };

  // Help categories
  const categories = [
    {
      id: 'general',
      name: 'General',
      icon: '🔍'
    },
    {
      id: 'account',
      name: 'Account',
      icon: '👤'
    },
    {
      id: 'store',
      name: 'Store Setup',
      icon: '🏪'
    },
    {
      id: 'products',
      name: 'Products',
      icon: '📦'
    },
    {
      id: 'orders',
      name: 'Orders',
      icon: '🛒'
    },
    {
      id: 'payments',
      name: 'Payments',
      icon: '💳'
    },
    {
      id: 'chatflow',
      name: 'Chat Flows',
      icon: '💬'
    }
  ];

  // FAQ data
  const faqs = {
    general: [
      {
        question: "What is WhaMart?",
        answer: "WhaMart is a platform that allows businesses to create WhatsApp-based stores. It enables you to sell products and services directly through WhatsApp, with features like automated chat flows, product catalogs, and order management."
      },
      {
        question: "How much does WhaMart cost?",
        answer: "WhaMart offers several pricing plans, including a free starter plan and premium plans with additional features. Visit our Pricing page for detailed information about our current plans and features."
      },
      {
        question: "Do I need technical knowledge to use WhaMart?",
        answer: "No, WhaMart is designed to be user-friendly with no coding or technical knowledge required. Our intuitive interface makes it easy to set up your store, add products, and create automated chat flows."
      },
      {
        question: "Can I try WhaMart before paying?",
        answer: "Yes! We offer a free plan with basic features so you can experience WhaMart before upgrading to a premium plan. No credit card is required to sign up for the free plan."
      }
    ],
    account: [
      {
        question: "How do I create a WhaMart account?",
        answer: "Click on the 'Get Started' button on our homepage and follow the simple registration process. You'll need to provide your email address and create a password."
      },
      {
        question: "How do I reset my password?",
        answer: "On the login page, click 'Forgot Password', enter your email address, and follow the instructions sent to your email to reset your password."
      },
      {
        question: "Can I have multiple users for my WhaMart account?",
        answer: "Yes, our Premium and Business plans support multiple team members with different access levels. You can invite team members from your account settings."
      }
    ],
    store: [
      {
        question: "How do I connect WhaMart to my WhatsApp account?",
        answer: "After creating your WhaMart account, go to 'Store Settings' and click 'Connect WhatsApp'. You'll be guided through the process of connecting to WhatsApp Business API."
      },
      {
        question: "Can I customize my store's appearance?",
        answer: "Yes, you can customize your store's logo, colors, and greeting messages to match your brand identity through the Store Settings page."
      },
      {
        question: "What is a store URL and how do I use it?",
        answer: "Your store URL is a unique web address (like whamart.shop/your-store) that you can share with customers. When they visit this URL, they can start shopping from your WhatsApp store."
      }
    ],
    products: [
      {
        question: "How do I add products to my store?",
        answer: "In your vendor dashboard, go to 'Products', click 'Add New Product', and fill in the details including name, description, price, and images."
      },
      {
        question: "Can I offer discounts on my products?",
        answer: "Yes, you can set regular and discount prices for your products, allowing customers to see the savings they're getting."
      },
      {
        question: "How many products can I add to my store?",
        answer: "The number of products you can add depends on your plan. The free plan allows up to 10 products, while premium plans offer higher or unlimited product limits."
      }
    ],
    orders: [
      {
        question: "How do I manage incoming orders?",
        answer: "All orders appear in the 'Orders' section of your dashboard. You can view details, update status, and manage fulfillment from there."
      },
      {
        question: "Can customers track their orders?",
        answer: "Yes, customers receive automated updates about their order status through WhatsApp messages, keeping them informed throughout the fulfillment process."
      },
      {
        question: "How do I mark an order as fulfilled?",
        answer: "In the Orders section, open the order you want to update, change its status to 'Fulfilled', and optionally add tracking information if applicable."
      }
    ],
    payments: [
      {
        question: "What payment methods can I offer to my customers?",
        answer: "WhaMart supports various payment methods including UPI, bank transfers, COD (Cash on Delivery), and payment links from popular payment processors."
      },
      {
        question: "How do I set up payment options for my store?",
        answer: "Go to 'Settings' > 'Payment Methods' in your dashboard and enable the payment options you want to offer. You can configure details for each method."
      },
      {
        question: "Are there any transaction fees?",
        answer: "WhaMart doesn't charge additional transaction fees. However, your payment processor might have their standard fees which are separate from your WhaMart subscription."
      }
    ],
    chatflow: [
      {
        question: "What are Chat Flows?",
        answer: "Chat Flows are automated conversation sequences that guide customers through your store. They can show products, collect information, and answer common questions automatically."
      },
      {
        question: "How do I create a Chat Flow?",
        answer: "In your dashboard, go to 'Chat Flows', click 'Create New Flow', and use our visual builder to design your conversation flow by adding nodes and connections."
      },
      {
        question: "Can I have different chat flows for different purposes?",
        answer: "Yes, you can create multiple chat flows for various purposes like product browsing, order tracking, customer support, and more. You can set conditions for when each flow should activate."
      }
    ]
  };

  // Filter FAQs based on search query
  const filteredFaqs = searchQuery 
    ? Object.values(faqs).flat().filter(faq => 
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs[activeCategory];

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      margin: 0,
      padding: 0,
      color: colors.dark,
      backgroundColor: colors.white
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: colors.white,
        color: colors.dark,
        padding: '15px 5%',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: isMobile ? '15px' : '0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/">
            <img
              src="/WhaMart_Logo.png"
              alt="WhaMart Logo"
              style={{
                height: '40px',
                marginRight: '10px'
              }}
            />
          </Link>
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '30px'
        }}>
          {!isMobile && (
            <nav>
              <ul style={{
                display: 'flex',
                listStyle: 'none',
                gap: '30px',
                margin: 0,
                padding: 0
              }}>
                <li><Link to="/#features" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Features</Link></li>
                <li><Link to="/#how-it-works" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>How It Works</Link></li>
                <li><Link to="/#pricing" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Pricing</Link></li>
                <li><Link to="/blog" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Blog</Link></li>
                <li><Link to="/help-center" style={{ color: colors.primary, textDecoration: 'none', fontWeight: 600, fontSize: '16px' }}>Help Center</Link></li>
              </ul>
            </nav>
          )}

          <div style={{ display: 'flex', gap: '15px' }}>
            <Link to="/login" style={{
              padding: '10px 20px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              transition: 'all 0.3s ease'
            }}>
              Sign In
            </Link>
            <Link to="/register" style={{
              padding: '10px 20px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Help Center Hero Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '60px 5%',
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '10%',
          right: '5%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <h1 style={{
            fontSize: isMobile ? '32px' : '42px',
            fontWeight: 700,
            marginBottom: '16px',
            color: colors.dark
          }}>
            How can we <span style={{ color: colors.primary }}>help you?</span>
          </h1>
          <p style={{
            fontSize: '18px',
            maxWidth: '700px',
            margin: '0 auto 32px',
            color: colors.gray,
            lineHeight: 1.6
          }}>
            Find answers to your questions about using WhaMart for your business
          </p>

          {/* Search Bar */}
          <div style={{
            maxWidth: '600px',
            margin: '0 auto',
            position: 'relative'
          }}>
            <input
              type="text"
              placeholder="Search for help topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: '100%',
                padding: '18px 24px',
                paddingLeft: '50px',
                borderRadius: '50px',
                border: 'none',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                fontSize: '16px',
                outline: 'none'
              }}
            />
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke={colors.gray} 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
              style={{
                position: 'absolute',
                left: '20px',
                top: '50%',
                transform: 'translateY(-50%)'
              }}
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
        </div>
      </section>

      {/* Help Center Content */}
      <section style={{
        padding: '80px 5%',
        backgroundColor: colors.white
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto',
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '40px'
        }}>
          {/* Categories Sidebar */}
          {!searchQuery && (
            <div style={{ 
              width: isMobile ? '100%' : '300px',
              flexShrink: 0
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: 600,
                marginBottom: '20px'
              }}>
                Help Topics
              </h3>
              <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: 0,
                display: isMobile ? 'flex' : 'block',
                flexWrap: 'wrap',
                gap: '10px'
              }}>
                {categories.map((category) => (
                  <li key={category.id} style={{
                    marginBottom: isMobile ? '0' : '8px'
                  }}>
                    <button
                      onClick={() => setActiveCategory(category.id)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        width: isMobile ? 'auto' : '100%',
                        padding: '12px 16px',
                        backgroundColor: activeCategory === category.id ? colors.primary : colors.lightGray,
                        color: activeCategory === category.id ? colors.white : colors.dark,
                        border: 'none',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        fontSize: '15px',
                        fontWeight: activeCategory === category.id ? 600 : 500,
                        textAlign: 'left',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <span style={{ marginRight: '10px', fontSize: '18px' }}>{category.icon}</span>
                      {category.name}
                    </button>
                  </li>
                ))}
              </ul>

              {/* Contact Support */}
              <div style={{
                marginTop: '40px',
                padding: '24px',
                backgroundColor: colors.secondary,
                borderRadius: '12px'
              }}>
                <h4 style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  marginBottom: '12px'
                }}>
                  Need more help?
                </h4>
                <p style={{
                  fontSize: '15px',
                  color: colors.gray,
                  marginBottom: '16px',
                  lineHeight: 1.5
                }}>
                  Can't find what you're looking for? Our support team is here to help.
                </p>
                <Link to="/contact-us" style={{
                  display: 'inline-block',
                  padding: '10px 20px',
                  backgroundColor: colors.white,
                  color: colors.primary,
                  border: `1px solid ${colors.primary}`,
                  borderRadius: '50px',
                  textDecoration: 'none',
                  fontWeight: 500,
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Contact Support
                </Link>
              </div>
            </div>
          )}

          {/* FAQs Content */}
          <div style={{ flex: 1 }}>
            {searchQuery ? (
              <>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 700,
                  marginBottom: '30px'
                }}>
                  Search Results for "{searchQuery}"
                </h2>
                {filteredFaqs.length === 0 ? (
                  <div style={{
                    padding: '40px',
                    textAlign: 'center',
                    backgroundColor: colors.lightGray,
                    borderRadius: '12px'
                  }}>
                    <h3 style={{ fontSize: '18px', marginBottom: '10px' }}>No results found</h3>
                    <p style={{ color: colors.gray }}>Try different keywords or contact our support team for assistance.</p>
                  </div>
                ) : (
                  filteredFaqs.map((faq, index) => (
                    <div 
                      key={index}
                      style={{
                        marginBottom: '20px',
                        padding: '20px',
                        backgroundColor: colors.lightGray,
                        borderRadius: '12px',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <h3 style={{
                        fontSize: '18px',
                        fontWeight: 600,
                        marginBottom: '12px'
                      }}>
                        {faq.question}
                      </h3>
                      <p style={{
                        fontSize: '15px',
                        lineHeight: 1.6,
                        color: colors.gray
                      }}>
                        {faq.answer}
                      </p>
                    </div>
                  ))
                )}
              </>
            ) : (
              <>
                <h2 style={{
                  fontSize: '28px',
                  fontWeight: 700,
                  marginBottom: '30px'
                }}>
                  {categories.find(c => c.id === activeCategory).name} FAQs
                </h2>
                {faqs[activeCategory].map((faq, index) => (
                  <div 
                    key={index}
                    style={{
                      marginBottom: '20px',
                      padding: '24px',
                      backgroundColor: colors.lightGray,
                      borderRadius: '12px',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: 600,
                      marginBottom: '12px'
                    }}>
                      {faq.question}
                    </h3>
                    <p style={{
                      fontSize: '15px',
                      lineHeight: 1.6,
                      color: colors.gray
                    }}>
                      {faq.answer}
                    </p>
                  </div>
                ))}
              </>
            )}
          </div>
        </div>
      </section>

      {/* Video Tutorials Section */}
      <section style={{
        padding: '80px 5%',
        backgroundColor: colors.lightGray
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h2 style={{
            fontSize: '32px',
            fontWeight: 700,
            marginBottom: '20px',
            textAlign: 'center'
          }}>
            Video Tutorials
          </h2>
          <p style={{
            fontSize: '18px',
            color: colors.gray,
            marginBottom: '40px',
            textAlign: 'center',
            maxWidth: '700px',
            margin: '0 auto 40px'
          }}>
            Learn how to make the most of WhaMart with our step-by-step tutorial videos
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
            gap: '30px'
          }}>
            {/* Tutorial 1 */}
            <div style={{
              backgroundColor: colors.white,
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
            }}>
              <div style={{
                height: '180px',
                backgroundColor: '#E0E0E0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="48" 
                  height="48" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke={colors.primary} 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="10 8 16 12 10 16 10 8"></polygon>
                </svg>
              </div>
              <div style={{ padding: '20px' }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  marginBottom: '8px'
                }}>
                  Getting Started with WhaMart
                </h3>
                <p style={{
                  fontSize: '14px',
                  color: colors.gray,
                  marginBottom: '16px',
                  lineHeight: 1.5
                }}>
                  Learn how to set up your WhaMart account, connect your WhatsApp Business, and create your first store.
                </p>
                <div style={{ color: colors.primary, fontWeight: 500, fontSize: '14px' }}>
                  5:32 min
                </div>
              </div>
            </div>
            
            {/* Tutorial 2 */}
            <div style={{
              backgroundColor: colors.white,
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
            }}>
              <div style={{
                height: '180px',
                backgroundColor: '#E0E0E0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="48" 
                  height="48" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke={colors.primary} 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="10 8 16 12 10 16 10 8"></polygon>
                </svg>
              </div>
              <div style={{ padding: '20px' }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  marginBottom: '8px'
                }}>
                  Building Effective Chat Flows
                </h3>
                <p style={{
                  fontSize: '14px',
                  color: colors.gray,
                  marginBottom: '16px',
                  lineHeight: 1.5
                }}>
                  Master the art of creating engaging, conversion-focused chat flows that guide customers through your store.
                </p>
                <div style={{ color: colors.primary, fontWeight: 500, fontSize: '14px' }}>
                  7:15 min
                </div>
              </div>
            </div>
            
            {/* Tutorial 3 */}
            <div style={{
              backgroundColor: colors.white,
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
            }}>
              <div style={{
                height: '180px',
                backgroundColor: '#E0E0E0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="48" 
                  height="48" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke={colors.primary} 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="10 8 16 12 10 16 10 8"></polygon>
                </svg>
              </div>
              <div style={{ padding: '20px' }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  marginBottom: '8px'
                }}>
                  Managing Orders and Payments
                </h3>
                <p style={{
                  fontSize: '14px',
                  color: colors.gray,
                  marginBottom: '16px',
                  lineHeight: 1.5
                }}>
                  Learn how to process orders, manage inventory, and set up payment methods for your WhaMart store.
                </p>
                <div style={{ color: colors.primary, fontWeight: 500, fontSize: '14px' }}>
                  6:48 min
                </div>
              </div>
            </div>
          </div>
          
          <div style={{
            textAlign: 'center',
            marginTop: '40px'
          }}>
            <a href="#" style={{
              padding: '14px 32px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 600,
              fontSize: '16px',
              display: 'inline-block',
              transition: 'all 0.3s ease'
            }}>
              View All Tutorials
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="footer" style={{
        backgroundColor: colors.dark,
        padding: '80px 5% 40px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '2fr 1fr 1fr 1fr',
          gap: '40px',
          marginBottom: '50px'
        }}>
          {/* Company Info */}
          <div>
            <h3 style={{
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              color: colors.white
            }}>
              Whamart
            </h3>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              marginBottom: '24px',
              fontSize: '15px',
              lineHeight: 1.6
            }}>
              Turn your WhatsApp Business into a powerful e-commerce platform with our innovative catalog and automated response solutions.
            </p>
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px'
            }}>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Product
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#features" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Features
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#how-it-works" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  How It Works
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#pricing" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Company
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/about-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  About Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/blog" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Blog
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/press" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Support
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/help-center" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Help Center
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/contact-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Contact Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/terms-of-service" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div style={{
          borderTop: '1px solid rgba(255,255,255,0.1)',
          paddingTop: '30px',
          textAlign: 'center',
          color: 'rgba(255,255,255,0.5)',
          fontSize: '14px'
        }}>
          © {new Date().getFullYear()} Whamart. All rights reserved.
        </div>
      </footer>
    </div>
  );
}