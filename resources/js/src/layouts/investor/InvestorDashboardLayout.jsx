import { useState, useEffect } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/dashboard.css';
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  HomeIcon,
  ChartBarIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  QuestionMarkCircleIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  UsersIcon,
  PresentationChartLineIcon,
  GlobeAltIcon,
  PuzzlePieceIcon,
  UserGroupIcon,
  DocumentChartBarIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';

export default function InvestorDashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true); // For desktop sidebar visibility
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  }, [location.pathname]);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    if (window.innerWidth < 768) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarVisible(!sidebarVisible);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      // Handle logout error silently
    }
  };

  // Define navigation items for investor
  const getNavItems = () => {
    // Main navigation items
    const mainItems = [
      { name: 'Dashboard', href: '/investor', icon: HomeIcon },
      { name: 'Performance', href: '/investor/performance', icon: ChartBarIcon },
      { name: 'Investments', href: '/investor/investments', icon: CurrencyDollarIcon },
      { name: 'Capital Management', href: '/investor/capital', icon: BanknotesIcon },
    ];

    // Analytics items
    const analyticsItems = [
      { name: 'Vendor Analytics', href: '/investor/vendor-analytics', icon: BuildingOfficeIcon },
      { name: 'Revenue Analytics', href: '/investor/revenue-analytics', icon: PresentationChartLineIcon },
      { name: 'Growth Projections', href: '/investor/projections', icon: DocumentChartBarIcon },
      { name: 'Market Expansion', href: '/investor/market-expansion', icon: GlobeAltIcon },
    ];

    // Management items
    const managementItems = [
      { name: 'Equity Distribution', href: '/investor/equity', icon: PieChartIcon },
      { name: 'Management Board', href: '/investor/management', icon: UserGroupIcon },
      { name: 'Upcoming Features', href: '/investor/roadmap', icon: PuzzlePieceIcon },
    ];

    // Secondary navigation items
    const secondaryItems = [
      { name: 'Profile', href: '/investor/profile', icon: UserCircleIcon },
      { name: 'Settings', href: '/investor/settings', icon: CogIcon },
      { name: 'Help & Support', href: '/investor/support', icon: QuestionMarkCircleIcon },
    ];

    return {
      main: mainItems,
      analytics: analyticsItems,
      management: managementItems,
      secondary: secondaryItems
    };
  };

  // Determine the active route
  const isActiveRoute = (href) => {
    const currentPath = window.location.pathname;
    return currentPath === href || (href !== '/investor' && currentPath.startsWith(href));
  };

  const navItems = getNavItems();

  return (
    <div className="dashboard-layout">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div className={`dashboard-sidebar md:hidden z-40 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button
            type="button"
            className="flex items-center justify-center h-8 w-8 rounded-full bg-accent hover:bg-primary transition-colors"
            onClick={() => setSidebarOpen(false)}
          >
            <span className="sr-only">Close sidebar</span>
            <XMarkIcon className="h-5 w-5 text-white" aria-hidden="true" />
          </button>
        </div>

        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Analytics</div>
            {navItems.analytics.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Management</div>
            {navItems.management.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Account</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className={`dashboard-sidebar hidden md:block ${!sidebarVisible ? 'dashboard-sidebar-collapsed' : ''}`}>
        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Analytics</div>
            {navItems.analytics.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Management</div>
            {navItems.management.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Account</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className={`dashboard-main-content ${!sidebarVisible ? 'sidebar-collapsed-content' : ''}`}>
        {/* Mobile header */}
        <div className="dashboard-mobile-header">
          <button
            type="button"
            className="dashboard-mobile-menu-button"
            onClick={toggleSidebar}
          >
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
          <h1 className="dashboard-mobile-brand">Whamart Investor</h1>
          <div className="w-6"></div> {/* Empty div for flex spacing */}
        </div>

        {/* Desktop header */}
        <div className="dashboard-header">
          <div className="dashboard-header-content">
            <div className="dashboard-header-left">
              <button
                type="button"
                className="dashboard-header-menu-button"
                onClick={toggleSidebar}
              >
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div className="dashboard-header-right">
              <div className="dashboard-header-user">
                <div className="dashboard-header-user-info">
                  <p className="dashboard-header-user-name">{currentUser?.name || 'Investor'}</p>
                  <p className="dashboard-header-user-role">Investor</p>
                </div>
                <div className="dashboard-header-user-avatar">
                  <UserCircleIcon className="h-8 w-8 text-gray-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-content-wrapper">
          <Outlet />
        </div>
      </div>
    </div>
  );
}

// Custom PieChart icon since it's not available in heroicons
function PieChartIcon(props) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24" 
      strokeWidth={1.5} 
      stroke="currentColor" 
      className={props.className}
      style={props.style}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 2v10l8.6 5" />
    </svg>
  );
}
