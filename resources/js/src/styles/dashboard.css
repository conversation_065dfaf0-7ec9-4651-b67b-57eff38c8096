/* Dashboard specific styles */
:root {
  --primary: #25D366;    /* Bright WhatsApp green - 60% */
  --secondary: #E9F7EF;  /* Light green background - 30% */
  --accent: #075E54;     /* Dark green accent - 10% */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --gray: #6C757D;
  --dark: #212529;
  --error: #DC3545;
  --error-light: #F8D7DA;
  --border-radius-sm: 8px;
  --border-radius-md: 16px;
  --border-radius-lg: 24px;
  --border-radius-xl: 50px;
  --shadow-sm: 0 5px 15px rgba(0,0,0,0.05);
  --shadow-md: 0 10px 30px rgba(0,0,0,0.08);
  --shadow-lg: 0 15px 35px rgba(0,0,0,0.1);
  --shadow-primary: 0 4px 14px rgba(37, 211, 102, 0.3);
  --shadow-primary-lg: 0 6px 20px rgba(37, 211, 102, 0.4);
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --header-height: 80px;
}

body {
  font-family: 'Poppins', 'Segoe UI', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--secondary);
  color: var(--dark);
}

/* Main Layout */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  width: 100%;
  position: relative;
  background-color: var(--secondary);
}

/* Sidebar Styling */
.dashboard-sidebar {
  background-color: var(--primary);
  color: var(--white);
  height: 100vh;
  width: 280px;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 30; /* Lower than header */
  transition: var(--transition-normal);
  box-shadow: var(--shadow-primary-lg);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255,255,255,0.3) transparent;
  border-radius: 0 20px 20px 0;
  padding-top: 0; /* No padding, sidebar will be behind header */
}

/* Collapsed sidebar */
.dashboard-sidebar.sidebar-collapsed {
  width: 0;
  overflow: hidden;
  box-shadow: none;
}

.dashboard-sidebar::-webkit-scrollbar {
  width: 4px;
}

.dashboard-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.dashboard-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(255,255,255,0.3);
  border-radius: 20px;
}

/* Fix for mobile sidebar */
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 280px;
    transform: translateX(-100%);
    border-radius: 0 20px 20px 0;
    padding-top: 0; /* Reset padding for mobile */
  }

  .dashboard-sidebar.translate-x-0 {
    transform: translateX(0);
  }
}

/* Sidebar logo styling */
.dashboard-sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  margin-top: 15px;
  background-color: var(--white);
  border-radius: 12px;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: var(--shadow-sm);
}

.dashboard-sidebar-logo-image {
  height: 40px;
  width: auto;
  object-fit: contain;
  max-width: 100%;
}

.dashboard-nav-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 15px 20px; /* Reduced top padding */
}

.dashboard-nav-section {
  margin-bottom: 20px; /* Reduced bottom margin */
}

.dashboard-nav-section-title {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  color: rgba(255,255,255,0.8);
  padding: 0 16px;
  margin-bottom: 10px; /* Reduced bottom margin */
  margin-top: 15px; /* Reduced top margin */
  font-weight: 600;
}

.dashboard-nav-item {
  display: flex;
  align-items: center;
  padding: 10px 14px; /* Reduced padding */
  color: var(--white);
  transition: var(--transition-fast);
  font-weight: 500;
  text-decoration: none;
  position: relative;
  margin: 3px 0; /* Reduced margin */
  border-radius: var(--border-radius-md);
  font-size: 14px; /* Smaller font size */
}

.dashboard-nav-item:hover {
  background-color: rgba(255,255,255,0.2);
  transform: translateX(3px);
}

.dashboard-nav-item.active {
  background-color: var(--white);
  color: var(--primary);
  box-shadow: var(--shadow-sm);
  font-weight: 600;
}

.dashboard-nav-item.active .dashboard-nav-icon {
  color: var(--primary);
}

.dashboard-nav-icon {
  margin-right: 10px; /* Reduced margin */
  width: 18px; /* Smaller icon */
  height: 18px; /* Smaller icon */
  color: var(--white);
}

.dashboard-user-section {
  padding: 15px;
  border-top: 1px solid rgba(255,255,255,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255,255,255,0.1);
  margin: 10px 15px 20px;
  border-radius: 16px;
  margin-top: auto;
}

.dashboard-logout-button {
  background: rgba(255,255,255,0.2);
  border: none;
  color: var(--white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 50px;
  transition: var(--transition-fast);
  white-space: nowrap;
  width: 100%;
  font-weight: 500;
}

.dashboard-logout-button:hover {
  background-color: #dc3545; /* Red color for hover */
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.dashboard-logout-icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
}

/* Main Content Area */
.dashboard-main-content {
  margin-left: 280px;
  width: calc(100% - 280px);
  min-height: 100vh;
  transition: var(--transition-normal);
  padding: 0;
  position: relative;
}

/* Main content when sidebar is collapsed */
.dashboard-main-content.sidebar-collapsed-content {
  margin-left: 0;
  width: 100%;
}

/* Dashboard Header */
.dashboard-header {
  background-color: var(--white);
  height: var(--header-height);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow: var(--shadow-sm);
  display: none;
  margin-bottom: 20px;
}

@media (min-width: 769px) {
  .dashboard-header {
    display: block;
  }
}

.dashboard-header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dashboard-header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.dashboard-header-menu-button {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.dashboard-header-menu-button:hover {
  background-color: var(--light-gray);
}

.dashboard-header-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark);
  margin: 0;
  position: relative;
  padding-left: 24px;
}

.dashboard-header-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 24px;
  width: 1px;
  background-color: rgba(0,0,0,0.1);
}

.dashboard-header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.dashboard-header-search {
  position: relative;
}

.dashboard-search-input {
  background-color: var(--light-gray);
  border: none;
  border-radius: var(--border-radius-xl);
  padding: 10px 16px;
  width: 240px;
  font-size: 14px;
  transition: var(--transition-fast);
}

.dashboard-search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2);
  background-color: var(--white);
}

.dashboard-header-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-header-user-info {
  text-align: right;
}

.dashboard-header-user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  margin: 0;
}

.dashboard-header-user-role {
  font-size: 12px;
  color: var(--gray);
  margin: 0;
}

.dashboard-header-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px;
  padding-top: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 1024px) {
  .dashboard-content-wrapper {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .dashboard-main-content {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-content-wrapper {
    padding: 20px;
  }
}

/* Mobile Header */
.dashboard-mobile-header {
  display: none;
  background-color: var(--white);
  padding: 16px 20px;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 30;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .dashboard-mobile-header {
    display: flex;
  }
}

.dashboard-mobile-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.dashboard-mobile-logo-image {
  height: 35px;
  width: auto;
  object-fit: contain;
  max-width: 120px;
}

.dashboard-mobile-menu-button {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.dashboard-mobile-menu-button:hover {
  background-color: var(--light-gray);
}

/* Welcome Section */
.dashboard-welcome-section {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border-left: 4px solid var(--primary);
  transition: transform 0.3s ease;
}

.dashboard-welcome-section:hover {
  transform: translateY(-5px);
}

@media (max-width: 768px) {
  .dashboard-welcome-section {
    padding: 30px;
  }
}

.dashboard-welcome-decoration {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%);
  z-index: 0;
}

.dashboard-welcome-decoration-1 {
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
}

.dashboard-welcome-decoration-2 {
  bottom: -30px;
  left: -30px;
  width: 150px;
  height: 150px;
}

.dashboard-welcome-content {
  position: relative;
  z-index: 1;
}

.dashboard-badge {
  display: inline-block;
  padding: 8px 16px;
  background-color: rgba(37, 211, 102, 0.1);
  color: var(--primary);
  border-radius: var(--border-radius-xl);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
}

.dashboard-welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--dark);
}

.dashboard-welcome-subtitle {
  font-size: 16px;
  color: var(--gray);
  margin-bottom: 0;
  line-height: 1.6;
  max-width: 600px;
}

/* Stats Section */
.dashboard-stats-section {
  margin-bottom: 25px;
}

.dashboard-section-header {
  margin-bottom: 16px;
}

.dashboard-section-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--dark);
  margin: 0;
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
}

@media (min-width: 640px) {
  .dashboard-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }
}

.dashboard-stat-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  padding: 15px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%;
}

.dashboard-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.dashboard-stat-icon-container {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.dashboard-stat-icon {
  width: 22px;
  height: 22px;
}

.dashboard-stat-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray);
  margin: 0 0 6px 0;
}

.dashboard-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--dark);
  margin: 0 0 6px 0;
}

.dashboard-stat-description {
  font-size: 12px;
  color: var(--gray);
  margin: 0;
  line-height: 1.4;
}

/* Orders Section */
.dashboard-orders-section {
  margin-bottom: 25px;
}

.dashboard-section-header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-orders-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.dashboard-orders-table-container {
  overflow-x: auto;
  width: 100%;
}

.dashboard-orders-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.dashboard-orders-table th {
  background-color: var(--light-gray);
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.dashboard-orders-table td {
  padding: 16px;
  font-size: 14px;
  color: var(--dark);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  vertical-align: middle;
}

.dashboard-order-row {
  transition: var(--transition-fast);
}

.dashboard-order-row:hover {
  background-color: var(--light-gray);
}

.dashboard-orders-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.dashboard-pagination-info {
  font-size: 14px;
  color: var(--gray);
}

.dashboard-pagination-controls {
  display: flex;
  gap: 8px;
}

.dashboard-pagination-button {
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  color: var(--dark);
  cursor: pointer;
  transition: var(--transition-fast);
}

.dashboard-pagination-button:hover:not(:disabled) {
  background-color: var(--light-gray);
}

.dashboard-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dashboard-pagination-active {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

.dashboard-order-status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: var(--border-radius-xl);
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.dashboard-order-status-completed {
  background-color: rgba(37, 211, 102, 0.1);
  color: var(--primary);
}

.dashboard-order-status-processing {
  background-color: rgba(255, 193, 7, 0.1);
  color: #FFC107;
}

.dashboard-order-status-pending {
  background-color: rgba(108, 117, 125, 0.1);
  color: var(--gray);
}

.dashboard-order-date {
  font-size: 12px;
  color: var(--gray);
  margin: 0;
}

/* Store Preview Section */
.dashboard-store-section {
  margin-bottom: 40px;
}

.dashboard-store-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.dashboard-store-content {
  padding: 30px;
}

.dashboard-store-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
}

.dashboard-store-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--dark);
  margin: 0 0 12px 0;
}

.dashboard-store-description {
  font-size: 15px;
  color: var(--gray);
  margin: 0 0 24px 0;
  line-height: 1.6;
  max-width: 600px;
}

.dashboard-store-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.dashboard-store-info {
  background-color: var(--secondary);
  border-radius: var(--border-radius-md);
  padding: 24px;
}

.dashboard-store-info-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark);
  margin: 0 0 16px 0;
}

.dashboard-store-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-store-info-item:last-child {
  margin-bottom: 0;
}

.dashboard-store-info-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.dashboard-store-info-icon {
  width: 20px;
  height: 20px;
  color: var(--white);
}

.dashboard-store-info-text {
  flex: 1;
}

.dashboard-store-info-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  margin: 0 0 2px 0;
}

.dashboard-store-info-value {
  font-size: 13px;
  color: var(--gray);
  margin: 0;
}

/* Button Styling */
.dashboard-button {
  padding: 12px 24px;
  border-radius: var(--border-radius-xl);
  font-weight: 500;
  font-size: 15px;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  text-decoration: none;
  border: none;
}

.dashboard-button-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-primary);
}

.dashboard-button-primary:hover {
  background-color: var(--accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary-lg);
}

.dashboard-button-outline {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.dashboard-button-outline:hover {
  background-color: rgba(37, 211, 102, 0.1);
  transform: translateY(-2px);
}

.dashboard-button-icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
}

/* Responsive Layout */
@media (max-width: 1200px) {
  .dashboard-welcome-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .dashboard-welcome-section {
    padding: 24px;
  }

  .dashboard-welcome-title {
    font-size: 24px;
  }

  .dashboard-welcome-subtitle {
    font-size: 15px;
  }

  .dashboard-stat-card {
    padding: 24px;
  }

  .dashboard-order-item {
    padding: 20px;
  }

  .dashboard-store-content {
    padding: 24px;
  }
}

/* Utility Classes */
.whatsapp-bg-primary {
  background-color: var(--primary);
}

.whatsapp-bg-secondary {
  background-color: var(--secondary);
}

.whatsapp-bg-accent {
  background-color: var(--accent);
}

.whatsapp-text-primary {
  color: var(--primary);
}

.whatsapp-text-accent {
  color: var(--accent);
}

.dashboard-button {
  padding: 10px 20px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.dashboard-button-primary {
  background-color: #25D366;
  color: #FFFFFF;
  border: none;
  box-shadow: 0 4px 14px rgba(37, 211, 102, 0.3);
}

.dashboard-button-primary:hover {
  background-color: #128C7E;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

.dashboard-button-outline {
  background-color: transparent;
  color: #25D366;
  border: 1px solid #25D366;
}

.dashboard-button-outline:hover {
  background-color: rgba(37, 211, 102, 0.1);
  transform: translateY(-2px);
}

.dashboard-badge {
  display: inline-block;
  padding: 8px 16px;
  background-color: rgba(37, 211, 102, 0.1);
  color: #25D366;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
}

.dashboard-stat-card {
  position: relative;
  background-color: #FFFFFF;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  overflow: hidden;
  transition: transform 0.3s ease;
  border: 1px solid rgba(0,0,0,0.03);
}

.dashboard-stat-card:hover {
  transform: translateY(-5px);
}

.dashboard-stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #212529;
  margin-top: 8px;
}

.dashboard-stat-label {
  font-size: 16px;
  color: #6C757D;
  margin-bottom: 4px;
}

.whatsapp-bg-primary {
  background-color: #25D366;
}

.whatsapp-bg-secondary {
  background-color: #E9F7EF;
}

.whatsapp-bg-accent {
  background-color: #075E54;
}

.whatsapp-text-primary {
  color: #25D366;
}

.whatsapp-text-accent {
  color: #075E54;
}
