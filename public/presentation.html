<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Instrument+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'whatsapp': {
                            'light': '#DCF8C6',
                            'default': '#25D366',
                            'dark': '#128C7E',
                            'teal': '#075E54',
                        }
                    },
                    fontFamily: {
                        sans: ['Instrument Sans', 'ui-sans-serif', 'system-ui', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background: linear-gradient(135deg, #25D366 0%, #075E54 100%);
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .dark body {
            background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
            color: #EDEDEC;
        }

        .presentation-container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
            background: transparent;
        }

        .dark .presentation-container {
            background: transparent;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 2rem;
            box-sizing: border-box;
            position: absolute;
            color: #1b1b18;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide.active {
            display: flex;
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .dark .slide {
            color: #EDEDEC;
        }

        /* Slide entrance animations */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-enter-right {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .slide-enter-left {
            animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }



        /* Slide 1 Styles */
        #slide1 {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(7, 94, 84, 0.1) 100%);
            backdrop-filter: blur(10px);
        }
        .dark #slide1 {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
        }

        /* Common Slide Styles */
        .slide h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #25D366 0%, #075E54 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .slide h2 {
            width: 100%;
            text-align: center;
            color: #1b1b18;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            font-weight: 600;
            background: linear-gradient(135deg, #25D366 0%, #075E54 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dark .slide h2 {
            background: linear-gradient(135deg, #25D366 0%, #4ADE80 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Card Container Styles */
        .card-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            padding: 2rem;
            text-align: left;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
            max-width: 1024px;
            width: 100%;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .dark .card-container {
            background: rgba(22, 22, 21, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 250, 237, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.3);
        }

        /* Slide 2 Styles */
        #slide2 {
            align-items: center;
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.05) 0%, rgba(7, 94, 84, 0.05) 100%);
        }

        #slide2 .container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .dark #slide2 .container {
            background: rgba(22, 22, 21, 0.9);
            border: 1px solid rgba(37, 211, 102, 0.3);
        }

        #slide2 .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        #slide2 .problem-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(249, 250, 251, 0.9) 100%);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(37, 211, 102, 0.1);
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        #slide2 .problem-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(37, 211, 102, 0.2);
            border-color: rgba(37, 211, 102, 0.3);
        }

        .dark #slide2 .problem-card {
            background: linear-gradient(135deg, rgba(22, 22, 21, 0.9) 0%, rgba(26, 26, 26, 0.9) 100%);
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .dark #slide2 .problem-card:hover {
            border-color: rgba(37, 211, 102, 0.4);
        }

        #slide2 .icon {
            color: #25D366;
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
            text-shadow: 0 2px 4px rgba(37, 211, 102, 0.3);
        }
        


        /* Slide 3 Styles */
        #slide3 {
            align-items: center;
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.05) 0%, rgba(7, 94, 84, 0.05) 100%);
        }

        #slide3 .container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            max-width: 1200px;
            width: 100%;
        }

        .dark #slide3 .container {
            background: rgba(22, 22, 21, 0.9);
            border: 1px solid rgba(37, 211, 102, 0.3);
        }

        #slide3 .feature-container {
            display: flex;
            justify-content: space-between;
            gap: 3rem;
            width: 100%;
            margin-top: 2rem;
        }

        #slide3 .feature-column {
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(249, 250, 251, 0.8) 100%);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .dark #slide3 .feature-column {
            background: linear-gradient(135deg, rgba(22, 22, 21, 0.8) 0%, rgba(26, 26, 26, 0.8) 100%);
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        #slide3 ul {
            list-style: none;
            padding: 0;
            text-align: left;
            margin: 0;
        }

        #slide3 li {
            display: flex;
            align-items: center;
            font-size: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            padding: 0.75rem;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        #slide3 li:hover {
            background: rgba(37, 211, 102, 0.1);
            transform: translateX(5px);
        }

        .dark #slide3 li {
            color: #EDEDEC;
        }

        .dark #slide3 li:hover {
            background: rgba(37, 211, 102, 0.2);
        }

        #slide3 .icon {
            color: #25D366;
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(7, 94, 84, 0.1) 100%);
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.2);
            border: 2px solid rgba(37, 211, 102, 0.2);
        }

        .dark #slide3 .icon {
            color: #4ADE80;
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.2) 0%, rgba(7, 94, 84, 0.2) 100%);
            border: 2px solid rgba(37, 211, 102, 0.3);
        }

        /* Slide 4 Styles */
        #slide4 {
            align-items: center;
        }

        #slide4 .content-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        #slide4 .chart-container {
            width: 45%;
            position: relative;
            border-radius: 0.5rem;
            overflow: hidden;
            background-color: rgba(255, 242, 242, 0.3);
            padding: 1rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark #slide4 .chart-container {
            background-color: rgba(29, 0, 2, 0.3);
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        #slide4 .segments {
            width: 48%;
            text-align: left;
        }

        #slide4 .segment {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            margin-bottom: 1.5rem;
        }
        
        .dark #slide4 .segment {
            color: #EDEDEC;
        }

        #slide4 .icon {
            color: #f53003; /* Laravel red */
            font-size: 1.25rem;
            margin-right: 1rem;
            width: 1.5rem;
            text-align: center;
            border-radius: 9999px;
            background-color: #FDFDFC;
            padding: 0.25rem;
            box-shadow: 0px 0px 1px 0px rgba(0,0,0,0.03), 0px 1px 2px 0px rgba(0,0,0,0.06);
            border: 1px solid #e3e3e0;
        }
        
        .dark #slide4 .icon {
            color: #FF4433;
            background-color: #161615;
            border: 1px solid #3E3E3A;
        }

        #slide4 .focus-paragraph {
            margin-top: 2rem;
            font-size: 0.875rem;
            width: 100%;
            text-align: center;
            color: #706f6c;
        }
        
        .dark #slide4 .focus-paragraph {
            color: #A1A09A;
        }

        /* Slide 5 Styles */
        #slide5 {
            align-items: center;
        }

        .comparison-table-container {
            max-width: 1024px;
            width: 100%;
            background-color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .comparison-table-container {
            background-color: #161615;
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            color: #1b1b18;
            font-size: 0.875rem;
        }
        
        .dark .comparison-table {
            color: #EDEDEC;
        }

        .comparison-table th, .comparison-table td {
            border: 1px solid #e3e3e0;
            padding: 0.75rem 1rem;
            text-align: left;
        }
        
        .dark .comparison-table th, .dark .comparison-table td {
            border-color: #3E3E3A;
        }

        .comparison-table th {
            background-color: #FDFDFC;
            font-weight: 500;
        }
        
        .dark .comparison-table th {
            background-color: #1a1a18;
        }

        /* Slide 6 Styles */
        #slide6 {
            align-items: center;
        }

        #slide6 .business-model-container {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            width: 100%;
            margin-bottom: 2rem;
        }

        #slide6 .model-item {
            width: 30%;
            text-align: center;
            padding: 1rem;
            background-color: #FDFDFC;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark #slide6 .model-item {
            background-color: #161615;
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        #slide6 .icon {
            font-size: 1.5rem;
            color: #f53003;
            margin-bottom: 0.75rem;
        }
        
        .dark #slide6 .icon {
            color: #FF4433;
        }

        #slide6 h3 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #1b1b18;
        }
        
        .dark #slide6 h3 {
            color: #EDEDEC;
        }

        #slide6 p {
            font-size: 0.875rem;
            color: #706f6c;
        }
        
        .dark #slide6 p {
            color: #A1A09A;
        }

        .revenue-chart-container {
            width: 100%;
            max-width: 600px;
            margin-top: 1.5rem;
            border-radius: 0.5rem;
            overflow: hidden;
            background-color: rgba(255, 242, 242, 0.3);
            padding: 1rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .revenue-chart-container {
            background-color: rgba(29, 0, 2, 0.3);
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }
        
        .revenue-projection {
            margin-top: 1rem;
            font-size: 0.875rem;
            color: #706f6c;
        }
        
        .dark .revenue-projection {
            color: #A1A09A;
        }

        /* Slide 7 Styles */
        #slide7 {
            align-items: center;
        }

        #slide7 .capital-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 1024px;
        }

        .capital-chart-container {
            width: 100%;
            max-width: 600px;
            margin-bottom: 2rem;
            border-radius: 0.5rem;
            overflow: hidden;
            background-color: rgba(255, 242, 242, 0.3);
            padding: 1rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .capital-chart-container {
            background-color: rgba(29, 0, 2, 0.3);
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        .launch-highlight {
            background-color: #f53003;
            color: #fff;
            padding: 1rem 1.5rem;
            border-radius: 0.375rem;
            font-size: 1rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .dark .launch-highlight {
            background-color: #FF4433;
        }

        .launch-highlight .fa-rocket {
            margin-right: 0.75rem;
        }

        /* Slide 8 Styles */
        #slide8 {
            align-items: center;
        }

        .timeline-container {
            display: flex;
            justify-content: space-between;
            width: 100%;
            position: relative;
            margin-top: 2rem;
            max-width: 1024px;
            background-color: white;
            padding: 2rem 1rem 1rem;
            border-radius: 0.5rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .timeline-container {
            background-color: #161615;
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        .timeline-container::before {
            content: '';
            position: absolute;
            top: 1rem;
            left: 5%;
            right: 5%;
            height: 2px;
            background-color: #e3e3e0;
            z-index: 1;
        }
        
        .dark .timeline-container::before {
            background-color: #3E3E3A;
        }

        .timeline-milestone {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 18%;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .milestone-icon {
            width: 1.5rem;
            height: 1.5rem;
            background-color: #FDFDFC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: #f53003;
            border: 1px solid #e3e3e0;
            box-shadow: 0px 0px 1px 0px rgba(0,0,0,0.03), 0px 1px 2px 0px rgba(0,0,0,0.06);
        }
        
        .dark .milestone-icon {
            background-color: #161615;
            color: #FF4433;
            border-color: #3E3E3A;
        }

        .timeline-milestone:last-child .milestone-icon {
            color: #f53003;
        }
        
        .dark .timeline-milestone:last-child .milestone-icon {
            color: #FF4433;
        }

        .milestone-text {
            margin-top: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            color: #706f6c;
        }
        
        .dark .milestone-text {
            color: #A1A09A;
        }

        /* Slide 9 Styles */
        #slide9 {
            background-color: #f5f5f5; /* Light grey */
        }

        .investment-container {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 100%;
            padding: 0 50px;
        }

        .investment-points {
            width: 45%;
        }

        .investment-points ul {
            list-style: none;
            padding: 0;
        }

        .investment-points li {
            font-size: 1.4rem;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .investment-points .highlight {
            color: #28a745;
            font-weight: bold;
        }

        .contact-details {
            font-size: 0.875rem;
            margin-bottom: 1rem;
            color: #706f6c;
        }
        
        .dark .contact-details {
            color: #A1A09A;
        }

        .social-icons {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }

        .social-icon {
            font-size: 1.25rem;
            margin: 0 0.75rem;
            color: #f53003;
        }
        
        .dark .social-icon {
            color: #FF4433;
        }

        /* Slide 10 Styles */
        #slide10 {
            align-items: center;
        }

        .qa-container {
            width: 100%;
            max-width: 900px;
            text-align: left;
        }

        .qa-container h2 {
            text-align: center;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .qa-item {
            background-color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .qa-item {
            background-color: #161615;
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        .qa-question {
            font-size: 0.875rem;
            font-weight: 500;
            color: #1b1b18;
            margin-bottom: 0.5rem;
        }
        
        .dark .qa-question {
            color: #EDEDEC;
        }

        .qa-answer {
            font-size: 0.875rem;
            color: #706f6c;
        }
        
        .dark .qa-answer {
            color: #A1A09A;
        }

        /* Slide 11 Styles */
        #slide11 {
            align-items: center;
        }

        .final-cta {
            width: 100%;
            max-width: 900px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: inset 0px 0px 0px 1px rgba(26,26,0,0.16);
        }
        
        .dark .final-cta {
            background-color: #161615;
            box-shadow: inset 0px 0px 0px 1px rgba(255,250,237,0.18);
        }

        .cta-heading {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 1rem;
            color: #1b1b18;
        }
        
        .dark .cta-heading {
            color: #EDEDEC;
        }

        .cta-text {
            font-size: 0.875rem;
            color: #706f6c;
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .dark .cta-text {
            color: #A1A09A;
        }

        .cta-button {
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            background-color: #f53003;
            color: #fff;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            font-weight: 500;
        }
        
        .dark .cta-button {
            background-color: #FF4433;
        }

        .cta-info {
            margin-top: 1rem;
            font-size: 0.75rem;
            color: #706f6c;
        }
        
        .dark .cta-info {
            color: #A1A09A;
        }

        /* Controls */
        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: rgba(37, 211, 102, 0.1);
            border-color: rgba(37, 211, 102, 0.4);
            transform: scale(1.1);
        }

        .dark .nav-btn {
            background: rgba(22, 22, 21, 0.9);
            border: 1px solid rgba(37, 211, 102, 0.3);
        }

        .dark .nav-btn:hover {
            background: rgba(37, 211, 102, 0.2);
            border-color: rgba(37, 211, 102, 0.5);
        }

        #language-select {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 25px;
            padding: 8px 16px;
            font-size: 14px;
            color: #1b1b18;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #language-select:hover {
            border-color: rgba(37, 211, 102, 0.4);
            background: rgba(37, 211, 102, 0.05);
        }

        .dark #language-select {
            background: rgba(22, 22, 21, 0.9);
            border: 1px solid rgba(37, 211, 102, 0.3);
            color: #EDEDEC;
        }

        .dark #language-select:hover {
            background: rgba(37, 211, 102, 0.1);
            border-color: rgba(37, 211, 102, 0.5);
        }

        /* Floating particles background effect */
        .presentation-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(37, 211, 102, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(7, 94, 84, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(37, 211, 102, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* Slide content should be above background */
        .slide > * {
            position: relative;
            z-index: 1;
        }

        /* Improved text shadows and effects */
        .slide h1, .slide h2 {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Smooth hover effects for interactive elements */
        .problem-card, .feature-column {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Progress indicator */
        .slide-progress {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .progress-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .progress-dot.active {
            background: #25D366;
            transform: scale(1.2);
        }

        .progress-dot:hover {
            background: rgba(37, 211, 102, 0.7);
        }
    </style>
</head>
<body>

    <div class="presentation-container w-full h-screen bg-white dark:bg-gray-900 overflow-hidden relative">
        <div id="slide1" class="slide">
            <div class="text-center fade-in-up" style="animation-delay: 0.2s;">
                <div style="margin-bottom: 2rem;">
                    <img src="WhaMart_Logo.png" alt="Whamart Logo" style="height: 120px; margin: 0 auto; filter: drop-shadow(0 4px 8px rgba(37, 211, 102, 0.3));">
                </div>
                <h1 data-key="title" style="margin-bottom: 1rem;">Whamart</h1>
                <div style="display: inline-block; padding: 12px 24px; background: rgba(37, 211, 102, 0.1); border-radius: 50px; margin-bottom: 2rem; border: 1px solid rgba(37, 211, 102, 0.2);">
                    <h2 style="font-size: 1.5rem; margin: 0; color: #25D366; font-weight: 600;" data-key="subtitle">Empowering Bharat’s Local Businesses Digitally</h2>
                </div>
                <p style="font-size: 1.25rem; color: rgba(255, 255, 255, 0.9); font-weight: 500; text-shadow: 0 2px 4px rgba(0,0,0,0.3);" data-key="byline">Presented by Karan Solanki – Founder, Whamart</p>
            </div>
        </div>

        <div class="slide" id="slide2">
            <div class="container">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="display: inline-block; padding: 8px 16px; background: rgba(37, 211, 102, 0.1); color: #25D366; border-radius: 50px; font-size: 14px; font-weight: 600; margin-bottom: 16px;">
                        Problem Statement
                    </div>
                    <h2 data-key="problemTitle">The Problem We’re Solving</h2>
                </div>
                <div class="grid">
                    <!-- Card 1 -->
                    <div class="problem-card fade-in-up" style="animation-delay: 0.1s;">
                        <i class="fas fa-store-slash icon"></i>
                        <p style="font-size: 1.1rem; font-weight: 500; color: #1b1b18;" data-key="problem1">8 Cr+ local businesses still offline.</p>
                    </div>
                    <!-- Card 2 -->
                    <div class="problem-card fade-in-up" style="animation-delay: 0.2s;">
                        <i class="fas fa-puzzle-piece icon"></i>
                        <p style="font-size: 1.1rem; font-weight: 500; color: #1b1b18;" data-key="problem2">Existing ecommerce tools are expensive or complicated.</p>
                    </div>
                    <!-- Card 3 -->
                    <div class="problem-card fade-in-up" style="animation-delay: 0.3s;">
                        <i class="fab fa-whatsapp icon"></i>
                        <p style="font-size: 1.1rem; font-weight: 500; color: #1b1b18;" data-key="problem3">WhatsApp API is not affordable or beginner-friendly.</p>
                    </div>
                    <!-- Card 4 -->
                    <div class="problem-card fade-in-up" style="animation-delay: 0.4s;">
                        <i class="fas fa-credit-card icon"></i>
                        <p style="font-size: 1.1rem; font-weight: 500; color: #1b1b18;" data-key="problem4">Payment gateway setup is costly & takes time.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide3">
            <div class="container">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="display: inline-block; padding: 8px 16px; background: rgba(37, 211, 102, 0.1); color: #25D366; border-radius: 50px; font-size: 14px; font-weight: 600; margin-bottom: 16px;">
                        Our Solution
                    </div>
                    <h2 data-key="featuresTitle">Whamart – Simple, Powerful, Affordable</h2>
                </div>
                <div class="feature-container">
                    <!-- Column 1 -->
                    <div class="feature-column fade-in-up" style="animation-delay: 0.1s;">
                        <ul>
                            <li>
                                <i class="fas fa-store icon"></i>
                                <span data-key="feature1">Mini WhatsApp-style Store</span>
                            </li>
                            <li>
                                <i class="fas fa-sitemap icon"></i>
                                <span data-key="feature2">Drag & Drop Chat Flow Builder</span>
                            </li>
                            <li>
                                <i class="fas fa-robot icon"></i>
                                <span data-key="feature3">24x7 Automation</span>
                            </li>
                            <li>
                                <i class="fas fa-rupee-sign icon"></i>
                                <span data-key="feature4">UPI-based Instant Payments</span>
                            </li>
                        </ul>
                    </div>
                    <!-- Column 2 -->
                    <div class="feature-column fade-in-up" style="animation-delay: 0.2s;">
                        <ul>
                            <li>
                                <i class="fas fa-book-open icon"></i>
                                <span data-key="feature5">Product & Service Catalog</span>
                            </li>
                            <li>
                                <i class="fas fa-file-pdf icon"></i>
                                <span data-key="feature6">Auto Bill PDF Generator</span>
                            </li>
                            <li>
                                <i class="fas fa-check-circle icon"></i>
                                <span data-key="feature7">Verified Blue Tick</span>
                            </li>
                            <li>
                                <i class="fas fa-mobile-alt icon"></i>
                                <span data-key="feature8">No App Required</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide4">
            <h2 data-key="audienceTitle">Our Target Audience & TAM</h2>
            <div class="content-wrapper flex items-center justify-around w-full max-w-5xl">
                <div class="chart-container">
                    <canvas id="tamChart"></canvas>
                </div>
                <div class="segments">
                    <div class="segment"><i class="icon fas fa-store"></i><span data-key="segment1">Kirana stores</span></div>
                    <div class="segment"><i class="icon fas fa-user-tie"></i><span data-key="segment2">Freelancers</span></div>
                    <div class="segment"><i class="icon fas fa-cut"></i><span data-key="segment3">Tailors</span></div>
                    <div class="segment"><i class="icon fas fa-tags"></i><span data-key="segment4">Digital Sellers</span></div>
                </div>
            </div>
            <p class="focus-paragraph" data-key="focusParagraph">Our early focus is on Surat, Tier 2/3 cities where WhatsApp is already the most used business tool.</p>
        </div>

        <div class="slide" id="slide5">
            <h2 data-key="competitionTitle">How We’re Different from the Competition</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th data-key="competitor">Competitor</th>
                        <th data-key="strength">Strength</th>
                        <th data-key="weakness">Weakness</th>
                        <th data-key="advantage">Whamart Advantage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td data-key="comp1">Shopify</td>
                        <td data-key="str1">Robust</td>
                        <td data-key="weak1">High cost, complex</td>
                        <td data-key="adv1">Simple chat-based mini-store</td>
                    </tr>
                    <tr>
                        <td data-key="comp2">Swiggy Mini</td>
                        <td data-key="str2">Delivery focus</td>
                        <td data-key="weak2">Only food category</td>
                        <td data-key="adv2">Multi-category</td>
                    </tr>
                    <tr>
                        <td data-key="comp3">Dukaan</td>
                        <td data-key="str3">Easy setup</td>
                        <td data-key="weak3">Needs payment gateway</td>
                        <td data-key="adv3">Zero-fee UPI</td>
                    </tr>
                    <tr>
                        <td data-key="comp4">Digital Showroom</td>
                        <td data-key="str4">Visual catalogs</td>
                        <td data-key="weak4">No automation</td>
                        <td data-key="adv4">Full chat flow</td>
                    </tr>
                    <tr>
                        <td data-key="comp5">WhatsApp API</td>
                        <td data-key="str5">Official</td>
                        <td data-key="weak5">Expensive + KYC</td>
                        <td data-key="adv5">Instant setup</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="slide" id="slide6">
            <h2 data-key="businessModelTitle">How We Make Money</h2>
            <div class="business-model-container">
                <div class="model-item">
                    <h3 data-key="model1_title">Annual Plan</h3>
                    <p data-key="model1_desc">₹1500 – ₹3000</p>
                </div>
                <div class="model-item">
                    <h3 data-key="model2_title">Add-on Services (Phase 2)</h3>
                    <p data-key="model2_desc">AI Chatbot, CRM</p>
                </div>
                <div class="model-item">
                    <h3 data-key="model3_title">No Commission</h3>
                    <p data-key="model3_desc">100% Vendor Profit</p>
                </div>
            </div>
            <div class="revenue-chart-container w-1/2">
                <canvas id="revenueChart"></canvas>
            </div>
            <p class="revenue-projection" data-key="revenueProjection">Projected Revenue: ₹30 Lakh in 6 months from 2000+ users</p>
        </div>

        <div class="slide" id="slide7">
            <h2 data-key="capitalTitle">How We’ll Use ₹10 Lakh Capital</h2>
            <div class="capital-container">
                <div class="capital-chart-container">
                    <canvas id="capitalChart"></canvas>
                </div>
                <div class="mt-8 bg-whatsapp-default text-white p-6 rounded-lg text-2xl font-bold shadow-lg flex items-center justify-center">
                    <i class="fas fa-rocket"></i>
                    <span data-key="launchHighlight">Launch in 1 week after funding!</span>
                </div>
            </div>
        </div>

        <div class="slide" id="slide8">
            <h2 data-key="timelineTitle">Product is Ready to Launch</h2>
            <div class="timeline-container w-full max-w-5xl relative">
                <div class="timeline-milestone">
                    <div class="milestone-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="milestone-text" data-key="milestone1">MVP Ready</div>
                </div>
                <div class="timeline-milestone">
                    <div class="milestone-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="milestone-text" data-key="milestone2">7-day Trial Launched</div>
                </div>
                <div class="timeline-milestone">
                    <div class="milestone-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="milestone-text" data-key="milestone3">Influencer List Prepared</div>
                </div>
                <div class="timeline-milestone">
                    <div class="milestone-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="milestone-text" data-key="milestone4">Ads Strategy Finalized</div>
                </div>
                <div class="timeline-milestone">
                    <div class="milestone-icon"><i class="fas fa-rocket"></i></div>
                    <div class="milestone-text" data-key="milestone5">Launch in 1 Week post-funding</div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide9">
            <h2 data-key="investmentTitle">Investment Offer & Returns</h2>
            <div class="investment-container flex items-center justify-around w-full max-w-5xl">
                <div class="investment-points w-1/2 text-left">
                    <ul>
                        <li><span data-key="invPoint1"><b>Capital Raise:</b> ₹10 lakh (₹5 lakh entry possible)</span></li>
                        <li><span data-key="invPoint2"><b>3-month lock-in</b> with monthly update reports</span></li>
                        <li><span data-key="invPoint3"><b>Expected ROI:</b> <span class="highlight">30%+</span> in 6 months</span></li>
                        <li><span data-key="invPoint4"><b>Revenue projection:</b> ₹30L in 6 months</span></li>
                    </ul>
                </div>
                <div class="roi-chart-container">
                    <canvas id="roiChart"></canvas>
                </div>
            </div>
        </div>

        <div class="slide" id="slide10">
            <h2 data-key="whyWhamartTitle">Why Whamart?</h2>
            <div class="why-container max-w-3xl text-left">
                <ul>
                    <li><i class="fas fa-users"></i><span data-key="why1">Simple product for 8 Cr underserved vendors</span></li>
                    <li><i class="fas fa-rocket"></i><span data-key="why2">Ready-to-launch system</span></li>
                    <li><i class="fas fa-user-tie"></i><span data-key="why3">Founder-led focused execution</span></li>
                    <li><i class="fas fa-chart-line"></i><span data-key="why4">Low-cost, high-return</span></li>
                    <li><i class="fas fa-map-marker-alt"></i><span data-key="why5">Bharat’s Tier 2/3 market focus</span></li>
                    <li><i class="fas fa-sign-out-alt"></i><span data-key="why6">Exit option post 6 months</span></li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide11">
            <h2 data-key="finalTitle">Let’s Build Bharat’s Future Together</h2>
            <div class="final-container text-white">
                <div class="final-summary mt-6">
                    <h3 data-key="finalRaise">Seeking ₹10 Lakh for 6 Months</h3>
                </div>
                <div class="contact-info mt-10 text-xl">
                    <p><i class="fas fa-phone"></i> <span data-key="phone">+91 99999 99999</span></p>
                    <p><i class="fab fa-whatsapp"></i> <span data-key="whatsapp">+91 99999 99999</span></p>
                    <p><i class="fas fa-envelope"></i> <span data-key="email"><EMAIL></span></p>
                </div>
                <div class="thank-you" data-key="thankYou">Thank You</div>
            </div>
        </div>

        <div class="slide" id="slide12">
            <h2 data-key="thankYou">Thank You!</h2>
            <p data-key="questions">Any Questions?</p>
        </div>

        <!-- Controls -->
        <div class="absolute bottom-5 left-1/2 -translate-x-1/2 flex items-center gap-4 z-20">
            <button id="prev-btn" class="nav-btn bg-white/50 dark:bg-black/50 backdrop-blur-sm p-2 rounded-full text-gray-800 dark:text-gray-200 hover:bg-white/80 dark:hover:bg-black/80 transition-all">
                <i class="fas fa-chevron-left w-5 h-5"></i>
            </button>
            <select id="language-select" class="bg-white/50 dark:bg-black/50 backdrop-blur-sm border-none text-gray-800 dark:text-gray-200 rounded-full py-2 px-4 focus:ring-2 focus:ring-green-500">
                <option value="en">English</option>
                <option value="hi">Hindi</option>
                <option value="gu">Gujarati</option>
            </select>
            <button id="next-btn" class="nav-btn bg-white/50 dark:bg-black/50 backdrop-blur-sm p-2 rounded-full text-gray-800 dark:text-gray-200 hover:bg-white/80 dark:hover:bg-black/80 transition-all">
                <i class="fas fa-chevron-right w-5 h-5"></i>
            </button>
        </div>
        <div class="date absolute bottom-5 right-5 text-sm text-gray-500 dark:text-gray-400"></div>

        <!-- Progress indicator -->
        <div class="slide-progress" id="slide-progress"></div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const translations = {
            en: {
                title: 'Whamart',
                subtitle: 'Empowering Bharat’s Local Businesses Digitally',
                byline: 'Presented by Karan Solanki – Founder, Whamart',
                problemTitle: 'The Problem We’re Solving',
                problem1: '8 Cr+ local businesses still offline.',
                problem2: 'Existing ecommerce tools are expensive or complicated.',
                problem3: 'WhatsApp API is not affordable or beginner-friendly.',
                problem4: 'Payment gateway setup is costly & takes time.',
                featuresTitle: 'Whamart – Simple, Powerful, Affordable',
                feature1: 'Mini WhatsApp-style Store',
                feature2: 'Drag & Drop Chat Flow Builder',
                feature3: '24x7 Automation',
                feature4: 'UPI-based Instant Payments',
                feature5: 'Product & Service Catalog',
                feature6: 'Auto Bill PDF Generator',
                feature7: 'Verified Blue Tick',
                feature8: 'No App Required',
                audienceTitle: 'Our Target Audience & TAM',
                segment1: 'Kirana stores',
                segment2: 'Freelancers',
                segment3: 'Tailors',
                segment4: 'Digital Sellers',
                focusParagraph: 'Our early focus is on Surat, Tier 2/3 cities where WhatsApp is already the most used business tool.',
                competitionTitle: 'How We’re Different from the Competition',
                competitor: 'Competitor',
                strength: 'Strength',
                weakness: 'Weakness',
                advantage: 'Whamart Advantage',
                comp1: 'Shopify', str1: 'Robust', weak1: 'High cost, complex', adv1: 'Simple chat-based mini-store',
                comp2: 'Swiggy Mini', str2: 'Delivery focus', weak2: 'Only food category', adv2: 'Multi-category',
                comp3: 'Dukaan', str3: 'Easy setup', weak3: 'Needs payment gateway', adv3: 'Zero-fee UPI',
                comp4: 'Digital Showroom', str4: 'Visual catalogs', weak4: 'No automation', adv4: 'Full chat flow',
                comp5: 'WhatsApp API', str5: 'Official', weak5: 'Expensive + KYC', adv5: 'Instant setup',
                businessModelTitle: 'How We Make Money',
                model1_title: 'Annual Plan', model1_desc: '₹1500 – ₹3000',
                model2_title: 'Add-on Services (Phase 2)', model2_desc: 'AI chatbot, CRM',
                model3_title: 'No commission', model3_desc: '100% vendor profit',
                revenueProjection: 'Projected Revenue: ₹30L in 6 months from 2000+ users',
                capitalTitle: 'How We’ll Use ₹10 Lakh Capital',
                cat1: 'Influencer Marketing', cat2: 'Meta + YouTube Ads', cat3: 'Local Banners',
                cat4: 'Server & Tech Infra', cat5: 'Team Salaries', cat6: 'Legal/Admin',
                launchHighlight: 'Launch in 1 week after funding!',
                timelineTitle: 'Product is Ready to Launch',
                milestone1: 'MVP Ready', milestone2: '7-day Trial Launched', milestone3: 'Influencer List Prepared',
                milestone4: 'Ads Strategy Finalized', milestone5: 'Launch in 1 Week post-funding',
                investmentTitle: 'Investment Offer & Returns',
                invPoint1: 'Capital Raise: ₹10 lakh (₹5 lakh entry possible)',
                invPoint2: '3-month lock-in with monthly update reports',
                invPoint3: 'Expected ROI: 30%+ in 6 months',
                invPoint4: 'Revenue projection: ₹30L in 6 months',
                investmentLabel: 'Investment (₹ Lakhs)',
                monthLabel: 'Month',
                whyWhamartTitle: 'Why Whamart?',
                why1: 'Simple product for 8 Cr underserved vendors',
                why2: 'Ready-to-launch system',
                why3: 'Founder-led focused execution',
                why4: 'Low-cost, high-return',
                why5: 'Bharat’s Tier 2/3 market focus',
                why6: 'Exit option post 6 months',
                finalTitle: 'Let’s Build Bharat’s Future Together',
                finalRaise: 'Seeking ₹10 Lakh for 6 Months',
                phone: '+91 99999 99999',
                whatsapp: '+91 99999 99999',
                email: '<EMAIL>',
                thankYou: 'Thank You'
            },
            hi: {
                title: 'व्हामार्ट',
                subtitle: 'भारत के स्थानीय व्यवसायों को डिजिटल रूप से सशक्त बनाना',
                byline: 'करण सोलंकी द्वारा प्रस्तुत - संस्थापक, व्हामार्ट',
                problemTitle: 'हम जिस समस्या का समाधान कर रहे हैं',
                problem1: '8 करोड़+ स्थानीय व्यवसाय अभी भी ऑफ़लाइन हैं।',
                problem2: 'मौजूदा ई-कॉमर्स उपकरण महंगे या जटिल हैं।',
                problem3: 'व्हाट्सएप एपीआई सस्ती या शुरुआती-अनुकूल नहीं है।',
                problem4: 'पेमेंट गेटवे सेटअप महंगा है और इसमें समय लगता है।',
                featuresTitle: 'व्हामार्ट - सरल, शक्तिशाली, किफायती',
                feature1: 'मिनी व्हाट्सएप-स्टाइल स्टोर',
                feature2: 'ड्रैग एंड ड्रॉप चैट फ्लो बिल्डर',
                feature3: '24x7 ऑटोमेशन',
                feature4: 'UPI-आधारित तत्काल भुगतान',
                feature5: 'उत्पाद और सेवा कैटलॉग',
                feature6: 'ऑटो बिल पीडीएफ जेनरेटर',
                feature7: 'सत्यापित ब्लू टिक',
                feature8: 'किसी ऐप की आवश्यकता नहीं',
                audienceTitle: 'हमारे लक्षित दर्शक और TAM',
                segment1: 'किराना स्टोर',
                segment2: 'फ्रीलांसर',
                segment3: 'दर्जी',
                segment4: 'डिजिटल विक्रेता',
                focusParagraph: 'हमारा शुरुआती फोकस सूरत, टियर 2/3 शहरों पर है जहां व्हाट्सएप पहले से ही सबसे ज्यादा इस्तेमाल किया जाने वाला बिजनेस टूल है।',
                competitionTitle: 'हम प्रतिस्पर्धा से कैसे अलग हैं',
                competitor: 'प्रतियोगी',
                strength: 'ताकत',
                weakness: 'कमजोरी',
                advantage: 'व्हामार्ट का फायदा',
                comp1: 'Shopify', str1: 'मजबूत', weak1: 'उच्च लागत, जटिल', adv1: 'सरल चैट-आधारित मिनी-स्टोर',
                comp2: 'Swiggy Mini', str2: 'डिलीवरी फोकस', weak2: 'केवल खाद्य श्रेणी', adv2: 'बहु-श्रेणी',
                comp3: 'Dukaan', str3: 'आसान सेटअप', weak3: 'पेमेंट गेटवे की जरूरत', adv3: 'शून्य-शुल्क UPI',
                comp4: 'Digital Showroom', str4: 'विज़ुअल कैटलॉग', weak4: 'कोई ऑटोमेशन नहीं', adv4: 'पूर्ण चैट प्रवाह',
                comp5: 'WhatsApp API', str5: 'आधिकारिक', weak5: 'महंगा + KYC', adv5: 'तत्काल सेटअप',
                businessModelTitle: 'हम पैसे कैसे कमाते हैं',
                model1_title: 'वार्षिक योजना', model1_desc: '₹1500 – ₹3000',
                model2_title: 'ऐड-ऑन सेवाएं (चरण 2)', model2_desc: 'एआई चैटबॉट, सीआरएम',
                model3_title: 'कोई कमीशन नहीं', model3_desc: '100% विक्रेता लाभ',
                revenueProjection: 'अनुमानित राजस्व: 2000+ उपयोगकर्ताओं से 6 महीनों में ₹30 लाख',
                capitalTitle: 'हम ₹10 लाख पूंजी का उपयोग कैसे करेंगे',
                cat1: 'इन्फ्लुएंसर मार्केटिंग', cat2: 'मेटा + यूट्यूब विज्ञापन', cat3: 'स्थानीय बैनर',
                cat4: 'सर्वर और टेक इंफ्रा', cat5: 'टीम वेतन', cat6: 'कानूनी/प्रशासन',
                launchHighlight: 'फंडिंग के 1 सप्ताह बाद लॉन्च!',
                timelineTitle: 'उत्पाद लॉन्च के लिए तैयार है',
                milestone1: 'एमवीपी तैयार', milestone2: '7-दिवसीय परीक्षण लॉन्च किया गया', milestone3: 'प्रभावशाली सूची तैयार',
                milestone4: 'विज्ञापन रणनीति को अंतिम रूप दिया गया', milestone5: 'फंडिंग के बाद 1 सप्ताह में लॉन्च',
                investmentTitle: 'निवेश प्रस्ताव और रिटर्न',
                invPoint1: 'पूंजी जुटाना: ₹10 लाख (₹5 लाख प्रवेश संभव)',
                invPoint2: 'मासिक अपडेट रिपोर्ट के साथ 3 महीने का लॉक-इन',
                invPoint3: 'अपेक्षित ROI: 6 महीने में 30%+',
                invPoint4: 'राजस्व प्रक्षेपण: 6 महीने में ₹30 लाख',
                investmentLabel: 'निवेश (₹ लाख)',
                monthLabel: 'महीना',
                whyWhamartTitle: 'व्हामार्ट क्यों?',
                why1: '8 करोड़ असेवित विक्रेताओं के लिए सरल उत्पाद',
                why2: 'लॉन्च के लिए तैयार प्रणाली',
                why3: 'संस्थापक के नेतृत्व में केंद्रित निष्पादन',
                why4: 'कम लागत, उच्च रिटर्न',
                why5: 'भारत के टियर 2/3 बाजार पर ध्यान केंद्रित',
                why6: '6 महीने के बाद बाहर निकलने का विकल्प',
                finalTitle: 'आइए मिलकर भारत का भविष्य बनाएं',
                finalRaise: '6 महीने के लिए ₹10 लाख की मांग',
                phone: '+91 99999 99999',
                whatsapp: '+91 99999 99999',
                email: '<EMAIL>',
                thankYou: 'धन्यवाद'
            },
            gu: {
                title: 'વ્હામર્ટ',
                subtitle: 'ભારતના સ્થાનિક વ્યવસાયોને ડિજિટલી રીતે સશક્ત બનાવવું',
                byline: 'કરણ સોલંકી દ્વારા પ્રસ્તુત - સ્થાપક, વ્હામર્ટ',
                problemTitle: 'અમે જે સમસ્યા હલ કરી રહ્યા છીએ',
                problem1: '8 કરોડ+ સ્થાનિક વ્યવસાયો હજુ પણ ઑફલાઇન છે.',
                problem2: 'હાલના ઈકોમર્સ સાધનો ખર્ચાળ અથવા જટિલ છે.',
                problem3: 'વોટ્સએપ એપીઆઈ સસ્તું કે શિખાઉ માણસ માટે અનુકૂળ નથી.',
                problem4: 'પેમેન્ટ ગેટવે સેટઅપ ખર્ચાળ છે અને સમય લે છે।',
                featuresTitle: 'વ્હામર્ટ - સરળ, શક્તિશાળી, સસ્તું',
                feature1: 'મિની વોટ્સએપ-સ્ટાઇલ સ્ટોર',
                feature2: 'ડ્રેગ એન્ડ ડ્રોપ ચેટ ફ્લો બિલ્ડર',
                feature3: '24x7 ઓટોમેશન',
                feature4: 'UPI-આધારિત ત્વરિત ચુકવણીઓ',
                feature5: 'ઉત્પાદન અને સેવા કેટલોગ',
                feature6: 'ઓટો બિલ પીડીએફ જનરેટર',
                feature7: 'ચકાસાયેલ બ્લુ ટિક',
                feature8: 'કોઈ એપ્લિકેશનની જરૂર નથી',
                audienceTitle: 'અમારા લક્ષ્ય પ્રેક્ષકો અને TAM',
                segment1: 'કરિયાણાની દુકાનો',
                segment2: 'ફ્રીલાન્સર્સ',
                segment3: 'દરજીઓ',
                segment4: 'ડિજિટલ વિક્રેતાઓ',
                focusParagraph: 'અમારું પ્રારંભિક ધ્યાન સુરત, ટાયર 2/3 શહેરો પર છે જ્યાં વોટ્સએપ પહેલેથી જ સૌથી વધુ ઉપયોગમાં લેવાતું બિઝનેસ ટૂલ છે.',
                competitionTitle: 'અમે સ્પર્ધાથી કેવી રીતે અલગ છીએ',
                competitor: 'પ્રતિસ્પર્ધી',
                strength: 'શક્તિ',
                weakness: 'નબળાઈ',
                advantage: 'વ્હામર્ટનો ફાયદો',
                comp1: 'Shopify', str1: 'મજબૂત', weak1: 'ઊંચી કિંમત, જટિલ', adv1: 'સરળ ચેટ-આધારિત મીની-સ્ટોર',
                comp2: 'Swiggy Mini', str2: 'ડિલિવરી ફોકસ', weak2: 'ફક્ત ફૂડ કેટેગરી', adv2: 'બહુ-શ્રેણી',
                comp3: 'Dukaan', str3: 'સરળ સેટઅપ', weak3: 'પેમેન્ટ ગેટવેની જરૂર', adv3: 'શૂન્ય-ફી UPI',
                comp4: 'Digital Showroom', str4: 'વિઝ્યુઅલ કેટલોગ', weak4: 'કોઈ ઓટોમેશન નથી', adv4: 'સંપૂર્ણ ચેટ ફ્લો',
                comp5: 'WhatsApp API', str5: 'સત્તાવાર', weak5: 'ખર્ચાળ + KYC', adv5: 'ત્વરિત સેટઅપ',
                businessModelTitle: 'અમે પૈસા કેવી રીતે બનાવીએ છીએ',
                model1_title: 'વાર્ષિક યોજના', model1_desc: '₹1500 – ₹3000',
                model2_title: 'એડ-ઓન સેવાઓ (તબક્કો 2)', model2_desc: 'એઆઈ ચેટબોટ, સીઆરએમ',
                model3_title: 'કોઈ કમિશન નહીં', model3_desc: '100% વિક્રેતા નફો',
                revenueProjection: 'પ્રોજેક્ટેડ રેવન્યુ: 2000+ વપરાશકર્તાઓ પાસેથી 6 મહિનામાં ₹30 લાખ',
                capitalTitle: 'અમે ₹10 લાખની મૂડીનો ઉપયોગ કેવી રીતે કરીશું',
                cat1: 'પ્રભાવક માર્કેટિંગ', cat2: 'મેટા + યુટ્યુબ જાહેરાતો', cat3: 'સ્થાનિક બેનરો',
                cat4: 'સર્વર અને ટેક ઇન્ફ્રા', cat5: 'ટીમનો પગાર', cat6: 'કાનૂની/વહીવટ',
                launchHighlight: 'ફંડિંગ પછી 1 અઠવાડિયામાં લોન્ચ!',
                timelineTitle: 'ઉત્પાદન લોન્ચ કરવા માટે તૈયાર છે',
                milestone1: 'MVP તૈયાર', milestone2: '7-દિવસની અજમાયશ શરૂ થઈ', milestone3: 'પ્રભાવકોની યાદી તૈયાર',
                milestone4: 'જાહેરાત વ્યૂહરચનાને અંતિમ સ્વરૂપ અપાયું', milestone5: 'ફંડિંગ પછી 1 અઠવાડિયામાં લોન્ચ',
                investmentTitle: 'રોકાણ ઓફર અને વળતર',
                invPoint1: 'મૂડી વધારો: ₹10 લાખ (₹5 લાખ પ્રવેશ શક્ય)',
                invPoint2: 'માસિક અપડેટ રિપોર્ટ્સ સાથે 3-મહિનાનો લોક-ઇન',
                invPoint3: 'અપેક્ષિત ROI: 6 મહિનામાં 30%+',
                invPoint4: 'આવક પ્રક્ષેપણ: 6 મહિનામાં ₹30 લાખ',
                investmentLabel: 'રોકાણ (₹ લાખ)',
                monthLabel: 'મહિનો',
                whyWhamartTitle: 'વ્હામર્ટ શા માટે?',
                why1: '8 કરોડ વંચિત વિક્રેતાઓ માટે સરળ ઉત્પાદન',
                why2: 'લોન્ચ કરવા માટે તૈયાર સિસ્ટમ',
                why3: 'સ્થાપક-આગેવાની હેઠળ કેન્દ્રિત અમલ',
                why4: 'ઓછી કિંમત, ઉચ્ચ વળતર',
                why5: 'ભારતના ટાયર 2/3 બજાર પર ધ્યાન કેન્દ્રિત',
                why6: '6 મહિના પછી બહાર નીકળવાનો વિકલ્પ',
                finalTitle: 'ચાલો સાથે મળીને ભારતનું ભવિષ્ય બનાવીએ',
                finalRaise: '6 મહિના માટે ₹10 લાખની માંગણી',
                phone: '+91 99999 99999',
                whatsapp: '+91 99999 99999',
                email: '<EMAIL>',
                thankYou: 'આભાર'
            }
        };

    let currentSlide = 0;
    const slides = document.querySelectorAll('.slide');
    const totalSlides = slides.length;
    let tamChart = null;
    let revenueChart = null;
    let roiChart = null;
    let capitalChart = null;
    let isTransitioning = false;

    // Renamed for clarity
    function renderTAMChart() {
        if (tamChart) {
            tamChart.destroy();
        }
        const ctx = document.getElementById('tamChart').getContext('2d');
        const currentLang = document.getElementById('language-select').value;
        tamChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: [
                    translations[currentLang].tamSegment1,
                    translations[currentLang].tamSegment2,
                    translations[currentLang].tamSegment3,
                    translations[currentLang].tamSegment4
                ],
                datasets: [{
                    label: 'MSME Segments',
                    data: [25, 25, 25, 25],
                    backgroundColor: [
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(75, 192, 192, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: translations[currentLang].tamTitle,
                        font: { size: 16 }
                    }
                }
            }
        });
    }

    function renderRevenueChart() {
        if (revenueChart) {
            revenueChart.destroy();
        }
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const currentLang = document.getElementById('language-select').value;
        revenueChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    `${translations[currentLang].monthLabel} 1`,
                    `${translations[currentLang].monthLabel} 2`,
                    `${translations[currentLang].monthLabel} 3`,
                    `${translations[currentLang].monthLabel} 4`,
                    `${translations[currentLang].monthLabel} 5`,
                    `${translations[currentLang].monthLabel} 6`
                ],
                datasets: [{
                    label: 'Projected Revenue',
                    data: [2, 4, 8, 15, 22, 30], // Revenue in Lakhs
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: `${translations[currentLang].investmentLabel}`
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: translations[currentLang].revenueProjection
                    }
                }
            }
        });
    }

    function renderROIChart() {
        if (roiChart) {
            roiChart.destroy();
        }
        const ctx = document.getElementById('roiChart').getContext('2d');
        const currentLang = document.getElementById('language-select').value;
        roiChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    `${translations[currentLang].monthLabel} 0`,
                    `${translations[currentLang].monthLabel} 1`,
                    `${translations[currentLang].monthLabel} 2`,
                    `${translations[currentLang].monthLabel} 3`,
                    `${translations[currentLang].monthLabel} 4`,
                    `${translations[currentLang].monthLabel} 5`,
                    `${translations[currentLang].monthLabel} 6`
                ],
                datasets: [{
                    label: translations[currentLang].investmentLabel,
                    data: [10, 10.5, 11, 11.5, 12, 12.5, 13],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: translations[currentLang].investmentLabel
                        }
                    },
                    x: {
                       title: {
                            display: true,
                            text: translations[currentLang].monthLabel
                       }
                    }
                }
            }
        });
    }

    function renderCapitalChart() {
        if (capitalChart) {
            capitalChart.destroy();
        }
        const ctx = document.getElementById('capitalChart').getContext('2d');
        const currentLang = document.getElementById('language-select').value;
        capitalChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [
                    translations[currentLang].cat1,
                    translations[currentLang].cat2,
                    translations[currentLang].cat3,
                    translations[currentLang].cat4,
                    translations[currentLang].cat5,
                    translations[currentLang].cat6
                ],
                datasets: [{
                    label: 'Budget Allocation',
                    data: [250000, 200000, 100000, 100000, 300000, 50000],
                    backgroundColor: ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0', '#9966ff', '#ff9f40'],
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'top' },
                    title: {
                        display: true,
                        text: translations[currentLang].capitalTitle
                    }
                }
            }
        });
    }

    function showSlide(index, direction = 'next') {
        if (isTransitioning) return;
        isTransitioning = true;

        const currentSlideEl = slides[currentSlide];
        const nextSlideEl = slides[index];

        // Remove active class from current slide
        if (currentSlideEl) {
            currentSlideEl.classList.remove('active');
            currentSlideEl.classList.add(direction === 'next' ? 'prev' : 'next');
        }

        // Add active class to next slide with animation
        if (nextSlideEl) {
            nextSlideEl.style.display = 'flex';
            nextSlideEl.classList.add('active');

            // Add entrance animation
            const animationClass = direction === 'next' ? 'slide-enter-right' : 'slide-enter-left';
            nextSlideEl.classList.add(animationClass);

            // Trigger animations for child elements
            const animatedElements = nextSlideEl.querySelectorAll('.fade-in-up');
            animatedElements.forEach((el, i) => {
                el.style.animationDelay = `${0.1 + (i * 0.1)}s`;
                el.classList.add('fade-in-up');
            });
        }

        // Clean up after animation
        setTimeout(() => {
            slides.forEach((slide, i) => {
                if (i !== index) {
                    slide.style.display = 'none';
                    slide.classList.remove('active', 'prev', 'next', 'slide-enter-right', 'slide-enter-left');
                }
            });
            if (nextSlideEl) {
                nextSlideEl.classList.remove('slide-enter-right', 'slide-enter-left');
            }
            isTransitioning = false;
        }, 600);

        // Update navigation buttons
        document.getElementById('prev-btn').style.display = index === 0 ? 'none' : 'inline-block';
        document.getElementById('next-btn').style.display = index === totalSlides - 1 ? 'none' : 'inline-block';

        const activeSlideId = slides[index] ? slides[index].id : null;
        // Render charts after transition
        setTimeout(() => {
            switch (activeSlideId) {
                case 'slide4': // TAM Chart
                    renderTAMChart();
                    break;
                case 'slide6': // Revenue Chart
                    renderRevenueChart();
                    break;
                case 'slide7': // Capital Chart
                    renderCapitalChart();
                    break;
                case 'slide9': // ROI Chart
                    renderROIChart();
                    break;
                default:
                    // No chart for other slides
                    break;
            }
        }, 300);
    }

    function changeLanguage(lang) {
        const langData = translations[lang];
        document.querySelectorAll('[data-key]').forEach(el => {
            const key = el.dataset.key;
            if (langData[key]) {
                // This is a robust way to change text without destroying child elements like icons.
                // It finds the first text node in the element and updates it.
                // If no text node, it updates the whole innerHTML (for simple elements).
                let textNodeFound = false;
                for (const node of el.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                        node.textContent = langData[key];
                        textNodeFound = true;
                        break;
                    }
                }
                if (!textNodeFound) {
                    el.innerHTML = langData[key];
                }
            }
        });

        // Re-render active chart with new language labels
        const activeSlideId = slides[currentSlide] ? slides[currentSlide].id : null;
         switch (activeSlideId) {
            case 'slide4':
                if(tamChart) renderTAMChart();
                break;
            case 'slide6':
                if(revenueChart) renderRevenueChart();
                break;
            case 'slide7':
                if(capitalChart) renderCapitalChart();
                break;
            case 'slide9':
                if(roiChart) renderROIChart();
                break;
        }
    }

    document.getElementById('language-select').addEventListener('change', (event) => {
        changeLanguage(event.target.value);
    });

    document.getElementById('next-btn').addEventListener('click', () => {
        if (currentSlide < totalSlides - 1 && !isTransitioning) {
            const prevSlide = currentSlide;
            currentSlide++;
            showSlide(currentSlide, 'next');
        }
    });

    document.getElementById('prev-btn').addEventListener('click', () => {
        if (currentSlide > 0 && !isTransitioning) {
            const prevSlide = currentSlide;
            currentSlide--;
            showSlide(currentSlide, 'prev');
        }
    });

    // Add keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowRight' || e.key === ' ') {
            e.preventDefault();
            if (currentSlide < totalSlides - 1 && !isTransitioning) {
                currentSlide++;
                showSlide(currentSlide, 'next');
            }
        } else if (e.key === 'ArrowLeft') {
            e.preventDefault();
            if (currentSlide > 0 && !isTransitioning) {
                currentSlide--;
                showSlide(currentSlide, 'prev');
            }
        }
    });



    document.querySelector('.date').textContent = new Date().toLocaleDateString('en-GB');

    // Create progress indicator
    function createProgressIndicator() {
        const progressContainer = document.getElementById('slide-progress');
        progressContainer.innerHTML = '';

        for (let i = 0; i < totalSlides; i++) {
            const dot = document.createElement('div');
            dot.className = 'progress-dot';
            if (i === 0) dot.classList.add('active');

            dot.addEventListener('click', () => {
                if (i !== currentSlide && !isTransitioning) {
                    const direction = i > currentSlide ? 'next' : 'prev';
                    currentSlide = i;
                    showSlide(currentSlide, direction);
                    updateProgressIndicator();
                }
            });

            progressContainer.appendChild(dot);
        }
    }

    function updateProgressIndicator() {
        const dots = document.querySelectorAll('.progress-dot');
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === currentSlide);
        });
    }

    // Initial setup
    slides.forEach((slide, i) => {
        if (i === 0) {
            slide.style.display = 'flex';
            slide.classList.add('active');
        } else {
            slide.style.display = 'none';
            slide.classList.remove('active');
        }
    });

    // Hide prev button initially
    document.getElementById('prev-btn').style.display = 'none';

    // Create progress indicator
    createProgressIndicator();

    changeLanguage('en');
    });
</script>

</body>
</html>
