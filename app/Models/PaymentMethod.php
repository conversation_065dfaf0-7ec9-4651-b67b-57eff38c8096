<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'type',
        'name',
        'is_default',
        'is_active',
        'upi_id',
        'qr_code_url',
        'account_name',
        'account_number',
        'bank_name',
        'ifsc_code',
        'instructions',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
