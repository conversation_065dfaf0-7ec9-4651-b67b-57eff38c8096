import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Link } from 'react-router-dom';
import {
  UserIcon,
  GlobeAltIcon,
  BellIcon,
  CheckCircleIcon,
  CreditCardIcon,
  ArrowUpRightIcon
} from '@heroicons/react/24/outline';
import { subscriptionAPI } from '../../services/api';
import '../../styles/vendor-dashboard.css';

export default function VendorSettings() {
  const { currentUser } = useAuth();

  const [formData, setFormData] = useState({
    name: currentUser?.name || '',
    email: currentUser?.email || '',
    phone: '+1234567890',
    currency: 'INR',
    language: 'en',
    notifications: true,
  });

  const [successMessage, setSuccessMessage] = useState('');

  // Subscription plan data
  const [subscriptionPlan, setSubscriptionPlan] = useState({
    name: 'Loading...',
    startDate: new Date(),
    endDate: new Date(),
    features: [],
    price: '₹0',
    originalPrice: '₹0'
  });

  const [loading, setLoading] = useState(true);

  // Fetch current subscription data
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        const response = await subscriptionAPI.getCurrentSubscription();

        if (response.data.subscription) {
          const subscription = response.data.subscription;
          setSubscriptionPlan({
            name: subscription.plan.name,
            startDate: subscription.startDate,
            endDate: subscription.endDate,
            features: subscription.plan.features || [],
            price: `₹${subscription.amount}`,
            originalPrice: subscription.plan.originalPrice ? `₹${subscription.plan.originalPrice}` : null
          });
        } else if (response.data.defaultPlan) {
          // If no subscription found, use the default plan
          setSubscriptionPlan(response.data.defaultPlan);
        }
      } catch (error) {
        console.error('Error fetching subscription:', error);
        // Set default values if API fails
        setSubscriptionPlan({
          name: 'Free',
          startDate: new Date(),
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          features: ['20 products', '1 chat flow', 'Basic support', 'Verified badge'],
          price: '₹1,499',
          originalPrice: '₹1,999'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, you would save the settings to the backend
//     // Removed console.log

    // Show success message
    setSuccessMessage('Settings saved successfully!');

    // Hide success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 3000);
  };

  // Format date to display in a readable format
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Define sections for settings
  const settingsSections = [
    {
      id: 'personal',
      title: 'Personal Information',
      icon: UserIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
    },
    {
      id: 'subscription',
      title: 'Subscription Plan',
      icon: CreditCardIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
    },
    {
      id: 'preferences',
      title: 'Preferences',
      icon: GlobeAltIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: BellIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
    }
  ];

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Settings</div>
          <h1 className="welcome-title">
            Account Settings
          </h1>
          <p className="welcome-subtitle">
            Manage your account preferences and notification settings.
          </p>
        </div>
      </div>

      {successMessage && (
        <div className="card" style={{ marginBottom: '24px', backgroundColor: 'rgba(40, 167, 69, 0.1)' }}>
          <div style={{ padding: '16px', display: 'flex', alignItems: 'center' }}>
            <CheckCircleIcon style={{ color: '#28a745', width: '24px', height: '24px', marginRight: '12px' }} />
            <span style={{ color: '#28a745', fontWeight: '500' }}>{successMessage}</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Personal Information Section */}
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">Personal Information</h2>
          </div>
          <div className="card">
            <div style={{ padding: '24px', maxWidth: '100%' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '24px',
                marginBottom: '24px',
                maxWidth: '100%'
              }}>
                <div style={{ maxWidth: '100%' }}>
                  <label htmlFor="name" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '8px' }}>
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={formData.name}
                    onChange={handleChange}
                    style={{
                      width: '100%',
                      padding: '10px 12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(0,0,0,0.1)',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>

                <div style={{ maxWidth: '100%' }}>
                  <label htmlFor="email" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '8px' }}>
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    value={formData.email}
                    onChange={handleChange}
                    style={{
                      width: '100%',
                      padding: '10px 12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(0,0,0,0.1)',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ maxWidth: '100%' }}>
                <label htmlFor="phone" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '8px' }}>
                  Phone Number
                </label>
                <input
                  type="text"
                  name="phone"
                  id="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '10px 12px',
                    borderRadius: '8px',
                    border: '1px solid rgba(0,0,0,0.1)',
                    fontSize: '14px',
                    backgroundColor: '#F8F9FA',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Subscription Plan Section */}
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">Subscription Plan</h2>
          </div>
          <div className="card">
            <div style={{ padding: '24px', maxWidth: '100%' }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '20px'
              }}>
                {/* Current Plan Card */}
                {loading ? (
                  <div style={{
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    borderRadius: '12px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    textAlign: 'center'
                  }}>
                    <p style={{ fontSize: '16px', color: '#6C757D' }}>Loading subscription information...</p>
                  </div>
                ) : (
                  <div style={{
                    border: '1px solid rgba(37, 211, 102, 0.3)',
                    borderRadius: '12px',
                    padding: '20px',
                    backgroundColor: 'rgba(37, 211, 102, 0.05)'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '16px'
                    }}>
                      <div>
                        <h3 style={{
                          fontSize: '18px',
                          fontWeight: '700',
                          color: '#212529',
                          marginBottom: '4px'
                        }}>
                          Current Plan: {subscriptionPlan.name}
                        </h3>
                        <p style={{
                          fontSize: '14px',
                          color: '#6C757D',
                          margin: '0'
                        }}>
                          Valid from {formatDate(subscriptionPlan.startDate)} to {formatDate(subscriptionPlan.endDate)}
                        </p>
                      </div>
                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'flex-end'
                      }}>
                        <div style={{
                          fontSize: '20px',
                          fontWeight: '700',
                          color: '#212529'
                        }}>
                          {subscriptionPlan.price}
                        </div>
                        {subscriptionPlan.originalPrice && (
                          <div style={{
                            fontSize: '14px',
                            color: '#6C757D',
                            textDecoration: 'line-through'
                          }}>
                            {subscriptionPlan.originalPrice}
                          </div>
                        )}
                      </div>
                    </div>

                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                      gap: '12px',
                      marginBottom: '20px'
                    }}>
                      {subscriptionPlan.features.map((feature, index) => (
                        <div key={index} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}>
                          <CheckCircleIcon style={{
                            width: '16px',
                            height: '16px',
                            color: '#25D366'
                          }} />
                          <span style={{
                            fontSize: '14px',
                            color: '#212529'
                          }}>
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>

                    <Link to="/vendor/pricing" style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '10px 20px',
                      backgroundColor: '#25D366',
                      color: 'white',
                      borderRadius: '8px',
                      textDecoration: 'none',
                      fontWeight: '600',
                      fontSize: '14px',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}>
                      Upgrade Plan
                      <ArrowUpRightIcon style={{
                        width: '16px',
                        height: '16px'
                      }} />
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Preferences Section */}
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">Preferences</h2>
          </div>
          <div className="card">
            <div style={{ padding: '24px', maxWidth: '100%' }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '24px',
                maxWidth: '100%'
              }}>
                <div style={{ maxWidth: '100%' }}>
                  <label htmlFor="currency" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '8px' }}>
                    Currency
                  </label>
                  <select
                    name="currency"
                    id="currency"
                    value={formData.currency}
                    onChange={handleChange}
                    style={{
                      width: '100%',
                      padding: '10px 12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(0,0,0,0.1)',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="INR">INR - Indian Rupee</option>
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="JPY">JPY - Japanese Yen</option>
                  </select>
                </div>

                <div style={{ maxWidth: '100%' }}>
                  <label htmlFor="language" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '8px' }}>
                    Language
                  </label>
                  <select
                    name="language"
                    id="language"
                    value={formData.language}
                    onChange={handleChange}
                    style={{
                      width: '100%',
                      padding: '10px 12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(0,0,0,0.1)',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="en">English</option>
                    <option value="hi">Hindi</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="it">Italian</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notifications Section */}
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">Notifications</h2>
          </div>
          <div className="card">
            <div style={{ padding: '24px', maxWidth: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                <input
                  id="notifications"
                  name="notifications"
                  type="checkbox"
                  checked={formData.notifications}
                  onChange={handleChange}
                  style={{
                    marginTop: '3px',
                    marginRight: '12px',
                    width: '16px',
                    height: '16px'
                  }}
                />
                <div>
                  <label htmlFor="notifications" style={{ display: 'block', fontSize: '14px', fontWeight: '600', color: '#212529', marginBottom: '4px' }}>
                    Email Notifications
                  </label>
                  <p style={{ fontSize: '14px', color: '#6C757D', margin: '0' }}>
                    Receive email notifications for new orders and messages.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="store-actions" style={{ marginTop: '32px', marginBottom: '24px', display: 'flex', justifyContent: 'flex-end', gap: '16px' }}>
          <button
            type="button"
            className="btn btn-outline"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
}
