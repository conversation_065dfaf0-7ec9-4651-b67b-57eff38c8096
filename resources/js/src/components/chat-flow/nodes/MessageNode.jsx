import React, { memo } from 'react';
import { Handle, Position, useStore } from 'reactflow';
import { PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

/**
 * MessageNode component for the Chat Flow Builder
 * Represents a WhatsApp message in the flow
 */
const MessageNode = ({ data, selected, isConnectable, id }) => {
  const { content, header, footer, buttons, image, isFirst, handleProductSelection, onEdit, onDelete } = data;

  // Get connected edges from React Flow store
  const edges = useStore((state) => state.edges);

  // Calculate connection status for buttons
  const getConnectionStatus = () => {
    if (!buttons || buttons.length === 0) return { total: 0, connected: 0 };

    const quickReplyButtons = buttons.filter(button => button.type === 'quick_reply');
    const connectedEdges = edges.filter(edge => edge.source === id);

    return {
      total: quickReplyButtons.length,
      connected: connectedEdges.length
    };
  };

  const connectionStatus = getConnectionStatus();

  // Get button color and icon based on type
  const getButtonStyle = (type) => {
    switch (type) {
      case 'quick_reply':
        return {
          className: 'bg-green-100 text-green-800 border-green-300',
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          ),
          tooltip: 'Can connect to other messages'
        };
      case 'url':
        return {
          className: 'bg-blue-100 text-blue-800 border-blue-300',
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
            </svg>
          ),
          tooltip: 'Opens external website'
        };
      case 'call':
        return {
          className: 'bg-purple-100 text-purple-800 border-purple-300',
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
          ),
          tooltip: 'Opens phone dialer'
        };
      default:
        return {
          className: 'bg-gray-100 text-gray-800 border-gray-300',
          icon: null,
          tooltip: ''
        };
    }
  };

  // Handle edit button click
  const handleEdit = (e) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(data);
    }
  };

  // Handle delete button click
  const handleDelete = (e) => {
    e.stopPropagation();
    if (onDelete) {
      // Call the onDelete handler directly, which will show the confirmation modal
      onDelete(data.id);
    }
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-md w-[280px] overflow-hidden transition-all group ${
        selected ? 'ring-2 ring-green-500' : 'ring-1 ring-gray-200'
      }`}
    >
      {/* Header */}
      <div className="bg-gray-100 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
          <div className="text-sm font-medium text-gray-700">
            {isFirst ? 'Start Message' : `Message #${data.id}`}
          </div>

          {/* Product selection indicator */}
          {handleProductSelection && (
            <div className="ml-2 px-1.5 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
              </svg>
              Product
            </div>
          )}
        </div>

        {/* Connection status indicator */}
        {connectionStatus.total > 0 && (
          <div className="flex items-center text-xs">
            <div className={`px-2 py-0.5 rounded-full ${
              connectionStatus.connected === connectionStatus.total
                ? 'bg-green-100 text-green-800'
                : connectionStatus.connected > 0
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
            }`}>
              {connectionStatus.connected}/{connectionStatus.total} connected
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        {header && (
          <div className="text-xs font-semibold text-green-600 mb-1 p-1 bg-green-50 rounded">{header}</div>
        )}

        {image && (
          <div className="mb-2 px-1">
            <img
              src={typeof image === 'string' && image.startsWith('data:image/')
                ? image
                : (image instanceof Blob || image instanceof File)
                  ? URL.createObjectURL(image)
                  : typeof image === 'object'
                    ? (() => {
                        // Try to extract data URL from object
                        try {
                          if (image.data) return image.data;
                          if (image.url) return image.url;
                          if (image.src) return image.src;

                          // Try to extract from JSON string
                          const jsonString = JSON.stringify(image);
                          const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                          if (dataUrlMatch && dataUrlMatch[1]) return dataUrlMatch[1];
                        } catch (err) {
//                           // Removed console.error
                        }
                        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkludmFsaWQgSW1hZ2U8L3RleHQ+PC9zdmc+';
                      })()
                    : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkludmFsaWQgSW1hZ2U8L3RleHQ+PC9zdmc+'}
              alt="Message image"
              className="w-full h-auto max-h-32 object-contain rounded-md border border-gray-200"
              onError={(e) => {
//                 // Removed console.error
                e.target.onerror = null;

                // Try one more time with a direct approach for data URLs
                if (typeof image === 'object') {
                  try {
                    // Try to extract data URL from JSON string
                    const jsonString = JSON.stringify(image);
                    const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                    if (dataUrlMatch && dataUrlMatch[1]) {
//                       // Removed console.log
                      e.target.src = dataUrlMatch[1];
                      return;
                    }
                  } catch (err) {
//                     // Removed console.error
                  }
                }

                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkltYWdlIEVycm9yPC90ZXh0Pjwvc3ZnPg==';
              }}
            />
          </div>
        )}

        <div className="text-sm text-gray-800 mb-2 whitespace-pre-wrap">
          {content || 'No content'}
        </div>

        {footer && (
          <div className="text-xs italic text-gray-500 mb-2 p-1 bg-gray-50 rounded">{footer}</div>
        )}

        {/* Buttons */}
        {buttons && buttons.length > 0 && (
          <div className="bg-gray-50 -mx-4 px-4 py-2 mt-2">
            {buttons.map((button, idx) => {
              const buttonStyle = getButtonStyle(button.type);

              // Check if this button is connected (only for quick reply buttons)
              const isConnected = button.type === 'quick_reply' &&
                edges.some(edge =>
                  edge.source === id &&
                  edge.data?.buttonIndex === idx
                );

              return (
                <div
                  key={`btn-${idx}`}
                  className={`text-xs px-3 py-1.5 rounded-full mb-1 inline-block mr-1 border ${buttonStyle.className} relative group/button`}
                >
                  <div className="flex items-center">
                    {buttonStyle.icon}
                    <span>{button.text}</span>

                    {/* Connection indicator for quick reply buttons */}
                    {button.type === 'quick_reply' && (
                      <span className={`ml-1 w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                    )}
                  </div>

                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover/button:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                    {button.type === 'quick_reply'
                      ? isConnected
                        ? 'Connected to another message'
                        : 'Can be connected to another message'
                      : buttonStyle.tooltip
                    }
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-end mt-3 space-x-2">
          <button
            className="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors flex items-center"
            onClick={handleEdit}
          >
            <PencilIcon className="h-3 w-3 mr-1" />
            Edit
          </button>
          <button
            className="text-xs px-2 py-1 bg-red-50 text-red-600 rounded hover:bg-red-100 transition-colors flex items-center"
            onClick={handleDelete}
          >
            <TrashIcon className="h-3 w-3 mr-1" />
            Delete
          </button>
        </div>
      </div>

      {/* Input Handle - Only for non-first nodes */}
      {!isFirst && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          className="w-8 h-8 rounded-full bg-green-500 border-2 border-white -left-4 socket-handle"
          isConnectable={isConnectable}
        >
          <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
          {/* Visual indicator for better visibility */}
          <div className="absolute -left-12 top-1/2 transform -translate-y-1/2 text-xs text-gray-500 whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            Connect here
          </div>
        </Handle>
      )}

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-8 h-8 rounded-full bg-green-500 border-2 border-white -right-4 socket-handle"
        isConnectable={isConnectable}
      >
        <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        {/* Visual indicator for better visibility */}
        <div className="absolute -right-12 top-1/2 transform -translate-y-1/2 text-xs text-gray-500 whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
          Drag from here
        </div>
      </Handle>
    </div>
  );
};

export default memo(MessageNode);
