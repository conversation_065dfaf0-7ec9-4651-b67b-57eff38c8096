import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import Footer from '../components/home/<USER>';

export default function AboutUs() {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529'
  };

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      margin: 0,
      padding: 0,
      color: colors.dark,
      backgroundColor: colors.white
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: colors.white,
        color: colors.dark,
        padding: '15px 5%',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: isMobile ? '15px' : '0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/">
            <img
              src="/WhaMart_Logo.png"
              alt="WhaMart Logo"
              style={{
                height: '40px',
                marginRight: '10px'
              }}
            />
          </Link>
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '30px'
        }}>
          {!isMobile && (
            <nav>
              <ul style={{
                display: 'flex',
                listStyle: 'none',
                gap: '30px',
                margin: 0,
                padding: 0
              }}>
                <li><a href="/#features" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Features</a></li>
                <li><a href="/#how-it-works" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>How It Works</a></li>
                <li><a href="/#pricing" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Pricing</a></li>
              </ul>
            </nav>
          )}

          <div style={{ display: 'flex', gap: '15px' }}>
            <Link to="/login" style={{
              padding: '10px 20px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              transition: 'all 0.3s ease'
            }}>
              Sign In
            </Link>
            <Link to="/register" style={{
              padding: '10px 20px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '80px 5%',
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '10%',
          right: '5%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          position: 'relative',
          zIndex: 1,
          maxWidth: '800px',
          margin: '0 auto'
        }}>
          <h1 style={{
            fontSize: isMobile ? '36px' : '48px',
            fontWeight: 700,
            marginBottom: '24px',
            color: colors.dark
          }}>
            About <span style={{ color: colors.primary }}>Whamart</span>
          </h1>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            marginBottom: '0',
            maxWidth: '700px',
            margin: '0 auto'
          }}>
            We're on a mission to revolutionize e-commerce for small businesses through the power of WhatsApp
          </p>
        </div>
      </section>

      {/* Our Story Section */}
      <section style={{
        padding: '100px 5%',
        backgroundColor: colors.white
      }}>
        <div style={{
          maxWidth: '1000px',
          margin: '0 auto',
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr',
          gap: '60px',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{
              fontSize: '36px',
              fontWeight: 700,
              marginBottom: '24px',
              color: colors.dark
            }}>
              Our Story
            </h2>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.8,
              color: colors.gray,
              marginBottom: '24px'
            }}>
              Whamart was founded in 2023 with a simple yet powerful vision: to make e-commerce accessible to every small business owner through the platform they already use daily - WhatsApp.
            </p>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.8,
              color: colors.gray,
              marginBottom: '24px'
            }}>
              Our founder, a small business owner himself, noticed that while his customers preferred to communicate and shop through WhatsApp, managing orders, products, and customer interactions manually was becoming increasingly challenging.
            </p>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.8,
              color: colors.gray
            }}>
              That's when the idea for Whamart was born - a platform that transforms WhatsApp from a simple messaging app into a powerful e-commerce tool for small businesses.
            </p>
          </div>
          <div style={{
            borderRadius: '20px',
            overflow: 'hidden',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}>
            <img 
              src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1287&q=80" 
              alt="Team working together" 
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />
          </div>
        </div>
      </section>

      {/* Mission & Values Section */}
      <section style={{
        padding: '100px 5%',
        backgroundColor: colors.secondary
      }}>
        <div style={{
          maxWidth: '1000px',
          margin: '0 auto',
          textAlign: 'center',
          marginBottom: '60px'
        }}>
          <h2 style={{
            fontSize: '36px',
            fontWeight: 700,
            marginBottom: '24px',
            color: colors.dark
          }}>
            Our Mission & Values
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '700px',
            margin: '0 auto'
          }}>
            At Whamart, we're guided by a set of core values that drive everything we do
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
          gap: '30px',
          maxWidth: '1000px',
          margin: '0 auto'
        }}>
          {/* Value 1 */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)'
          }}>
            <div style={{
              width: '70px',
              height: '70px',
              borderRadius: '50%',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '32px', height: '32px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 01-2.448-2.448 14.9 14.9 0 01.06-.312m-2.24 2.39a4.493 4.493 0 00-1.757 4.306 4.493 4.493 0 004.306-1.758M16.5 9a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Accessibility
            </h3>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              We believe e-commerce should be accessible to all businesses, regardless of size or technical expertise. Our platform is designed to be intuitive and easy to use.
            </p>
          </div>

          {/* Value 2 */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)'
          }}>
            <div style={{
              width: '70px',
              height: '70px',
              borderRadius: '50%',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '32px', height: '32px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Innovation
            </h3>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              We're constantly pushing the boundaries of what's possible with WhatsApp commerce, developing new features and tools to help businesses succeed.
            </p>
          </div>

          {/* Value 3 */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)'
          }}>
            <div style={{
              width: '70px',
              height: '70px',
              borderRadius: '50%',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '32px', height: '32px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Community
            </h3>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              We're building more than just a platform - we're creating a community of entrepreneurs who support and learn from each other.
            </p>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section style={{
        padding: '100px 5%',
        backgroundColor: colors.white
      }}>
        <div style={{
          maxWidth: '1000px',
          margin: '0 auto',
          textAlign: 'center',
          marginBottom: '60px'
        }}>
          <h2 style={{
            fontSize: '36px',
            fontWeight: 700,
            marginBottom: '24px',
            color: colors.dark
          }}>
            Meet Our Team
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '700px',
            margin: '0 auto'
          }}>
            The passionate individuals behind Whamart
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
          gap: '30px',
          maxWidth: '1000px',
          margin: '0 auto'
        }}>
          {/* Team Member 1 */}
          <div style={{
            textAlign: 'center'
          }}>
            <div style={{
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              overflow: 'hidden',
              margin: '0 auto 24px',
              border: `4px solid ${colors.secondary}`
            }}>
              <img 
                src="https://randomuser.me/api/portraits/men/32.jpg" 
                alt="Team Member" 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '8px',
              color: colors.dark
            }}>
              Rajesh Kumar
            </h3>
            <p style={{
              fontSize: '16px',
              color: colors.primary,
              fontWeight: 500,
              marginBottom: '16px'
            }}>
              Founder & CEO
            </p>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              With over 15 years of experience in e-commerce and small business development, Rajesh leads our vision and strategy.
            </p>
          </div>

          {/* Team Member 2 */}
          <div style={{
            textAlign: 'center'
          }}>
            <div style={{
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              overflow: 'hidden',
              margin: '0 auto 24px',
              border: `4px solid ${colors.secondary}`
            }}>
              <img 
                src="https://randomuser.me/api/portraits/women/44.jpg" 
                alt="Team Member" 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '8px',
              color: colors.dark
            }}>
              Priya Sharma
            </h3>
            <p style={{
              fontSize: '16px',
              color: colors.primary,
              fontWeight: 500,
              marginBottom: '16px'
            }}>
              CTO
            </p>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              A tech innovator with expertise in mobile platforms, Priya oversees our product development and technical strategy.
            </p>
          </div>

          {/* Team Member 3 */}
          <div style={{
            textAlign: 'center'
          }}>
            <div style={{
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              overflow: 'hidden',
              margin: '0 auto 24px',
              border: `4px solid ${colors.secondary}`
            }}>
              <img 
                src="https://randomuser.me/api/portraits/men/68.jpg" 
                alt="Team Member" 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '8px',
              color: colors.dark
            }}>
              Vikram Patel
            </h3>
            <p style={{
              fontSize: '16px',
              color: colors.primary,
              fontWeight: 500,
              marginBottom: '16px'
            }}>
              Head of Customer Success
            </p>
            <p style={{
              fontSize: '16px',
              lineHeight: 1.6,
              color: colors.gray
            }}>
              With a passion for helping small businesses thrive, Vikram ensures our customers get the most out of Whamart.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '80px 5%',
        textAlign: 'center'
      }}>
        <div style={{
          maxWidth: '700px',
          margin: '0 auto'
        }}>
          <h2 style={{
            fontSize: '36px',
            fontWeight: 700,
            marginBottom: '24px',
            color: colors.dark
          }}>
            Join the Whamart Revolution
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            marginBottom: '32px'
          }}>
            Ready to transform your WhatsApp into a powerful e-commerce platform? Get started today and join thousands of businesses already growing with Whamart.
          </p>
          <Link to="/register" style={{
            padding: '16px 32px',
            backgroundColor: colors.primary,
            color: colors.white,
            border: 'none',
            borderRadius: '50px',
            textDecoration: 'none',
            fontWeight: 600,
            fontSize: '16px',
            display: 'inline-block',
            boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
            transition: 'all 0.3s ease'
          }}>
            Get Started Free
          </Link>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
}