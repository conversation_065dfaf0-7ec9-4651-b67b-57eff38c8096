import { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  UserIcon,
  DocumentTextIcon,
  CalendarIcon,
  ChatBubbleLeftRightIcon,
  CheckIcon,
  XMarkIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorManagement() {
  const [loading, setLoading] = useState(true);
  const [managementData, setManagementData] = useState({
    boardMembers: [],
    managementTeam: [],
    boardMeetings: [],
    boardProposals: [],
    governanceDocuments: []
  });
  const [activeTab, setActiveTab] = useState('board');
  const [showVoteModal, setShowVoteModal] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState(null);
  const [voteComment, setVoteComment] = useState('');
  const [voteValue, setVoteValue] = useState(null);

  // Fetch management data
  useEffect(() => {
    const fetchManagementData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          boardMembers: [
            {
              id: 1,
              name: 'Karan Solanki',
              role: 'Founder & CEO',
              equity: 60,
              votingRights: 60,
              image: 'https://randomuser.me/api/portraits/men/1.jpg',
              bio: 'Founder and CEO of WhaMart. Serial entrepreneur with expertise in e-commerce and digital marketing.'
            },
            {
              id: 2,
              name: 'Priya Sharma',
              role: 'CTO',
              equity: 0,
              votingRights: 0,
              image: 'https://randomuser.me/api/portraits/women/2.jpg',
              bio: 'Technology leader with 10+ years of experience in building scalable platforms and mobile applications.'
            },
            {
              id: 3,
              name: 'Rajiv Mehta',
              role: 'Strategic Investor',
              equity: 10,
              votingRights: 10,
              image: 'https://randomuser.me/api/portraits/men/3.jpg',
              bio: 'Angel investor with portfolio of 20+ successful startups. Expertise in scaling B2B SaaS companies.'
            },
            {
              id: 4,
              name: 'Ananya Patel',
              role: 'Board Observer',
              equity: 0,
              votingRights: 0,
              image: 'https://randomuser.me/api/portraits/women/4.jpg',
              bio: 'Representing investor consortium. Background in finance and venture capital with focus on tech startups.'
            },
            {
              id: 5,
              name: 'Current Investor',
              role: 'Investor',
              equity: 5,
              votingRights: 5,
              image: 'https://randomuser.me/api/portraits/men/5.jpg',
              bio: 'Your position on the board as an investor with 5% equity stake.'
            }
          ],
          managementTeam: [
            {
              id: 1,
              name: 'Karan Solanki',
              role: 'Founder & CEO',
              image: 'https://randomuser.me/api/portraits/men/1.jpg',
              bio: 'Founder and CEO of WhaMart. Serial entrepreneur with expertise in e-commerce and digital marketing.'
            },
            {
              id: 2,
              name: 'Priya Sharma',
              role: 'CTO',
              image: 'https://randomuser.me/api/portraits/women/2.jpg',
              bio: 'Technology leader with 10+ years of experience in building scalable platforms and mobile applications.'
            },
            {
              id: 6,
              name: 'Vikram Singh',
              role: 'COO',
              image: 'https://randomuser.me/api/portraits/men/6.jpg',
              bio: 'Operations expert with background in logistics and supply chain management for e-commerce platforms.'
            },
            {
              id: 7,
              name: 'Neha Gupta',
              role: 'CMO',
              image: 'https://randomuser.me/api/portraits/women/7.jpg',
              bio: 'Marketing professional with expertise in digital marketing, brand building, and customer acquisition.'
            },
            {
              id: 8,
              name: 'Arjun Reddy',
              role: 'CFO',
              image: 'https://randomuser.me/api/portraits/men/8.jpg',
              bio: 'Finance professional with experience in startup funding, financial planning, and investor relations.'
            }
          ],
          boardMeetings: [
            {
              id: 1,
              title: 'Q2 2023 Board Meeting',
              date: '2023-07-15',
              status: 'completed',
              minutes: 'https://example.com/minutes/q2-2023',
              agenda: [
                'Q2 Financial Review',
                'Product Roadmap Update',
                'Marketing Strategy Discussion',
                'Funding Requirements'
              ]
            },
            {
              id: 2,
              title: 'Q3 2023 Board Meeting',
              date: '2023-10-15',
              status: 'upcoming',
              agenda: [
                'Q3 Financial Review',
                'Growth Metrics Analysis',
                'Expansion Strategy',
                'Team Hiring Plans'
              ]
            },
            {
              id: 3,
              title: 'Annual General Meeting 2023',
              date: '2023-12-20',
              status: 'upcoming',
              agenda: [
                'Annual Financial Review',
                '2024 Budget Approval',
                'Strategic Goals for 2024',
                'Investor Q&A'
              ]
            }
          ],
          boardProposals: [
            {
              id: 1,
              title: 'Series A Funding Round',
              description: 'Proposal to initiate Series A funding round targeting ₹2.25 Crore at ₹15 Crore valuation.',
              proposedBy: 'Karan Solanki',
              dateProposed: '2023-07-15',
              status: 'voting',
              deadline: '2023-08-15',
              votes: {
                for: 70,
                against: 0,
                abstain: 0,
                pending: 30
              },
              yourVote: null
            },
            {
              id: 2,
              title: 'New CTO Appointment',
              description: 'Proposal to appoint Priya Sharma as Chief Technology Officer with standard executive compensation package.',
              proposedBy: 'Karan Solanki',
              dateProposed: '2023-06-10',
              status: 'approved',
              votes: {
                for: 100,
                against: 0,
                abstain: 0,
                pending: 0
              },
              yourVote: 'for'
            },
            {
              id: 3,
              title: 'Expansion to 10 New Cities',
              description: 'Proposal to accelerate market expansion to 10 additional cities by Q1 2024 with budget allocation of ₹50 Lakh.',
              proposedBy: 'Rajiv Mehta',
              dateProposed: '2023-07-20',
              status: 'voting',
              deadline: '2023-08-20',
              votes: {
                for: 60,
                against: 0,
                abstain: 10,
                pending: 30
              },
              yourVote: null
            }
          ],
          governanceDocuments: [
            {
              id: 1,
              name: 'Articles of Association',
              date: '2023-01-01',
              url: '#'
            },
            {
              id: 2,
              name: 'Shareholders Agreement',
              date: '2023-01-01',
              url: '#'
            },
            {
              id: 3,
              name: 'Board Charter',
              date: '2023-01-15',
              url: '#'
            },
            {
              id: 4,
              name: 'Voting Rights Agreement',
              date: '2023-01-15',
              url: '#'
            },
            {
              id: 5,
              name: 'Management Rights Letter',
              date: '2023-01-15',
              url: '#'
            }
          ]
        };

        setManagementData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching management data:', error);
        setLoading(false);
      }
    };

    fetchManagementData();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Handle vote button click
  const handleVoteClick = (proposal) => {
    setSelectedProposal(proposal);
    setShowVoteModal(true);
    setVoteComment('');
    setVoteValue(null);
  };

  // Handle vote submission
  const handleVoteSubmit = () => {
    // In a real implementation, this would send the vote to an API
    console.log('Vote submitted:', { proposalId: selectedProposal.id, vote: voteValue, comment: voteComment });

    // Update the local state to reflect the vote
    const updatedProposals = managementData.boardProposals.map(proposal => {
      if (proposal.id === selectedProposal.id) {
        const updatedVotes = { ...proposal.votes };

        // If changing vote, adjust previous vote count
        if (proposal.yourVote) {
          updatedVotes[proposal.yourVote] -= 5; // Assuming 5% voting rights
        } else {
          updatedVotes.pending -= 5;
        }

        // Add to new vote count
        updatedVotes[voteValue] += 5;

        return {
          ...proposal,
          votes: updatedVotes,
          yourVote: voteValue
        };
      }
      return proposal;
    });

    setManagementData({
      ...managementData,
      boardProposals: updatedProposals
    });

    setShowVoteModal(false);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Governance</div>
          <h1 className="welcome-title">
            Management <span style={{ color: '#25D366' }}>Board</span>
          </h1>
          <p className="welcome-subtitle">
            View board members, management team, meetings, and proposals.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Tabs */}
        <div className="flex border-b mb-6 overflow-x-auto">
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'board' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('board')}
          >
            <UserGroupIcon className="h-5 w-5 mr-2" />
            Board & Management
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'meetings' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('meetings')}
          >
            <CalendarIcon className="h-5 w-5 mr-2" />
            Board Meetings
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'proposals' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('proposals')}
          >
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Board Proposals
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'documents' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('documents')}
          >
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Governance Documents
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading management data...</p>
          </div>
        ) : (
          <>
            {/* Board & Management Tab */}
            {activeTab === 'board' && (
              <div>
                <div className="section-header">
                  <h2 className="section-title">Board of Directors</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  {managementData.boardMembers.map((member) => (
                    <div key={member.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                      <div className="flex items-center mb-4">
                        <img
                          src={member.image}
                          alt={member.name}
                          className="w-16 h-16 rounded-full object-cover mr-4"
                        />
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                          <p className="text-sm text-gray-500">{member.role}</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{member.bio}</p>
                      {member.equity > 0 && (
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-500">Equity:</span>
                          <span className="font-medium">{formatPercentage(member.equity)}</span>
                        </div>
                      )}
                      {member.votingRights > 0 && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Voting Rights:</span>
                          <span className="font-medium">{formatPercentage(member.votingRights)}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="section-header">
                  <h2 className="section-title">Management Team</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {managementData.managementTeam.map((member) => (
                    <div key={member.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                      <div className="flex items-center mb-4">
                        <img
                          src={member.image}
                          alt={member.name}
                          className="w-16 h-16 rounded-full object-cover mr-4"
                        />
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                          <p className="text-sm text-gray-500">{member.role}</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">{member.bio}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Board Meetings Tab */}
            {activeTab === 'meetings' && (
              <div>
                <div className="section-header">
                  <h2 className="section-title">Board Meetings</h2>
                </div>
                <div className="space-y-6">
                  {managementData.boardMeetings.map((meeting) => (
                    <div key={meeting.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{meeting.title}</h3>
                          <p className="text-sm text-gray-500 flex items-center mt-1">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            {formatDate(meeting.date)}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          meeting.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {meeting.status === 'completed' ? 'Completed' : 'Upcoming'}
                        </span>
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Agenda</h4>
                        <ul className="space-y-1 pl-5 list-disc">
                          {meeting.agenda.map((item, index) => (
                            <li key={index} className="text-sm text-gray-600">{item}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-end">
                        {meeting.status === 'completed' && meeting.minutes && (
                          <a
                            href={meeting.minutes}
                            className="text-sm text-green-600 hover:text-green-800 flex items-center"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <DocumentTextIcon className="h-4 w-4 mr-1" />
                            View Minutes
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Board Proposals Tab */}
            {activeTab === 'proposals' && (
              <div>
                <div className="section-header">
                  <h2 className="section-title">Board Proposals</h2>
                </div>
                <div className="space-y-6">
                  {managementData.boardProposals.map((proposal) => (
                    <div key={proposal.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{proposal.title}</h3>
                          <p className="text-sm text-gray-500 flex items-center mt-1">
                            <UserIcon className="h-4 w-4 mr-1" />
                            Proposed by: {proposal.proposedBy}
                          </p>
                          <p className="text-sm text-gray-500 flex items-center mt-1">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            Date: {formatDate(proposal.dateProposed)}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          proposal.status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : proposal.status === 'rejected'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                          {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-4">{proposal.description}</p>

                      {proposal.status === 'voting' && (
                        <div className="mb-4">
                          <p className="text-sm text-gray-500 mb-2">
                            Voting Deadline: {formatDate(proposal.deadline)}
                          </p>
                          <div className="bg-gray-100 p-4 rounded-md">
                            <div className="flex justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700">Current Votes</span>
                              <span className="text-sm text-gray-500">
                                {proposal.votes.for}% For | {proposal.votes.against}% Against | {proposal.votes.abstain}% Abstain | {proposal.votes.pending}% Pending
                              </span>
                            </div>
                            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div className="flex h-full">
                                <div
                                  className="h-full bg-green-500"
                                  style={{ width: `${proposal.votes.for}%` }}
                                ></div>
                                <div
                                  className="h-full bg-red-500"
                                  style={{ width: `${proposal.votes.against}%` }}
                                ></div>
                                <div
                                  className="h-full bg-gray-400"
                                  style={{ width: `${proposal.votes.abstain}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-between items-center">
                        <div>
                          {proposal.yourVote && (
                            <span className="text-sm text-gray-500">
                              Your vote: <span className={`font-medium ${
                                proposal.yourVote === 'for'
                                  ? 'text-green-600'
                                  : proposal.yourVote === 'against'
                                    ? 'text-red-600'
                                    : 'text-gray-600'
                              }`}>
                                {proposal.yourVote.charAt(0).toUpperCase() + proposal.yourVote.slice(1)}
                              </span>
                            </span>
                          )}
                        </div>

                        {proposal.status === 'voting' && !proposal.yourVote && (
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => handleVoteClick(proposal)}
                          >
                            Cast Your Vote
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Governance Documents Tab */}
            {activeTab === 'documents' && (
              <div>
                <div className="section-header">
                  <h2 className="section-title">Governance Documents</h2>
                </div>
                <div className="card" style={{ padding: '24px' }}>
                  <div className="space-y-6">
                    {managementData.governanceDocuments.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-6 w-6 text-gray-400 mr-4" />
                          <div>
                            <p className="text-md font-medium text-gray-900">{doc.name}</p>
                            <p className="text-sm text-gray-500">{formatDate(doc.date)}</p>
                          </div>
                        </div>
                        <a
                          href={doc.url}
                          className="text-sm text-green-600 hover:text-green-800"
                          download
                        >
                          Download
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Vote Modal */}
      {showVoteModal && selectedProposal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">Cast Your Vote</h3>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setShowVoteModal(false)}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4">
              <h4 className="text-md font-medium text-gray-900 mb-2">{selectedProposal.title}</h4>
              <p className="text-sm text-gray-600 mb-4">{selectedProposal.description}</p>

              <div className="space-y-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Vote
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="vote-for"
                        name="vote"
                        type="radio"
                        checked={voteValue === 'for'}
                        onChange={() => setVoteValue('for')}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label htmlFor="vote-for" className="ml-3 block text-sm font-medium text-gray-700">
                        For
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="vote-against"
                        name="vote"
                        type="radio"
                        checked={voteValue === 'against'}
                        onChange={() => setVoteValue('against')}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                      />
                      <label htmlFor="vote-against" className="ml-3 block text-sm font-medium text-gray-700">
                        Against
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="vote-abstain"
                        name="vote"
                        type="radio"
                        checked={voteValue === 'abstain'}
                        onChange={() => setVoteValue('abstain')}
                        className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300"
                      />
                      <label htmlFor="vote-abstain" className="ml-3 block text-sm font-medium text-gray-700">
                        Abstain
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Comment (Optional)
                  </label>
                  <textarea
                    value={voteComment}
                    onChange={(e) => setVoteComment(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Add a comment with your vote..."
                  ></textarea>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  className="btn btn-outline mr-3"
                  onClick={() => setShowVoteModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleVoteSubmit}
                  disabled={!voteValue}
                >
                  <PaperAirplaneIcon className="h-5 w-5 mr-2" />
                  Submit Vote
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
