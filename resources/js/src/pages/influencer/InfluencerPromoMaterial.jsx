import { useState, useEffect } from 'react';
import {
  PhotoIcon,
  ArrowPathIcon,
  ArrowDownTrayIcon,
  DocumentTextIcon,
  ClipboardIcon,
  ShareIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerPromoMaterial() {
  const [loading, setLoading] = useState(false);
  const [materials, setMaterials] = useState([]);
  const [activeTab, setActiveTab] = useState('banners');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  // Fetch promotional materials
  useEffect(() => {
    const fetchMaterials = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockMaterials = [
          // Banners
          {
            id: 1,
            type: 'banner',
            title: 'WhaMart Standard Plan Promo',
            description: 'Banner promoting the Standard subscription plan with 25% discount.',
            imageUrl: 'https://via.placeholder.com/800x400/25D366/FFFFFF?text=WhaMart+Standard+Plan',
            dimensions: '800x400',
            format: 'PNG',
            category: 'subscription',
            downloadUrl: '#',
            createdAt: '2023-10-01'
          },
          {
            id: 2,
            type: 'banner',
            title: 'WhaMart Gold Plan Promo',
            description: 'Banner promoting the Gold subscription plan with 25% discount.',
            imageUrl: 'https://via.placeholder.com/800x400/075E54/FFFFFF?text=WhaMart+Gold+Plan',
            dimensions: '800x400',
            format: 'PNG',
            category: 'subscription',
            downloadUrl: '#',
            createdAt: '2023-10-01'
          },
          {
            id: 3,
            type: 'banner',
            title: 'WhaMart Premium Plan Promo',
            description: 'Banner promoting the Premium subscription plan with 25% discount.',
            imageUrl: 'https://via.placeholder.com/800x400/128C7E/FFFFFF?text=WhaMart+Premium+Plan',
            dimensions: '800x400',
            format: 'PNG',
            category: 'subscription',
            downloadUrl: '#',
            createdAt: '2023-10-01'
          },
          {
            id: 4,
            type: 'banner',
            title: 'WhaMart Launch Banner',
            description: 'Banner announcing the launch of WhaMart platform.',
            imageUrl: 'https://via.placeholder.com/1200x600/25D366/FFFFFF?text=WhaMart+Launch',
            dimensions: '1200x600',
            format: 'PNG',
            category: 'general',
            downloadUrl: '#',
            createdAt: '2023-09-15'
          },
          {
            id: 5,
            type: 'banner',
            title: 'WhatsApp Store Feature Banner',
            description: 'Banner highlighting the WhatsApp store feature.',
            imageUrl: 'https://via.placeholder.com/1200x600/075E54/FFFFFF?text=WhatsApp+Store+Feature',
            dimensions: '1200x600',
            format: 'PNG',
            category: 'features',
            downloadUrl: '#',
            createdAt: '2023-09-20'
          },
          {
            id: 6,
            type: 'banner',
            title: 'Chat Flow Builder Banner',
            description: 'Banner highlighting the chat flow builder feature.',
            imageUrl: 'https://via.placeholder.com/1200x600/128C7E/FFFFFF?text=Chat+Flow+Builder',
            dimensions: '1200x600',
            format: 'PNG',
            category: 'features',
            downloadUrl: '#',
            createdAt: '2023-09-25'
          },

          // Post Templates
          {
            id: 7,
            type: 'post',
            title: 'WhaMart Introduction Post',
            description: 'Post template introducing WhaMart to your audience.',
            imageUrl: 'https://via.placeholder.com/1080x1080/25D366/FFFFFF?text=WhaMart+Introduction',
            dimensions: '1080x1080',
            format: 'PNG',
            category: 'general',
            downloadUrl: '#',
            createdAt: '2023-10-05',
            caption: 'Excited to announce that I have partnered with WhaMart! 🚀 Now small businesses can create their own WhatsApp stores with automated chat flows. Use my link to get started and enjoy a 25% discount on all subscription plans! #WhaMart #WhatsAppStore #SmallBusiness'
          },
          {
            id: 8,
            type: 'post',
            title: 'WhaMart Features Post',
            description: 'Post template highlighting key features of WhaMart.',
            imageUrl: 'https://via.placeholder.com/1080x1080/075E54/FFFFFF?text=WhaMart+Features',
            dimensions: '1080x1080',
            format: 'PNG',
            category: 'features',
            downloadUrl: '#',
            createdAt: '2023-10-10',
            caption: 'WhaMart makes selling on WhatsApp super easy! 💚 Create automated chat flows, showcase your products, and accept payments - all within WhatsApp. Click my link in bio to start your 7-day free trial! #WhaMart #WhatsAppBusiness #OnlineStore'
          },
          {
            id: 9,
            type: 'post',
            title: 'WhaMart Discount Offer Post',
            description: 'Post template promoting the 25% discount offer.',
            imageUrl: 'https://via.placeholder.com/1080x1080/128C7E/FFFFFF?text=25%+Discount+Offer',
            dimensions: '1080x1080',
            format: 'PNG',
            category: 'subscription',
            downloadUrl: '#',
            createdAt: '2023-10-15',
            caption: 'SPECIAL OFFER! 🎉 Get 25% OFF on all WhaMart subscription plans when you sign up using my link! Whether you are selling products or services, WhaMart helps you create a professional WhatsApp store in minutes. Link in bio! #WhaMart #SpecialOffer #WhatsAppStore'
          },

          // Story Templates
          {
            id: 10,
            type: 'story',
            title: 'WhaMart Story Template 1',
            description: 'Story template for WhaMart introduction.',
            imageUrl: 'https://via.placeholder.com/1080x1920/25D366/FFFFFF?text=WhaMart+Story+1',
            dimensions: '1080x1920',
            format: 'PNG',
            category: 'general',
            downloadUrl: '#',
            createdAt: '2023-10-20'
          },
          {
            id: 11,
            type: 'story',
            title: 'WhaMart Story Template 2',
            description: 'Story template highlighting WhaMart features.',
            imageUrl: 'https://via.placeholder.com/1080x1920/075E54/FFFFFF?text=WhaMart+Story+2',
            dimensions: '1080x1920',
            format: 'PNG',
            category: 'features',
            downloadUrl: '#',
            createdAt: '2023-10-25'
          },
          {
            id: 12,
            type: 'story',
            title: 'WhaMart Story Template 3',
            description: 'Story template for subscription plan promotion.',
            imageUrl: 'https://via.placeholder.com/1080x1920/128C7E/FFFFFF?text=WhaMart+Story+3',
            dimensions: '1080x1920',
            format: 'PNG',
            category: 'subscription',
            downloadUrl: '#',
            createdAt: '2023-10-30'
          },

          // Video Scripts
          {
            id: 13,
            type: 'script',
            title: 'WhaMart Introduction Video Script',
            description: 'Script for creating an introduction video about WhaMart.',
            category: 'general',
            downloadUrl: '#',
            createdAt: '2023-11-01',
            content: `# WhaMart Introduction Video Script

## Opening (0:00 - 0:15)
"Hey everyone! Today I want to tell you about an amazing platform called WhaMart that is revolutionizing how small businesses sell on WhatsApp."

## What is WhaMart (0:15 - 0:45)
"WhaMart is a platform that lets businesses create their own WhatsApp store with automated chat flows. It is perfect for small businesses, entrepreneurs, and anyone looking to sell products or services through WhatsApp."

## Key Features (0:45 - 1:30)
"Let me show you some of the awesome features:
1. Create automated chat flows to interact with customers
2. Showcase your products with images and descriptions
3. Accept payments directly through WhatsApp
4. Track your sales and customer interactions
5. All of this with a WhatsApp-like interface that your customers already know how to use!"

## Subscription Plans (1:30 - 2:00)
"WhaMart offers three affordable subscription plans:
- Standard Plan at just ₹1,499 per year
- Gold Plan at ₹3,000 per year
- Premium Plan at ₹4,999 per year
And the best part? You get a 25% discount when you sign up using my link!"

## Call to Action (2:00 - 2:15)
"Click the link in my description to get started with WhaMart today. Do not miss this opportunity to take your business to the next level with WhatsApp automation!"

## Closing (2:15 - 2:30)
"If you have any questions, drop them in the comments below. Do not forget to like and subscribe for more content like this. Thanks for watching!"
`
          },
          {
            id: 14,
            type: 'script',
            title: 'WhaMart Features Demo Script',
            description: 'Script for creating a video demonstrating WhaMart features.',
            category: 'features',
            downloadUrl: '#',
            createdAt: '2023-11-05',
            content: `# WhaMart Features Demo Script

## Introduction (0:00 - 0:30)
"Welcome back to my channel! Today I am going to give you a detailed walkthrough of WhaMarts amazing features and show you exactly how they can help grow your business on WhatsApp."

## Chat Flow Builder (0:30 - 1:30)
"First, let's look at the Chat Flow Builder. This powerful tool lets you create automated conversations with your customers. You can add welcome messages, product showcases, and even decision trees based on customer responses. Let me show you how easy it is to set up..."

## Product Catalog (1:30 - 2:30)
"Next, let's explore the Product Catalog feature. You can add your products with images, descriptions, and prices. Customers can browse your catalog directly in WhatsApp and place orders with just a few taps. Here's how to set it up..."

## Payment Integration (2:30 - 3:30)
"WhaMart makes accepting payments super simple. You can generate payment QR codes that customers can scan to complete their purchase. No need for complex payment gateways! Let me show you how it works..."

## Analytics Dashboard (3:30 - 4:30)
"The Analytics Dashboard gives you valuable insights into your stores performance. You can track visitors, popular products, conversion rates, and more. This data helps you optimize your store and increase sales..."

## Conclusion & Call to Action (4:30 - 5:00)
"As you can see, WhaMart makes it incredibly easy to run a business on WhatsApp. Sign up using my link in the description to get a 25% discount on any subscription plan. If you found this video helpful, please like, comment, and subscribe for more content!"
`
          },
          {
            id: 15,
            type: 'script',
            title: 'WhaMart Success Stories Script',
            description: 'Script for creating a video about WhaMart success stories.',
            category: 'general',
            downloadUrl: '#',
            createdAt: '2023-11-10',
            content: `# WhaMart Success Stories Video Script

## Introduction (0:00 - 0:30)
"Hey everyone! Today I want to share some inspiring success stories from real businesses using WhaMart to grow their sales on WhatsApp."

## Success Story 1: Local Bakery (0:30 - 1:30)
"First, let's talk about Sunshine Bakery, a small local bakery that was struggling to manage orders through regular WhatsApp messages. After switching to WhaMart:
- They automated their order process with chat flows
- Customers could easily browse their daily menu
- Order accuracy improved by 95%
- Sales increased by 40% in just two months"

## Success Story 2: Handmade Jewelry (1:30 - 2:30)
"Next is Artistic Creations, a handmade jewelry business run by a single entrepreneur:
- They used WhaMart to showcase their jewelry collections with high-quality images
- Customers could filter products by type, material, and price
- The automated chat flow answered common questions about materials and shipping
- Their customer base grew by 60% and sales doubled in three months"

## Success Story 3: Yoga Instructor (2:30 - 3:30)
"Finally, let's look at Serene Yoga, a service-based business:
- They used WhaMart to schedule and manage yoga sessions
- Clients could book classes through an automated system
- Payment collection became seamless
- They saved 15 hours per week on administrative tasks
- Client retention improved by 35%"

## How You Can Succeed Too (3:30 - 4:00)
"These are just a few examples of how businesses are transforming with WhaMart. Whether you sell products or services, WhaMart can help you create a professional WhatsApp store that converts visitors into customers."

## Call to Action (4:00 - 4:30)
"Ready to write your own success story? Click the link in my description to get started with WhaMart today. Do not forget to use my code for a 25% discount on any subscription plan!"
`
          }
        ];

        setMaterials(mockMaterials);
      } catch (error) {
        console.error('Error fetching promotional materials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMaterials();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    setFilterCategory(e.target.value);
  };

  // Handle download
  const handleDownload = (material) => {
    // In a real implementation, this would download the file
    alert(`Downloading ${material.title}...`);
  };

  // Handle copy text
  const handleCopyText = (text) => {
    navigator.clipboard.writeText(text);
    alert('Text copied to clipboard!');
  };

  // Handle share
  const handleShare = (material) => {
    if (navigator.share) {
      navigator.share({
        title: material.title,
        text: material.description,
        url: material.downloadUrl
      })
      .catch(error => console.log('Error sharing:', error));
    } else {
      alert('Sharing is not supported on this browser.');
    }
  };

  // Get filtered materials
  const getFilteredMaterials = () => {
    return materials.filter(material => {
      const matchesSearch = searchTerm === '' ||
        material.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        material.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = activeTab === 'all' || material.type === activeTab;

      const matchesCategory = filterCategory === 'all' || material.category === filterCategory;

      return matchesSearch && matchesType && matchesCategory;
    });
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Marketing Resources</div>
          <h1 className="welcome-title">
            Promotional <span style={{ color: '#25D366' }}>Materials</span>
          </h1>
          <p className="welcome-subtitle">
            Access ready-to-use marketing materials to promote WhaMart on your social media channels.
          </p>
        </div>
      </div>

      {/* Promotional Materials Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Marketing Resources</h2>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search materials..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <div className="relative">
              <select
                value={filterCategory}
                onChange={handleFilterChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 appearance-none"
              >
                <option value="all">All Categories</option>
                <option value="general">General</option>
                <option value="features">Features</option>
                <option value="subscription">Subscription</option>
              </select>
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Material Type Tabs */}
        <div className="mb-6">
          <div className="flex border-b border-gray-200 overflow-x-auto">
            <button
              className={`px-4 py-2 text-sm font-medium ${activeTab === 'all' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('all')}
            >
              All Materials
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${activeTab === 'banner' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('banner')}
            >
              Banners
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${activeTab === 'post' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('post')}
            >
              Post Templates
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${activeTab === 'story' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('story')}
            >
              Story Templates
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${activeTab === 'script' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setActiveTab('script')}
            >
              Video Scripts
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading promotional materials...</p>
          </div>
        ) : (
          <>
            {getFilteredMaterials().length === 0 ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>No promotional materials found matching your criteria.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getFilteredMaterials().map((material) => (
                  <div key={material.id} className="card hover:shadow-md transition-shadow" style={{ padding: '0', overflow: 'hidden' }}>
                    {/* Image or Script Preview */}
                    {material.type !== 'script' ? (
                      <div className="relative">
                        <img
                          src={material.imageUrl}
                          alt={material.title}
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute top-2 right-2">
                          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-white text-gray-800 shadow">
                            {material.dimensions}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gray-50 p-4 h-48 overflow-hidden">
                        <DocumentTextIcon className="h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600 line-clamp-6">
                          {material.content.substring(0, 300)}...
                        </p>
                      </div>
                    )}

                    {/* Material Info */}
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-md font-medium text-gray-900">{material.title}</h3>
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                          {material.category.charAt(0).toUpperCase() + material.category.slice(1)}
                        </span>
                      </div>

                      <p className="text-sm text-gray-500 mb-4">{material.description}</p>

                      {material.type === 'post' && (
                        <div className="mb-4">
                          <p className="text-xs text-gray-500 mb-1">Suggested Caption:</p>
                          <div className="relative">
                            <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded line-clamp-2">
                              {material.caption}
                            </p>
                            <button
                              onClick={() => handleCopyText(material.caption)}
                              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                              title="Copy Caption"
                            >
                              <ClipboardIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">
                          Added: {formatDate(material.createdAt)}
                        </span>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleDownload(material)}
                            className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                            title="Download"
                          >
                            <ArrowDownTrayIcon className="h-5 w-5" />
                          </button>

                          {material.type === 'script' && (
                            <button
                              onClick={() => handleCopyText(material.content)}
                              className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                              title="Copy Script"
                            >
                              <ClipboardIcon className="h-5 w-5" />
                            </button>
                          )}

                          <button
                            onClick={() => handleShare(material)}
                            className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
                            title="Share"
                          >
                            <ShareIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Usage Guidelines */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Usage Guidelines</h2>
        </div>

        <div className="card" style={{ padding: '24px' }}>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">How to Use These Materials</h3>
              <p className="text-sm text-gray-600">
                These promotional materials are designed to help you effectively promote WhaMart on your social media channels. Feel free to use them as they are or customize them to match your personal brand and audience.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Best Practices</h3>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>Always include your referral link when sharing these materials</li>
                <li>Customize captions to make them more personal and authentic</li>
                <li>Share your own experience with WhaMart to build trust</li>
                <li>Highlight the benefits that would appeal most to your specific audience</li>
                <li>Post consistently across different platforms for maximum reach</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Restrictions</h3>
              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                <li>Do not modify the WhaMart logo or brand colors</li>
                <li>Do not make false claims about the platform or its features</li>
                <li>Do not use these materials for any purpose other than promoting WhaMart</li>
                <li>Do not imply that you are an official representative of WhaMart unless explicitly authorized</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
