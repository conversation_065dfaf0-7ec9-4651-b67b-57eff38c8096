import { useState, useEffect } from 'react';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  EyeIcon,
  ShieldCheckIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminVendors() {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');

  // Fetch vendors
  useEffect(() => {
    const fetchVendors = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call with pagination
        // For now, we'll use mock data
        const mockVendors = [
          {
            id: 1,
            name: '<PERSON><PERSON>',
            email: '<EMAIL>',
            phone: '+91 9876543210',
            storeName: 'Rahul Electronics',
            storeUrl: 'rahul-electronics',
            businessCategory: 'Electronics',
            isVerified: true,
            isActive: true,
            registeredDate: '2023-05-15',
            subscription: {
              plan: 'Gold',
              startDate: '2023-05-15',
              endDate: '2024-05-14',
              status: 'active'
            },
            stats: {
              products: 45,
              orders: 128,
              revenue: '₹45,600'
            }
          },
          {
            id: 2,
            name: 'Priya Patel',
            email: '<EMAIL>',
            phone: '+91 9876543211',
            storeName: 'Fashion Hub',
            storeUrl: 'fashion-hub',
            businessCategory: 'Clothing',
            isVerified: true,
            isActive: true,
            registeredDate: '2023-06-20',
            subscription: {
              plan: 'Premium',
              startDate: '2023-06-20',
              endDate: '2024-06-19',
              status: 'active'
            },
            stats: {
              products: 78,
              orders: 256,
              revenue: '₹89,200'
            }
          },
          {
            id: 3,
            name: 'Amit Kumar',
            email: '<EMAIL>',
            phone: '+91 9876543212',
            storeName: 'Spice Garden',
            storeUrl: 'spice-garden',
            businessCategory: 'Food',
            isVerified: false,
            isActive: true,
            registeredDate: '2023-07-10',
            subscription: {
              plan: 'Standard',
              startDate: '2023-07-10',
              endDate: '2024-07-09',
              status: 'active'
            },
            stats: {
              products: 25,
              orders: 87,
              revenue: '₹32,400'
            }
          },
          {
            id: 4,
            name: 'Neha Singh',
            email: '<EMAIL>',
            phone: '+91 9876543213',
            storeName: 'Beauty Essentials',
            storeUrl: 'beauty-essentials',
            businessCategory: 'Beauty',
            isVerified: true,
            isActive: false,
            registeredDate: '2023-08-05',
            subscription: {
              plan: 'Gold',
              startDate: '2023-08-05',
              endDate: '2024-08-04',
              status: 'suspended'
            },
            stats: {
              products: 56,
              orders: 142,
              revenue: '₹52,800'
            }
          },
          {
            id: 5,
            name: 'Vikram Joshi',
            email: '<EMAIL>',
            phone: '+91 9876543214',
            storeName: 'Tech Solutions',
            storeUrl: 'tech-solutions',
            businessCategory: 'Services',
            isVerified: true,
            isActive: true,
            registeredDate: '2023-09-15',
            subscription: {
              plan: 'Premium',
              startDate: '2023-09-15',
              endDate: '2024-09-14',
              status: 'active'
            },
            stats: {
              products: 12,
              orders: 95,
              revenue: '₹78,500'
            }
          }
        ];
        
        // Filter vendors based on search term and status
        let filteredVendors = mockVendors;
        
        if (searchTerm) {
          filteredVendors = filteredVendors.filter(vendor => 
            vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            vendor.storeName.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (filterStatus !== 'all') {
          filteredVendors = filteredVendors.filter(vendor => {
            if (filterStatus === 'active') return vendor.isActive;
            if (filterStatus === 'inactive') return !vendor.isActive;
            if (filterStatus === 'verified') return vendor.isVerified;
            if (filterStatus === 'unverified') return !vendor.isVerified;
            return true;
          });
        }
        
        setVendors(filteredVendors);
        setTotalPages(Math.ceil(filteredVendors.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching vendors:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVendors();
  }, [searchTerm, filterStatus]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle verify vendor
  const handleVerifyVendor = (vendorId) => {
    if (window.confirm('Are you sure you want to verify this vendor?')) {
      // In a real implementation, this would be an API call
      const updatedVendors = vendors.map(vendor => 
        vendor.id === vendorId ? { ...vendor, isVerified: true } : vendor
      );
      setVendors(updatedVendors);
    }
  };

  // Handle toggle vendor status (active/inactive)
  const handleToggleVendorStatus = (vendorId) => {
    const vendor = vendors.find(v => v.id === vendorId);
    const action = vendor.isActive ? 'block' : 'unblock';
    
    if (window.confirm(`Are you sure you want to ${action} this vendor?`)) {
      // In a real implementation, this would be an API call
      const updatedVendors = vendors.map(vendor => 
        vendor.id === vendorId ? { ...vendor, isActive: !vendor.isActive } : vendor
      );
      setVendors(updatedVendors);
    }
  };

  // Handle delete vendor
  const handleDeleteVendor = (vendorId) => {
    if (window.confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      // In a real implementation, this would be an API call
      const updatedVendors = vendors.filter(vendor => vendor.id !== vendorId);
      setVendors(updatedVendors);
      setSelectedVendor(null);
    }
  };

  // Handle view vendor details
  const handleViewVendorDetails = (vendor) => {
    setSelectedVendor(vendor);
  };

  // Get paginated vendors
  const paginatedVendors = vendors.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Vendor Management</div>
          <h1 className="welcome-title">
            Manage <span style={{ color: '#25D366' }}>Vendors</span>
          </h1>
          <p className="welcome-subtitle">
            View, verify, and manage vendor accounts. Monitor vendor activity and control access to the platform.
          </p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Vendor Accounts</h2>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search vendors..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <button
              className="btn btn-outline"
              onClick={() => {
                setSearchTerm('');
                setFilterStatus('all');
              }}
            >
              <ArrowPathIcon className="h-5 w-5" />
              Reset
            </button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-6">
          <button
            className={`btn ${filterStatus === 'all' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('all')}
          >
            All Vendors
          </button>
          <button
            className={`btn ${filterStatus === 'active' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('active')}
          >
            Active
          </button>
          <button
            className={`btn ${filterStatus === 'inactive' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('inactive')}
          >
            Blocked
          </button>
          <button
            className={`btn ${filterStatus === 'verified' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('verified')}
          >
            Verified
          </button>
          <button
            className={`btn ${filterStatus === 'unverified' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('unverified')}
          >
            Unverified
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading vendors...</p>
          </div>
        ) : (
          <div className="card" style={{ padding: '0' }}>
            {vendors.length === 0 ? (
              <div style={{ padding: '24px', textAlign: 'center' }}>
                <p>No vendors found matching your criteria.</p>
              </div>
            ) : (
              <>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Vendor</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Store</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Subscription</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedVendors.map((vendor) => (
                      <tr key={vendor.id} className="border-b hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div>
                              <div className="font-medium text-gray-900">{vendor.name}</div>
                              <div className="text-sm text-gray-500">{vendor.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{vendor.storeName}</div>
                          <div className="text-xs text-gray-500">{vendor.businessCategory}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{vendor.subscription.plan}</div>
                          <div className="text-xs text-gray-500">
                            Expires: {new Date(vendor.subscription.endDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex flex-col space-y-1">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${vendor.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {vendor.isActive ? 'Active' : 'Blocked'}
                            </span>
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${vendor.isVerified ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {vendor.isVerified ? 'Verified' : 'Unverified'}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewVendorDetails(vendor)}
                              className="text-blue-600 hover:text-blue-900"
                              title="View Details"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </button>
                            {!vendor.isVerified && (
                              <button
                                onClick={() => handleVerifyVendor(vendor.id)}
                                className="text-green-600 hover:text-green-900"
                                title="Verify Vendor"
                              >
                                <ShieldCheckIcon className="h-5 w-5" />
                              </button>
                            )}
                            <button
                              onClick={() => handleToggleVendorStatus(vendor.id)}
                              className={`${vendor.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                              title={vendor.isActive ? 'Block Vendor' : 'Unblock Vendor'}
                            >
                              {vendor.isActive ? <XCircleIcon className="h-5 w-5" /> : <CheckCircleIcon className="h-5 w-5" />}
                            </button>
                            <button
                              onClick={() => handleDeleteVendor(vendor.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete Vendor"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">{Math.min(currentPage * 10, vendors.length)}</span> of{' '}
                        <span className="font-medium">{vendors.length}</span> vendors
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Vendor Details Modal */}
      {selectedVendor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-3xl w-full p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Vendor Details</h3>
              <button
                onClick={() => setSelectedVendor(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Personal Information</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Name:</span> {selectedVendor.name}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Email:</span> {selectedVendor.email}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Phone:</span> {selectedVendor.phone}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Registered:</span> {new Date(selectedVendor.registeredDate).toLocaleDateString()}</p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Store Information</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Store Name:</span> {selectedVendor.storeName}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Store URL:</span> {selectedVendor.storeUrl}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Category:</span> {selectedVendor.businessCategory}</p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Status:</span>{' '}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${selectedVendor.isVerified ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {selectedVendor.isVerified ? 'Verified' : 'Unverified'}
                    </span>
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Subscription Details</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Plan:</span> {selectedVendor.subscription.plan}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Start Date:</span> {new Date(selectedVendor.subscription.startDate).toLocaleDateString()}</p>
                  <p className="text-sm mb-2"><span className="font-medium">End Date:</span> {new Date(selectedVendor.subscription.endDate).toLocaleDateString()}</p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Status:</span>{' '}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${selectedVendor.subscription.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {selectedVendor.subscription.status === 'active' ? 'Active' : 'Suspended'}
                    </span>
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Store Statistics</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Products:</span> {selectedVendor.stats.products}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Orders:</span> {selectedVendor.stats.orders}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Revenue:</span> {selectedVendor.stats.revenue}</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setSelectedVendor(null)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
              {!selectedVendor.isVerified && (
                <button
                  type="button"
                  onClick={() => {
                    handleVerifyVendor(selectedVendor.id);
                    setSelectedVendor(null);
                  }}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Verify Vendor
                </button>
              )}
              <button
                type="button"
                onClick={() => {
                  handleToggleVendorStatus(selectedVendor.id);
                  setSelectedVendor(null);
                }}
                className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${selectedVendor.isActive ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
              >
                {selectedVendor.isActive ? 'Block Vendor' : 'Unblock Vendor'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
