import axios from 'axios';
import { API_URL } from '../config';

// Create axios instance with base URL
const api = axios.create({
  baseURL: `${API_URL}/services`,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Service API methods
export const serviceAPI = {
  // Create a new service
  createService: async (serviceData) => {
    try {
      return await api.post('/', serviceData);
    } catch (error) {
      throw error;
    }
  },

  // Get all services with optional pagination and filters
  getAllServices: async (page = 1, limit = 10, search = '', category = '') => {
    try {
      return await api.get('/', {
        params: { page, limit, search, category }
      });
    } catch (error) {
      throw error;
    }
  },

  // Get a single service by ID
  getServiceById: async (id) => {
    try {
      return await api.get(`/${id}`);
    } catch (error) {
      throw error;
    }
  },

  // Update a service
  updateService: async (id, serviceData) => {
    try {
      return await api.put(`/${id}`, serviceData);
    } catch (error) {
      throw error;
    }
  },

  // Delete a service
  deleteService: async (id) => {
    try {
      return await api.delete(`/${id}`);
    } catch (error) {
      throw error;
    }
  },

  // Get services by store ID (public)
  getServicesByStoreId: async (storeId) => {
    try {
      return await axios.get(`${API_URL}/services/store/${storeId}`);
    } catch (error) {
      throw error;
    }
  }
};
