import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import MarketingRoutes from './routes/marketing.routes';
import Marketing from './pages/admin/Marketing.jsx';
import { StoreProvider } from './contexts/StoreContext';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import './styles/preloader.css';
import { useState, useEffect } from 'react';

// Role-specific Layouts
import VendorDashboardLayout from './layouts/vendor/VendorDashboardLayout';
import AdminDashboardLayout from './layouts/admin/AdminDashboardLayout';
import InfluencerDashboardLayout from './layouts/influencer/InfluencerDashboardLayout';
import InvestorDashboardLayout from './layouts/investor/InvestorDashboardLayout';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import StoreThemeDemo from './pages/StoreThemeDemo';
import StorePage from './pages/StorePage';
import AboutUs from './pages/AboutUs';
import ContactUs from './pages/ContactUs';
import Blog from './pages/Blog';
import Press from './pages/Press';
import HelpCenter from './pages/HelpCenter';
import TermsOfService from './pages/TermsOfService';
import PrivacyPolicy from './pages/PrivacyPolicy';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminVendors from './pages/admin/AdminVendors';
import AdminUsers from './pages/admin/AdminUsers';
import AdminAnalytics from './pages/admin/AdminAnalytics';
import AdminSubscriptions from './pages/admin/AdminSubscriptions';
import AdminMessageTemplates from './pages/admin/AdminMessageTemplates';
import AdminAcademy from './pages/admin/AdminAcademy';
import AdminPermissions from './pages/admin/AdminPermissions';
import AdminSettings from './pages/admin/AdminSettings';
import AdminSupport from './pages/admin/AdminSupport';
import AdminInvestorManagement from './pages/admin/InvestorManagement';

// Vendor Pages
import VendorDashboard from './pages/vendor/VendorDashboard';
import VendorStore from './pages/vendor/VendorStore';
import VendorProducts from './pages/vendor/VendorProducts';
import VendorOrders from './pages/vendor/VendorOrders';
import VendorCustomers from './pages/vendor/VendorCustomers';
import VendorAnalytics from './pages/vendor/VendorAnalytics';
import VendorMessages from './pages/vendor/VendorMessages';
import VendorSettings from './pages/vendor/VendorSettings';
import VendorSupport from './pages/vendor/VendorSupport';
import VendorAcademy from './pages/vendor/VendorAcademy';
import PricingPlans from './pages/vendor/PricingPlans';

// Product Pages
import AddProduct from './pages/vendor/products/AddProduct';
import EditProduct from './pages/vendor/products/EditProduct';

// Service Pages
import VendorServices from './pages/vendor/VendorServices';
import AddService from './pages/vendor/services/AddService';
import EditService from './pages/vendor/services/EditService';

// Chat Flow Pages
import ChatFlowList from './pages/vendor/chat-flow/ChatFlowList';
import ChatFlowBuilder from './pages/vendor/chat-flow/ChatFlowBuilder';

// Influencer Pages
import InfluencerDashboard from './pages/influencer/InfluencerDashboard';
import InfluencerProfile from './pages/influencer/InfluencerProfile';
import InfluencerWithdrawals from './pages/influencer/InfluencerWithdrawals';
import InfluencerPerformance from './pages/influencer/InfluencerPerformance';
import InfluencerEarnings from './pages/influencer/InfluencerEarnings';
import InfluencerCampaigns from './pages/influencer/InfluencerCampaigns';
import InfluencerReferralLinks from './pages/influencer/InfluencerReferralLinks';
import InfluencerPromoMaterial from './pages/influencer/InfluencerPromoMaterial';
import InfluencerContent from './pages/influencer/InfluencerContent';
import InfluencerSupport from './pages/influencer/InfluencerSupport';

// Investor Pages
import InvestorDashboard from './pages/investor/InvestorDashboard';
import InvestorPerformance from './pages/investor/InvestorPerformance';
import InvestorInvestments from './pages/investor/InvestorInvestments';
import InvestorCapital from './pages/investor/InvestorCapital';
import InvestorEquity from './pages/investor/InvestorEquity';
import InvestorManagement from './pages/investor/InvestorManagement';
import InvestorVendorAnalytics from './pages/investor/InvestorVendorAnalytics';
import InvestorRevenueAnalytics from './pages/investor/InvestorRevenueAnalytics';
import InvestorProjections from './pages/investor/InvestorProjections';
import InvestorMarketExpansion from './pages/investor/InvestorMarketExpansion';
import InvestorRoadmap from './pages/investor/InvestorRoadmap';
import InvestorProfile from './pages/investor/InvestorProfile';
import NotFound from './pages/NotFound';

// Protected Route Component
function ProtectedRoute({ children, userType }) {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="preloader-spinner"></div>
      </div>
    );
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (userType && currentUser.userType !== userType) {
    return <Navigate to={`/${currentUser.userType}`} />;
  }

  return children;
}

function App() {
  return (
    <AuthProvider>
      <StoreProvider>
        <SubscriptionProvider>
          <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/store-theme-demo" element={<StoreThemeDemo />} />
          <Route path="/store/:storeId" element={<StorePage />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/press" element={<Press />} />
          <Route path="/help-center" element={<HelpCenter />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute userType="admin">
              <AdminDashboardLayout />
            </ProtectedRoute>
          }>
            <Route index element={<AdminDashboard />} />
            <Route path="vendors" element={<AdminVendors />} />
            <Route path="users" element={<AdminUsers />} />
            <Route path="analytics" element={<AdminAnalytics />} />
            <Route path="subscriptions" element={<AdminSubscriptions />} />
            <Route path="message-templates" element={<AdminMessageTemplates />} />
            <Route path="academy" element={<AdminAcademy />} />
            <Route path="permissions" element={<AdminPermissions />} />
            <Route path="settings" element={<AdminSettings />} />
            <Route path="support" element={<AdminSupport />} />
            <Route path="investment" element={<AdminInvestorManagement />} />
            <Route path="marketing" element={<Marketing />} />
          </Route>

          {/* Vendor Routes */}
          <Route path="/vendor" element={
            <ProtectedRoute userType="vendor">
              <VendorDashboardLayout />
            </ProtectedRoute>
          }>
            <Route index element={<VendorDashboard />} />
            <Route path="store" element={<VendorStore />} />
            <Route path="products" element={<VendorProducts />} />
            <Route path="products/add" element={<AddProduct />} />
            <Route path="products/edit/:id" element={<EditProduct />} />
            <Route path="services" element={<VendorServices />} />
            <Route path="services/add" element={<AddService />} />
            <Route path="services/edit/:id" element={<EditService />} />
            <Route path="orders" element={<VendorOrders />} />
            <Route path="customers" element={<VendorCustomers />} />
            <Route path="analytics" element={<VendorAnalytics />} />
            <Route path="messages" element={<VendorMessages />} />
            <Route path="settings" element={<VendorSettings />} />
            <Route path="pricing" element={<PricingPlans />} />
            <Route path="support" element={<VendorSupport />} />
            <Route path="academy" element={<VendorAcademy />} />
            <Route path="chat-flow" element={<ChatFlowList />} />
            <Route path="chat-flow/builder" element={<ChatFlowBuilder />} />
            <Route path="chat-flow/builder/:id" element={<ChatFlowBuilder />} />
          </Route>

          {/* Influencer Routes */}
          <Route path="/influencer" element={
            <ProtectedRoute userType="influencer">
              <InfluencerDashboardLayout />
            </ProtectedRoute>
          }>
            <Route index element={<InfluencerDashboard />} />
            <Route path="performance" element={<InfluencerPerformance />} />
            <Route path="earnings" element={<InfluencerEarnings />} />
            <Route path="withdrawals" element={<InfluencerWithdrawals />} />
            <Route path="campaigns" element={<InfluencerCampaigns />} />
            <Route path="referral-links" element={<InfluencerReferralLinks />} />
            <Route path="promo-material" element={<InfluencerPromoMaterial />} />
            <Route path="content" element={<InfluencerContent />} />
            <Route path="profile" element={<InfluencerProfile />} />
            <Route path="support" element={<InfluencerSupport />} />
          </Route>

          {/* Investor Routes */}
          <Route path="/investor" element={
            <ProtectedRoute userType="investor">
              <InvestorDashboardLayout />
            </ProtectedRoute>
          }>
            <Route index element={<InvestorDashboard />} />
            <Route path="performance" element={<InvestorPerformance />} />
            <Route path="investments" element={<InvestorInvestments />} />
            <Route path="capital" element={<InvestorCapital />} />
            <Route path="equity" element={<InvestorEquity />} />
            <Route path="management" element={<InvestorManagement />} />
            <Route path="vendor-analytics" element={<InvestorVendorAnalytics />} />
            <Route path="revenue-analytics" element={<InvestorRevenueAnalytics />} />
            <Route path="projections" element={<InvestorProjections />} />
            <Route path="market-expansion" element={<InvestorMarketExpansion />} />
            <Route path="roadmap" element={<InvestorRoadmap />} />
            <Route path="profile" element={<InvestorProfile />} />
          </Route>

          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
          </Routes>
        </SubscriptionProvider>
      </StoreProvider>
    </AuthProvider>
  );
}

export default App;