import { useState, useEffect } from 'react';
import {
  BanknotesIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  XMarkIcon,
  CheckIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorCapital() {
  const [loading, setLoading] = useState(true);
  const [capitalData, setCapitalData] = useState({
    capitalSummary: {
      totalInvested: 0,
      currentValue: 0,
      returnOnInvestment: 0,
      equityPercentage: 0
    },
    capitalTransactions: [],
    capitalProjections: {
      oneYear: 0,
      threeYear: 0,
      fiveYear: 0
    },
    equityValuation: {
      currentCompanyValuation: 0,
      equityValue: 0,
      projectedValuations: []
    },
    investmentOptions: []
  });
  const [showAddCapitalModal, setShowAddCapitalModal] = useState(false);
  const [formData, setFormData] = useState({
    amount: '',
    date: '',
    type: 'investment',
    notes: ''
  });
  const [formError, setFormError] = useState('');
  const [formSuccess, setFormSuccess] = useState(false);

  // Fetch capital data
  useEffect(() => {
    const fetchCapitalData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          capitalSummary: {
            totalInvested: 300000,
            currentValue: 345000,
            returnOnInvestment: 15,
            equityPercentage: 5,
            monthlyProfitShare: 0, // इन्वेस्टर को प्रॉफिट शेयर नहीं मिलेगा
            exitStrategy: "Equity sale after valuation growth"
          },
          capitalTransactions: [
            {
              id: 1,
              date: '2025-05-15',
              amount: 300000,
              type: 'investment',
              equityReceived: 5,
              notes: 'Initial investment'
            }
          ],
          capitalProjections: {
            oneYear: 1500000, // 5% of 3 करोड़
            threeYear: 15000000, // 5% of 30 करोड़
            fiveYear: 75000000 // 5% of 150 करोड़
          },
          equityValuation: {
            currentCompanyValuation: 30000000, // 3 करोड़
            equityValue: 1500000, // 5% of 3 करोड़
            projectedValuations: [
              { year: '2025', value: 30000000 }, // 3 करोड़
              { year: '2026', value: ********* }, // 10 करोड़
              { year: '2027', value: ********* }, // 30 करोड़
              { year: '2028', value: ********* }, // 75 करोड़
              { year: '2029', value: 1500000000 } // 150 करोड़
            ]
          },
          financialMetrics: {
            monthlyRevenue: 117000,
            monthlyExpenses: 87000,
            monthlyProfit: 30000,
            profitMargin: 25.6,
            cashReserves: 180000,
            burnRate: 87000,
            runway: 6.2,
            vendorAcquisitionCost: 200,
            lifetimeValuePerVendor: 2500
          },
          investmentOptions: [
            {
              id: 1,
              name: 'Growth Capital',
              minAmount: 300000,
              maxAmount: 500000,
              equityOffered: 5,
              description: 'Additional investment to fund market expansion and vendor acquisition.',
              benefits: [
                'Pro-rata rights in future funding rounds',
                'Quarterly investor updates',
                'Access to management team'
              ]
            },
            {
              id: 2,
              name: 'Strategic Partnership',
              minAmount: 500000,
              maxAmount: 1000000,
              equityOffered: 10,
              description: 'Strategic investment with management rights and board seat.',
              benefits: [
                'Board seat',
                'Management rights',
                'Monthly investor updates',
                'Direct access to founder'
              ]
            }
          ]
        };

        setCapitalData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching capital data:', error);
        setLoading(false);
      }
    };

    fetchCapitalData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.amount || !formData.date || !formData.type) {
      setFormError('Please fill in all required fields');
      return;
    }

    if (isNaN(formData.amount) || parseFloat(formData.amount) <= 0) {
      setFormError('Please enter a valid amount');
      return;
    }

    // In a real implementation, this would send the data to an API
    console.log('Form submitted:', formData);

    // Show success message
    setFormSuccess(true);
    setFormError('');

    // Reset form after 2 seconds
    setTimeout(() => {
      setFormSuccess(false);
      setShowAddCapitalModal(false);
      setFormData({
        amount: '',
        date: '',
        type: 'investment',
        notes: ''
      });
    }, 2000);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Capital Management</div>
          <h1 className="welcome-title">
            Manage Your <span style={{ color: '#25D366' }}>Capital</span>
          </h1>
          <p className="welcome-subtitle">
            Track your capital investments, view growth projections, and add new capital.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Actions */}
        <div className="flex justify-end mb-6">
          <button
            className="btn btn-primary"
            onClick={() => setShowAddCapitalModal(true)}
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Capital
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading capital data...</p>
          </div>
        ) : (
          <>
            {/* Capital Summary */}
            <div className="stats-grid mb-8">
              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                  <BanknotesIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Total Invested</h3>
                  <p className="stat-value">{formatCurrency(capitalData.capitalSummary.totalInvested)}</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                  <BanknotesIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Current Value</h3>
                  <p className="stat-value">{formatCurrency(capitalData.capitalSummary.currentValue)}</p>
                  <div className="stat-change">
                    <ArrowUpIcon className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">
                      +{capitalData.capitalSummary.returnOnInvestment}%
                    </span>
                  </div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="#25D366"
                    className="w-6 h-6"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 2v10l8.6 5" />
                  </svg>
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">Equity Percentage</h3>
                  <p className="stat-value">{formatPercentage(capitalData.capitalSummary.equityPercentage)}</p>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="#075E54"
                    className="w-6 h-6"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
                  </svg>
                </div>
                <div className="stat-content">
                  <h3 className="stat-label">ROI</h3>
                  <p className="stat-value">{formatPercentage(capitalData.capitalSummary.returnOnInvestment)}</p>
                </div>
              </div>
            </div>

            {/* Financial Metrics */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Financial Metrics</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Performance</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Monthly Revenue</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.financialMetrics.monthlyRevenue)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Monthly Expenses</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.financialMetrics.monthlyExpenses)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Monthly Profit</p>
                          <p className="text-xl font-bold text-green-600">{formatCurrency(capitalData.financialMetrics.monthlyProfit)}</p>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <ArrowUpIcon className="h-4 w-4 mr-1" />
                          {formatPercentage(capitalData.financialMetrics.profitMargin)}
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Exit Strategy</p>
                          <p className="text-base font-medium text-gray-900">{capitalData.capitalSummary.exitStrategy}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Business Health</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Cash Reserves</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.financialMetrics.cashReserves)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Monthly Burn Rate</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.financialMetrics.burnRate)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Runway</p>
                          <p className="text-xl font-bold text-gray-900">{capitalData.financialMetrics.runway} months</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Vendor Acquisition Cost</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.financialMetrics.vendorAcquisitionCost)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Capital Growth Projections */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Capital Growth Projections</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Projected Capital Value</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Current Value</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.capitalSummary.currentValue)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">1 Year Projection</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.capitalProjections.oneYear)}</p>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <ArrowUpIcon className="h-4 w-4 mr-1" />
                          {formatPercentage(Math.round((capitalData.capitalProjections.oneYear / capitalData.capitalSummary.currentValue - 1) * 100))}
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">3 Year Projection</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.capitalProjections.threeYear)}</p>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <ArrowUpIcon className="h-4 w-4 mr-1" />
                          {formatPercentage(Math.round((capitalData.capitalProjections.threeYear / capitalData.capitalSummary.currentValue - 1) * 100))}
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">5 Year Projection</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(capitalData.capitalProjections.fiveYear)}</p>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <ArrowUpIcon className="h-4 w-4 mr-1" />
                          {formatPercentage(Math.round((capitalData.capitalProjections.fiveYear / capitalData.capitalSummary.currentValue - 1) * 100))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Company Valuation Growth</h3>
                    <div style={{ height: '200px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {capitalData.equityValuation.projectedValuations.map((item, index) => {
                          const maxValue = Math.max(...capitalData.equityValuation.projectedValuations.map(i => i.value));
                          const height = (item.value / maxValue) * 180;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className="w-12 rounded-t-md"
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#25D366' : '#075E54'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900 mt-1">{formatCurrency(item.value)}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Capital Transactions */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Capital Transactions</h2>
                <button className="btn btn-outline btn-sm">
                  <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                  Export
                </button>
              </div>
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Equity Received
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Notes
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {capitalData.capitalTransactions.map((transaction) => (
                        <tr key={transaction.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(transaction.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {formatCurrency(transaction.amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {transaction.equityReceived ? formatPercentage(transaction.equityReceived) : '-'}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {transaction.notes}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Investment Options */}
            <div>
              <div className="section-header">
                <h2 className="section-title">Investment Options</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {capitalData.investmentOptions.map((option) => (
                  <div key={option.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{option.name}</h3>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Investment Range</p>
                        <p className="text-lg font-bold text-gray-900">
                          {formatCurrency(option.minAmount)} - {formatCurrency(option.maxAmount)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Equity Offered</p>
                        <p className="text-lg font-bold text-green-600">{formatPercentage(option.equityOffered)}</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{option.description}</p>
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">Benefits:</p>
                      <ul className="text-sm text-gray-600 space-y-1 pl-5 list-disc">
                        {option.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex justify-end">
                      <button
                        className="btn btn-primary"
                        onClick={() => {
                          setFormData({
                            ...formData,
                            amount: option.minAmount.toString(),
                            type: 'investment'
                          });
                          setShowAddCapitalModal(true);
                        }}
                      >
                        Invest Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Add Capital Modal */}
      {showAddCapitalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">Add Capital</h3>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => {
                  setShowAddCapitalModal(false);
                  setFormError('');
                  setFormSuccess(false);
                }}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4">
              {formSuccess ? (
                <div className="success-message mb-4">
                  <CheckIcon className="h-5 w-5 mr-2" />
                  Capital added successfully!
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  {formError && (
                    <div className="error-message mb-4">
                      {formError}
                    </div>
                  )}

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span className="text-gray-500">₹</span>
                        </div>
                        <input
                          type="number"
                          name="amount"
                          value={formData.amount}
                          onChange={handleInputChange}
                          className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="500000"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date *
                      </label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Type *
                      </label>
                      <select
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        required
                      >
                        <option value="investment">Investment</option>
                        <option value="loan">Loan</option>
                        <option value="grant">Grant</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Any additional notes about this capital addition..."
                      ></textarea>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      className="btn btn-outline mr-3"
                      onClick={() => {
                        setShowAddCapitalModal(false);
                        setFormError('');
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      <CheckIcon className="h-5 w-5 mr-2" />
                      Add Capital
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
