<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Order::class, 'order');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $orders = Auth::user()->store->orders()->with('customer')->latest()->paginate(10);
        return response()->json($orders);
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        // Eager load items and customer information
        $order->load('items.product', 'customer');
        return response()->json($order);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        $validatedData = $request->validate([
            'status' => 'sometimes|in:pending,processing,completed,shipped,cancelled,refunded',
            'tracking_number' => 'nullable|string|max:255',
        ]);

        $order->update($validatedData);

        return response()->json([
            'message' => 'Order updated successfully.',
            'order' => $order
        ]);
    }
}
