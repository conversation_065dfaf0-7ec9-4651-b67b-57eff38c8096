import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  CurrencyRupeeIcon,
  ArrowPathIcon,
  BanknotesIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerEarnings() {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [earningsData, setEarningsData] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch earnings data
  useEffect(() => {
    const fetchEarningsData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          overview: {
            totalEarnings: 12450,
            availableBalance: 2700,
            pendingBalance: 1500,
            withdrawnAmount: 8250
          },
          earningsByMonth: [
            { month: 'Jan', amount: 450 },
            { month: 'Feb', amount: 750 },
            { month: 'Mar', amount: 900 },
            { month: 'Apr', amount: 600 },
            { month: 'May', amount: 750 },
            { month: 'Jun', amount: 1050 },
            { month: 'Jul', amount: 1200 },
            { month: 'Aug', amount: 1500 },
            { month: 'Sep', amount: 1800 },
            { month: 'Oct', amount: 2400 },
            { month: 'Nov', amount: 0 },
            { month: 'Dec', amount: 0 }
          ],
          earningsByPlan: [
            { plan: 'Standard', amount: 4500 },
            { plan: 'Gold', amount: 6000 },
            { plan: 'Premium', amount: 1950 }
          ]
        };
        
        const mockTransactions = [
          { id: 1, type: 'commission', amount: 1500, date: '2023-10-24', status: 'completed', description: 'Commission from Priya Patel', reference: 'COM123456' },
          { id: 2, type: 'commission', amount: 1200, date: '2023-10-22', status: 'completed', description: 'Commission from Neha Singh', reference: 'COM123455' },
          { id: 3, type: 'withdrawal', amount: -2700, date: '2023-10-20', status: 'completed', description: 'Withdrawal to Bank Account', reference: 'WD123456' },
          { id: 4, type: 'commission', amount: 900, date: '2023-10-20', status: 'completed', description: 'Commission from Ananya Desai', reference: 'COM123454' },
          { id: 5, type: 'commission', amount: 1800, date: '2023-10-17', status: 'completed', description: 'Commission from Kiran Shah', reference: 'COM123453' },
          { id: 6, type: 'withdrawal', amount: -1800, date: '2023-10-15', status: 'completed', description: 'Withdrawal to UPI', reference: 'WD123455' },
          { id: 7, type: 'commission', amount: 1350, date: '2023-10-15', status: 'completed', description: 'Commission from Pooja Mehta', reference: 'COM123452' },
          { id: 8, type: 'commission', amount: 2100, date: '2023-10-12', status: 'completed', description: 'Commission from Ravi Patel', reference: 'COM123451' },
          { id: 9, type: 'withdrawal', amount: -3750, date: '2023-10-10', status: 'completed', description: 'Withdrawal to Bank Account', reference: 'WD123454' },
          { id: 10, type: 'commission', amount: 1650, date: '2023-10-08', status: 'completed', description: 'Commission from Anil Kapoor', reference: 'COM123450' },
          { id: 11, type: 'commission', amount: 1500, date: '2023-10-05', status: 'pending', description: 'Commission from Suresh Kumar', reference: 'COM123449' },
          { id: 12, type: 'commission', amount: 1200, date: '2023-10-03', status: 'pending', description: 'Commission from Meena Verma', reference: 'COM123448' },
          { id: 13, type: 'commission', amount: 750, date: '2023-10-01', status: 'rejected', description: 'Commission from Rajiv Malhotra', reference: 'COM123447', rejectionReason: 'Refund processed' },
          { id: 14, type: 'commission', amount: 1050, date: '2023-09-28', status: 'completed', description: 'Commission from Deepak Sharma', reference: 'COM123446' },
          { id: 15, type: 'commission', amount: 900, date: '2023-09-25', status: 'completed', description: 'Commission from Kavita Patel', reference: 'COM123445' }
        ];
        
        setEarningsData(mockData);
        setTransactions(mockTransactions);
        setTotalPages(Math.ceil(mockTransactions.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching earnings data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEarningsData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return { color: 'bg-green-100 text-green-800', text: 'Completed', icon: CheckCircleIcon };
      case 'pending':
        return { color: 'bg-yellow-100 text-yellow-800', text: 'Pending', icon: ClockIcon };
      case 'rejected':
        return { color: 'bg-red-100 text-red-800', text: 'Rejected', icon: XCircleIcon };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown', icon: ClockIcon };
    }
  };

  // Get transaction type badge
  const getTypeBadge = (type, amount) => {
    switch (type) {
      case 'commission':
        return { color: 'bg-green-100 text-green-800', text: 'Commission' };
      case 'withdrawal':
        return { color: 'bg-blue-100 text-blue-800', text: 'Withdrawal' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: type.charAt(0).toUpperCase() + type.slice(1) };
    }
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Get paginated transactions
  const paginatedTransactions = transactions.slice((currentPage - 1) * 10, currentPage * 10);

  // Download earnings report
  const handleDownloadReport = () => {
    alert('Earnings report download started. This would be a CSV or PDF file in a real implementation.');
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Earnings Management</div>
          <h1 className="welcome-title">
            Your <span style={{ color: '#25D366' }}>Earnings</span>
          </h1>
          <p className="welcome-subtitle">
            Track your earnings, view transaction history, and manage your commissions.
          </p>
        </div>
      </div>

      {/* Time Range Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Earnings Overview</h2>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 90 Days</option>
              <option value="year">Last 12 Months</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 500);
              }}
            >
              <ArrowPathIcon className="h-5 w-5" />
              Refresh
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading earnings data...</p>
          </div>
        ) : (
          <>
            {/* Earnings Summary */}
            <div className="card" style={{ padding: '24px' }}>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Total Earnings</p>
                  <p className="text-2xl font-bold text-purple-600">{formatCurrency(earningsData?.overview.totalEarnings || 0)}</p>
                  <p className="text-xs text-gray-500 mt-2">Lifetime earnings</p>
                </div>
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Available Balance</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(earningsData?.overview.availableBalance || 0)}</p>
                  <Link to="/influencer/withdrawals" className="text-xs text-green-600 mt-2 hover:underline">
                    Request Withdrawal
                  </Link>
                </div>
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Pending Balance</p>
                  <p className="text-2xl font-bold text-blue-600">{formatCurrency(earningsData?.overview.pendingBalance || 0)}</p>
                  <div className="flex items-center text-xs text-gray-500 mt-2">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    <span>7-day holding period</span>
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-500 mb-1">Withdrawn Amount</p>
                  <p className="text-2xl font-bold text-gray-600">{formatCurrency(earningsData?.overview.withdrawnAmount || 0)}</p>
                  <p className="text-xs text-gray-500 mt-2">Total withdrawals</p>
                </div>
              </div>
            </div>

            {/* Monthly Earnings Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Monthly Earnings</h2>
                <button
                  className="btn btn-outline"
                  onClick={handleDownloadReport}
                >
                  <ArrowDownTrayIcon className="h-5 w-5" />
                  Download Report
                </button>
              </div>
              {earningsData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div style={{ height: '300px', position: 'relative' }}>
                    <div className="chart-container">
                      <div className="flex justify-between items-end h-64 relative">
                        <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                          <span>₹2.5K</span>
                          <span>₹2K</span>
                          <span>₹1.5K</span>
                          <span>₹1K</span>
                          <span>₹500</span>
                          <span>₹0</span>
                        </div>
                        <div className="ml-10 flex-1 flex items-end justify-between">
                          {earningsData.earningsByMonth.map((item, index) => (
                            <div key={index} className="flex flex-col items-center">
                              <div 
                                className="w-8 bg-green-500 rounded-t"
                                style={{ 
                                  height: `${(item.amount / 2500) * 240}px`,
                                  backgroundColor: '#25D366'
                                }}
                              ></div>
                              <span className="text-xs mt-1 text-gray-500">{item.month}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Earnings by Plan */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Earnings by Subscription Plan</h2>
              </div>
              {earningsData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="space-y-4">
                    {earningsData.earningsByPlan.map((plan) => (
                      <div key={plan.plan} className="flex items-center">
                        <div className="w-32 text-sm font-medium">{plan.plan}</div>
                        <div className="flex-1">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div 
                              className="bg-green-600 h-2.5 rounded-full" 
                              style={{ 
                                width: `${(plan.amount / earningsData.overview.totalEarnings) * 100}%`,
                                backgroundColor: '#25D366'
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-24 text-right text-sm font-medium">{formatCurrency(plan.amount)}</div>
                        <div className="w-16 text-right text-sm text-gray-500">
                          {Math.round((plan.amount / earningsData.overview.totalEarnings) * 100)}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Transaction History */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Transaction History</h2>
              </div>

              <div className="card" style={{ padding: '0' }}>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Date</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Description</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Reference</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Type</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Amount</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedTransactions.map((transaction) => {
                      const statusBadge = getStatusBadge(transaction.status);
                      const typeBadge = getTypeBadge(transaction.type, transaction.amount);
                      const StatusIcon = statusBadge.icon;
                      return (
                        <tr key={transaction.id} className="border-b hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-500">{formatDate(transaction.date)}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-900">{transaction.description}</div>
                            {transaction.rejectionReason && (
                              <div className="text-xs text-red-500 mt-1">{transaction.rejectionReason}</div>
                            )}
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-500">{transaction.reference}</div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${typeBadge.color}`}>
                              {typeBadge.text}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className={`text-sm font-medium ${transaction.amount > 0 ? 'text-green-600' : 'text-blue-600'}`}>
                              {formatCurrency(transaction.amount)}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color} flex items-center`}>
                                <StatusIcon className="h-4 w-4 mr-1" />
                                {statusBadge.text}
                              </span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">{Math.min(currentPage * 10, transactions.length)}</span> of{' '}
                        <span className="font-medium">{transactions.length}</span> transactions
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Earnings Information */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Earnings Information</h2>
              </div>

              <div className="card" style={{ padding: '24px' }}>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Commission Structure</h3>
                    <p className="text-sm text-gray-500">
                      You earn commissions based on the subscription plan purchased by your referrals:
                    </p>
                    <ul className="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
                      <li>Standard Plan (₹1,499/year): 15% commission (₹225)</li>
                      <li>Gold Plan (₹3,000/year): 20% commission (₹600)</li>
                      <li>Premium Plan (₹4,999/year): 25% commission (₹1,250)</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Schedule</h3>
                    <p className="text-sm text-gray-500">
                      Commissions are subject to a 7-day holding period before they become available for withdrawal. This is to allow for potential refunds or disputes.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Withdrawal Options</h3>
                    <p className="text-sm text-gray-500">
                      You can withdraw your available balance through Bank Transfer, UPI, or Paytm. The minimum withdrawal amount is ₹100.
                    </p>
                    <div className="mt-2">
                      <Link to="/influencer/withdrawals" className="btn btn-primary">
                        <BanknotesIcon style={{ width: '16px', height: '16px' }} />
                        Request Withdrawal
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
