import { useState, useEffect, useRef } from 'react';
import { PaperAirplaneIcon, PaperClipIcon, MicrophoneIcon } from '@heroicons/react/24/solid';

export default function ChatDemo() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "👋 Welcome to Whamart Store! How can I help you today?\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us",
      sender: 'bot',
      timestamp: '10:30 AM'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (inputMessage.trim() === '') return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages([...messages, userMessage]);
    setInputMessage('');

    // Simulate bot response based on user input
    setTimeout(() => {
      let botResponse;
      
      if (inputMessage === '1' || inputMessage.toLowerCase().includes('product')) {
        botResponse = {
          id: messages.length + 2,
          text: "Here are our latest products:\n\n🔹 Premium WhatsApp Theme - $29\n🔹 Basic Store Setup - $49\n🔹 Pro Store Package - $99\n\nReply with the product name to learn more.",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '2' || inputMessage.toLowerCase().includes('order')) {
        botResponse = {
          id: messages.length + 2,
          text: "Please provide your order number to check the status.",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '3' || inputMessage.toLowerCase().includes('support')) {
        botResponse = {
          id: messages.length + 2,
          text: "Our support team is available Monday-Friday, 9am-5pm. How can we help you today?",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '4' || inputMessage.toLowerCase().includes('about')) {
        botResponse = {
          id: messages.length + 2,
          text: "Whamart is a WhatsApp-themed e-commerce platform that helps small shop owners create WhatsApp-style stores with chat interface functionality.",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else {
        botResponse = {
          id: messages.length + 2,
          text: "I'm not sure I understand. Please select one of the options:\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      }

      setMessages(prevMessages => [...prevMessages, botResponse]);
    }, 1000);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="w-full max-w-md mx-auto bg-gray-100 rounded-xl shadow-lg overflow-hidden">
      {/* Chat header */}
      <div className="bg-whatsapp-teal text-white p-4 flex items-center">
        <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
            <path fillRule="evenodd" d="M7.5 6v.75H5.513c-.96 0-1.764.724-1.865 1.679l-1.263 12A1.875 1.875 0 0 0 4.25 22.5h15.5a1.875 1.875 0 0 0 1.865-2.071l-1.263-12a1.875 1.875 0 0 0-1.865-1.679H16.5V6a4.5 4.5 0 1 0-9 0ZM12 3a3 3 0 0 0-3 3v.75h6V6a3 3 0 0 0-3-3Zm-3 8.25a3 3 0 1 0 6 0v-.75a.75.75 0 0 1 1.5 0v.75a4.5 4.5 0 1 1-9 0v-.75a.75.75 0 0 1 1.5 0v.75Z" clipRule="evenodd" />
          </svg>
        </div>
        <div>
          <h2 className="font-medium">Whamart Store</h2>
          <p className="text-xs opacity-80">Online</p>
        </div>
      </div>
      
      {/* Chat messages */}
      <div className="h-96 overflow-y-auto p-4 bg-[#e5ddd5] bg-opacity-70 bg-[url('https://web.whatsapp.com/img/bg-chat-tile-light_04fcacde539c58cca6745483d4858c52.png')]">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
          >
            <div className={`rounded-lg px-4 py-2 max-w-[80%] shadow-sm ${
              message.sender === 'user' ? 'bg-[#dcf8c6]' : 'bg-white'
            }`}>
              <p className="text-sm whitespace-pre-line">{message.text}</p>
              <p className="text-right text-xs text-gray-500 mt-1">{message.timestamp}</p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Chat input */}
      <div className="bg-gray-200 p-3">
        <div className="flex items-center bg-white rounded-full px-4 py-2">
          <button className="text-gray-500 mr-2">
            <PaperClipIcon className="h-5 w-5" />
          </button>
          
          <input
            type="text"
            placeholder="Type a message"
            className="flex-1 border-none outline-none bg-transparent"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          
          {inputMessage ? (
            <button 
              className="text-whatsapp-default"
              onClick={handleSendMessage}
            >
              <PaperAirplaneIcon className="h-5 w-5 transform rotate-90" />
            </button>
          ) : (
            <button className="text-gray-500">
              <MicrophoneIcon className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
