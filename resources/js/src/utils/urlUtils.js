/**
 * URL Utilities for Whamart
 * 
 * Functions to handle URL encoding/decoding and formatting
 */

/**
 * Safely formats a store name for use in URLs
 * Removes apostrophes and other special characters
 * 
 * @param {string} name - The store name to format
 * @returns {string} - URL-safe store name
 */
export const formatStoreNameForUrl = (name) => {
  if (!name) return '';
  
  return name
    .toLowerCase()
    .replace(/'/g, '') // Remove apostrophes
    .replace(/[^a-z0-9-]/g, '-') // Replace other special chars with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with a single one
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generates a complete store URL
 * 
 * @param {string} storeName - The name of the store
 * @param {string|number} storeId - The store ID
 * @returns {string} - Complete store URL
 */
export const generateStoreUrl = (storeName, storeId) => {
  const formattedName = formatStoreNameForUrl(storeName);
  return `${formattedName}-${storeId}`;
};

/**
 * Builds a full store URL with domain
 * 
 * @param {string} storeUrl - The store URL path
 * @returns {string} - Full store URL with domain
 */
export const getFullStoreUrl = (storeUrl) => {
  return `https://whamart.shop/store/${storeUrl}`;
};

/**
 * Extracts store ID from a store URL
 * 
 * @param {string} storeUrl - The store URL
 * @returns {string|null} - The store ID or null if not found
 */
export const extractStoreIdFromUrl = (storeUrl) => {
  if (!storeUrl) return null;
  
  // Look for the last dash followed by numbers at the end of the URL
  const match = storeUrl.match(/-(\d+)$/);
  return match ? match[1] : null;
};
