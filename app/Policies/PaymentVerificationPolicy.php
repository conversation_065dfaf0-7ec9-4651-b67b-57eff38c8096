<?php

namespace App\Policies;

use App\Models\PaymentVerification;
use App\Models\User;
use App\Models\Order;
use Illuminate\Auth\Access\Response;

class PaymentVerificationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Anyone can attempt to view, but it will be filtered by order in the controller.
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, PaymentVerification $paymentVerification): bool
    {
        $order = $paymentVerification->order;
        // Allow if the user is the customer who placed the order OR the vendor of the store.
        return $user->id === $order->customer->user_id || $user->id === $order->store->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user, array $attributes): bool
    {
        $order = Order::find($attributes['order_id']);
        // Allow if the user is the customer who placed the order.
        return $order && $user->id === $order->customer->user_id;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, PaymentVerification $paymentVerification): bool
    {
        // Allow if the user is the vendor of the store where the order was placed.
        return $user->id === $paymentVerification->order->store->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, PaymentVerification $paymentVerification): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, PaymentVerification $paymentVerification): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, PaymentVerification $paymentVerification): bool
    {
        return false;
    }
}
