<?php

namespace App\Helpers;

class PaymentHelper
{
    /**
     * Generate a unique reference ID for payment verification.
     *
     * @return string
     */
    public static function generatePaymentReferenceId(): string
    {
        $timestamp = substr(time(), -6);
        $random = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
        return "PAY{$timestamp}{$random}";
    }

    /**
     * Generate a UPI payment QR code.
     *
     * @param  string  $upiId
     * @param  string  $businessName
     * @param  float|null  $amount
     * @param  string|null  $reference
     * @return string|null The QR code as a base64 encoded string or null on failure.
     */
    public static function generateUpiQrCode(string $upiId, string $businessName, ?float $amount = null, ?string $reference = null): ?string
    {
        // TODO: Implement this after installing the 'simplesoftwareio/simple-qrcode' package.
        // This is currently blocked by Docker permission issues.
        return null;
    }
}
