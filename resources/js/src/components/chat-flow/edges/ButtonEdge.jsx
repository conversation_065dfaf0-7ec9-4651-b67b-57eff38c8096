import React, { memo } from 'react';
import { BaseEdge, EdgeLabelRenderer, getBezierPath } from 'reactflow';
import { XMarkIcon } from '@heroicons/react/24/outline';

/**
 * ButtonEdge component for the Chat Flow Builder
 * Represents a connection between messages with button label using curved bezier lines
 */
const ButtonEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  markerEnd,
  style = {}
}) => {
  // Get path for the edge - using bezier path for smooth curved connections
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    curvature: 0.4 // Adjust curvature for more pronounced curves
  });

  // Get button text from data
  const buttonText = data?.buttonText || 'Button';

  return (
    <>
      {/* Base edge with path */}
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={{ ...style, stroke: '#25D366', strokeWidth: 2 }} />

      {/* Edge label with button text */}
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            pointerEvents: 'all',
            background: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: 12,
            fontWeight: 500,
            border: '1px solid #25D366',
            color: '#25D366'
          }}
          className="nodrag nopan shadow-sm"
        >
          {buttonText}
        </div>
      </EdgeLabelRenderer>

      {/* Delete button */}
      <EdgeLabelRenderer>
        <button
          className="nodrag nopan absolute flex items-center justify-center w-5 h-5 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors"
          style={{
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY - 20}px)`,
            fontSize: 10
          }}
          onClick={(event) => {
            event.stopPropagation();
            if (data?.onDelete) {
              data.onDelete(id);
            }
          }}
        >
          <XMarkIcon className="h-3 w-3" />
        </button>
      </EdgeLabelRenderer>
    </>
  );
};

export default memo(ButtonEdge);
