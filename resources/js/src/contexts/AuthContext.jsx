import { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/api';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is already logged in on component mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, []);

  // Fetch current user data from the backend
  const fetchCurrentUser = async () => {
    try {
      setLoading(true);

      // Check if we have a mock admin token
      const token = localStorage.getItem('token');
      if (token === 'admin-mock-token') {
        console.log('Using mock admin user from token');

        // Create a mock admin user
        const adminUser = {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          userType: 'admin'
        };

        setCurrentUser(adminUser);
        setError(null);
        return;
      }

      // Check if we have a mock influencer token
      if (token === 'influencer-mock-token') {
        console.log('Using mock influencer user from token');

        // Create a mock influencer user
        const influencerUser = {
          id: 2,
          name: 'Rahul Sharma',
          email: '<EMAIL>',
          userType: 'influencer'
        };

        setCurrentUser(influencerUser);
        setError(null);
        return;
      }

      // Check if we have a mock investor token
      if (token === 'investor-mock-token') {
        console.log('Using mock investor user from token');

        // Create a mock investor user
        const investorUser = {
          id: 3,
          name: 'Amit Patel',
          email: '<EMAIL>',
          userType: 'investor',
          profileImage: 'https://randomuser.me/api/portraits/men/5.jpg'
        };

        setCurrentUser(investorUser);
        setError(null);
        return;
      }

      // Regular API call
      const response = await authAPI.getCurrentUser();
      setCurrentUser(response.data.user);
      setError(null);
    } catch (err) {
      console.error('Error fetching current user:', err);
      localStorage.removeItem('token');
      setError('Session expired. Please login again.');
    } finally {
      setLoading(false);
    }
  };

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true);

      // Special case for admin login when backend might not be available
      if (email === '<EMAIL>' && password === 'admin123') {
        console.log('Using hardcoded admin login');

        // Create a mock admin user
        const adminUser = {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          userType: 'admin'
        };

        // Set a mock token in localStorage (if not already set by the Login component)
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'admin-mock-token');
        }

        // Set current user
        setCurrentUser(adminUser);
        setError(null);
        return adminUser;
      }

      // Special case for influencer login when backend might not be available
      if (email === '<EMAIL>' && password === 'influencer123') {
        console.log('Using hardcoded influencer login');

        // Create a mock influencer user
        const influencerUser = {
          id: 2,
          name: 'Rahul Sharma',
          email: '<EMAIL>',
          userType: 'influencer'
        };

        // Set a mock token in localStorage (if not already set by the Login component)
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'influencer-mock-token');
        }

        // Set current user
        setCurrentUser(influencerUser);
        setError(null);
        return influencerUser;
      }

      // Special case for investor login when backend might not be available
      if (email === '<EMAIL>' && password === 'investor123') {
        console.log('Using hardcoded investor login');

        // Create a mock investor user
        const investorUser = {
          id: 3,
          name: 'Amit Patel',
          email: '<EMAIL>',
          userType: 'investor',
          profileImage: 'https://randomuser.me/api/portraits/men/5.jpg'
        };

        // Set a mock token in localStorage (if not already set by the Login component)
        if (!localStorage.getItem('token')) {
          localStorage.setItem('token', 'investor-mock-token');
        }

        // Set current user
        setCurrentUser(investorUser);
        setError(null);
        return investorUser;
      }

      // Regular API login
      const response = await authAPI.login({ email, password });
      const { access_token, user } = response.data;

      // Save token to localStorage
      localStorage.setItem('token', access_token);

      // Set current user
      setCurrentUser(user);
      setError(null);
      return user;
    } catch (err) {
      console.error('Login error:', err);
      setError(err.response?.data?.message || 'Failed to login');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    localStorage.removeItem('token');
    setCurrentUser(null);
    return Promise.resolve();
  };

  // Update user's store information
  const updateUserStore = (storeData) => {
    if (currentUser && currentUser.store) {
      setCurrentUser({
        ...currentUser,
        store: {
          ...currentUser.store,
          ...storeData
        }
      });
    }
  };

  // Register new user
  const register = async (email, password, name, userType) => {
    try {
      setLoading(true);
      const response = await authAPI.register({ email, password, name, userType });
      const { token, user } = response.data;

      // Save token to localStorage
      localStorage.setItem('token', token);

      // Set current user
      setCurrentUser(user);
      setError(null);
      return user;
    } catch (err) {
//       // Removed console.error
      setError(err.response?.data?.message || 'Failed to register');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    currentUser,
    login,
    logout,
    register,
    updateUserStore,
    loading,
    error
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
