<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\SubscriptionPlan::create([
            'name' => 'Basic',
            'price' => 999,
            'interval' => 'monthly',
            'features' => json_encode([
                'Up to 10 products',
                'Basic analytics',
                'Email support',
            ]),
        ]);

        \App\Models\SubscriptionPlan::create([
            'name' => 'Pro',
            'price' => 2499,
            'interval' => 'monthly',
            'features' => json_encode([
                'Up to 50 products',
                'Advanced analytics',
                'Priority email support',
                'Theme customization',
            ]),
        ]);

        \App\Models\SubscriptionPlan::create([
            'name' => 'Enterprise',
            'price' => 4999,
            'interval' => 'monthly',
            'features' => json_encode([
                'Unlimited products',
                'Advanced analytics',
                '24/7 phone and email support',
                'Theme customization',
                'API access',
            ]),
        ]);
    }
}
