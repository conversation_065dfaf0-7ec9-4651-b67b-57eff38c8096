import { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  UserGroupIcon,
  UserIcon,
  DocumentArrowDownIcon,
  CurrencyRupeeIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorEquity() {
  const [loading, setLoading] = useState(true);
  const [equityData, setEquityData] = useState({
    equityDistribution: [],
    companyValuation: {
      current: 0,
      previousRound: 0,
      growth: 0,
      projectedValuations: []
    },
    investmentDetails: {
      initialInvestment: 0,
      investmentDate: new Date().toISOString(),
      equityPercentage: 0,
      currentValue: 0,
      roi: 0
    },
    equityDocuments: [],
    equityRights: {},
    futureRounds: {
      nextRound: {
        targetDate: new Date().toISOString(),
        targetValuation: 0,
        equityOffered: 0,
        fundingTarget: 0
      },
      dilutionImpact: {
        currentEquity: 0,
        postDilution: 0,
        value: {
          current: 0,
          postDilution: 0
        }
      }
    }
  });

  // Fetch equity data
  useEffect(() => {
    const fetchEquityData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          equityDistribution: [
            { holder: 'Founder (Karan Solanki)', percentage: 60, value: 4140000, color: '#25D366' },
            { holder: 'Current Investor', percentage: 5, value: 345000, color: '#075E54' },
            { holder: 'Reserved for Future Investors', percentage: 20, value: 1380000, color: '#128C7E' },
            { holder: 'Employee Stock Options', percentage: 10, value: 690000, color: '#34B7F1' },
            { holder: 'Advisors', percentage: 5, value: 345000, color: '#ECE5DD' }
          ],
          companyValuation: {
            current: 6900000,
            previousRound: 6000000,
            growth: 15,
            projectedValuations: [
              { year: '2023', value: 6900000 },
              { year: '2024', value: 10400000 },
              { year: '2025', value: 15600000 },
              { year: '2026', value: 23400000 },
              { year: '2027', value: 35100000 }
            ]
          },
          investmentDetails: {
            initialInvestment: 300000,
            investmentDate: '2023-01-01',
            equityPercentage: 5,
            currentValue: 345000,
            roi: 15
          },
          equityDocuments: [
            { id: 1, name: 'Term Sheet.pdf', date: '2023-01-01', url: '#' },
            { id: 2, name: 'Shareholders Agreement.pdf', date: '2023-01-01', url: '#' },
            { id: 3, name: 'Investment Agreement.pdf', date: '2023-01-01', url: '#' },
            { id: 4, name: 'Cap Table.xlsx', date: '2023-01-15', url: '#' },
            { id: 5, name: 'Valuation Report.pdf', date: '2023-01-15', url: '#' }
          ],
          equityRights: {
            votingRights: 'Proportional to equity percentage',
            boardSeat: 'Available for investors with 10%+ equity',
            proRataRights: 'Yes, for all investors',
            liquidationPreference: '1x for all investors',
            dividendRights: 'Non-participating preferred',
            informationRights: 'Quarterly financial reports and annual audited statements'
          },
          futureRounds: {
            nextRound: {
              targetDate: '2024-06-30',
              targetValuation: 15000000,
              equityOffered: 15,
              fundingTarget: 2250000
            },
            dilutionImpact: {
              currentEquity: 5,
              postDilution: 4.25,
              value: {
                current: 345000,
                postDilution: 637500
              }
            }
          }
        };

        setEquityData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching equity data:', error);
        setLoading(false);
      }
    };

    fetchEquityData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Ownership Structure</div>
          <h1 className="welcome-title">
            Equity <span style={{ color: '#25D366' }}>Distribution</span>
          </h1>
          <p className="welcome-subtitle">
            View equity distribution, company valuation, and shareholder rights.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Export Controls */}
        <div className="flex justify-end mb-6">
          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Cap Table
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading equity data...</p>
          </div>
        ) : (
          <>
            {/* Equity Distribution */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="md:col-span-2">
                <div className="card" style={{ padding: '24px' }}>
                  <h2 className="text-xl font-semibold mb-6">Equity Distribution</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-center">
                      <div className="relative" style={{ width: '200px', height: '200px' }}>
                        {/* This would be a real pie chart in production */}
                        <div className="bg-gray-100 rounded-full w-full h-full flex items-center justify-center">
                          <span className="text-gray-500">Equity Chart</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div className="space-y-3">
                        {equityData.equityDistribution.map((item, index) => (
                          <div key={index} className="flex items-center">
                            <div className="w-4 h-4 rounded-sm mr-2" style={{ backgroundColor: item.color }}></div>
                            <div className="flex-1 text-sm">{item.holder}</div>
                            <div className="text-sm font-medium">{formatPercentage(item.percentage)}</div>
                          </div>
                        ))}
                      </div>

                      <div className="mt-6 pt-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">Total Company Valuation</span>
                          <span className="text-lg font-bold text-gray-900">{formatCurrency(equityData.companyValuation.current)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div className="card" style={{ padding: '24px' }}>
                  <h2 className="text-xl font-semibold mb-4">Your Investment</h2>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Initial Investment</p>
                      <p className="text-xl font-bold text-gray-900">{formatCurrency(equityData.investmentDetails.initialInvestment)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Current Value</p>
                      <p className="text-xl font-bold text-green-600">{formatCurrency(equityData.investmentDetails.currentValue)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Equity Percentage</p>
                      <p className="text-xl font-bold text-gray-900">{formatPercentage(equityData.investmentDetails.equityPercentage)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Return on Investment</p>
                      <p className="text-xl font-bold text-green-600">{formatPercentage(equityData.investmentDetails.roi)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Investment Date</p>
                      <p className="text-xl font-bold text-gray-900">{formatDate(equityData.investmentDetails.investmentDate)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Valuation Growth */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Valuation Growth</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Projected Valuation</h3>
                    <div style={{ height: '250px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {equityData.companyValuation.projectedValuations.map((item, index) => {
                          const maxValue = Math.max(...equityData.companyValuation.projectedValuations.map(i => i.value));
                          const height = (item.value / maxValue) * 220;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className={`w-16 rounded-t-md ${index > 0 ? 'border border-dashed border-green-500' : ''}`}
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#25D366' : 'rgba(37, 211, 102, 0.2)'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Your Equity Value Growth</h3>
                    <div className="space-y-6">
                      {equityData.companyValuation.projectedValuations.map((item, index) => {
                        const equityValue = (item.value * equityData.investmentDetails.equityPercentage) / 100;

                        return (
                          <div key={index}>
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-sm text-gray-500">{item.year}</span>
                              <span className="text-lg font-bold text-gray-900">{formatCurrency(equityValue)}</span>
                            </div>
                            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-green-500 rounded-full"
                                style={{
                                  width: `${(equityValue / ((equityData.companyValuation.projectedValuations[equityData.companyValuation.projectedValuations.length - 1].value * equityData.investmentDetails.equityPercentage) / 100)) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Equity Rights and Documents */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <div className="section-header">
                  <h2 className="section-title">Equity Rights</h2>
                </div>
                <div className="card" style={{ padding: '24px' }}>
                  <div className="space-y-4">
                    {Object.entries(equityData.equityRights).map(([key, value]) => (
                      <div key={key}>
                        <p className="text-sm font-medium text-gray-700 mb-1">
                          {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </p>
                        <p className="text-sm text-gray-600">{value}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <div className="section-header">
                  <h2 className="section-title">Equity Documents</h2>
                </div>
                <div className="card" style={{ padding: '24px' }}>
                  <div className="space-y-4">
                    {equityData.equityDocuments.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                            <p className="text-xs text-gray-500">{formatDate(doc.date)}</p>
                          </div>
                        </div>
                        <a
                          href={doc.url}
                          className="text-sm text-green-600 hover:text-green-800"
                          download
                        >
                          Download
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Future Funding Rounds */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Future Funding Rounds</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Next Funding Round</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Target Date</p>
                        <p className="text-lg font-bold text-gray-900">{formatDate(equityData.futureRounds.nextRound.targetDate)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Target Valuation</p>
                        <p className="text-lg font-bold text-gray-900">{formatCurrency(equityData.futureRounds.nextRound.targetValuation)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Equity Offered</p>
                        <p className="text-lg font-bold text-gray-900">{formatPercentage(equityData.futureRounds.nextRound.equityOffered)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Funding Target</p>
                        <p className="text-lg font-bold text-gray-900">{formatCurrency(equityData.futureRounds.nextRound.fundingTarget)}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Dilution Impact</h3>
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Current Equity</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(equityData.futureRounds.dilutionImpact.currentEquity)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Post-Dilution Equity</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(equityData.futureRounds.dilutionImpact.postDilution)}</p>
                        </div>
                        <div className="pt-4 border-t border-gray-200">
                          <p className="text-sm text-gray-500 mb-1">Current Value</p>
                          <p className="text-lg font-bold text-gray-900">{formatCurrency(equityData.futureRounds.dilutionImpact.value.current)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Projected Post-Dilution Value</p>
                          <p className="text-lg font-bold text-green-600">{formatCurrency(equityData.futureRounds.dilutionImpact.value.postDilution)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
