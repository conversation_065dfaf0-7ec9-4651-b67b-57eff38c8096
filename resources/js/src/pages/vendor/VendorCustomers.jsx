import { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, FunnelIcon, ArrowDownTrayIcon, EyeIcon, PencilIcon, TrashIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import '../../../src/styles/vendor-dashboard.css';
import { customerAPI } from '../../services/api';

export default function VendorCustomers() {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    activeCount: 0,
    inactiveCount: 0,
    totalOrders: 0,
    totalRevenue: 0
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [customersPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);

  // Status options for filter
  const statusOptions = ['All', 'Active', 'Inactive'];

  // Fetch customers from API
  useEffect(() => {
    fetchCustomers();
    fetchCustomerStats();
  }, [currentPage, statusFilter]);

  // Fetch customers with search term debounce
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm) {
        fetchCustomers();
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  // Fetch customers from API
  const fetchCustomers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query params
      let queryParams = `page=${currentPage}&limit=${customersPerPage}`;

      if (searchTerm) {
        queryParams += `&search=${encodeURIComponent(searchTerm)}`;
      }

      if (statusFilter !== 'All') {
        queryParams += `&status=${statusFilter.toLowerCase()}`;
      }

      const response = await customerAPI.getAllCustomers(queryParams);

      if (!response.data.customers || !Array.isArray(response.data.customers)) {
        setError('Invalid response format from server. Please try again.');
        setLoading(false);
        return;
      }

      // Format customer data
      const formattedCustomers = response.data.customers.map(customer => ({
        ...customer,
        // Format status to match UI (capitalize first letter)
        status: customer.status.charAt(0).toUpperCase() + customer.status.slice(1),
        // Format total spent to INR currency format
        totalSpent: `₹${parseFloat(customer.totalSpent || 0).toLocaleString('en-IN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })}`,
        // Format last order date
        lastOrder: customer.lastOrderDate ? new Date(customer.lastOrderDate).toISOString().split('T')[0] : 'N/A'
      }));

      setCustomers(formattedCustomers);
      setTotalPages(response.data.pagination.totalPages);
      setTotalCustomers(response.data.pagination.total);
      setLoading(false);
    } catch (err) {
      setError('Failed to load customers. Please try again.');
      setLoading(false);
    }
  };

  // Fetch customer statistics
  const fetchCustomerStats = async () => {
    try {
      const response = await customerAPI.getCustomerStats();
      setStats(response.data.stats);
    } catch (err) {
      // Don't set error state for stats to avoid blocking the main UI
    }
  };

  // Change page
  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1); // Reset to first page on filter change
  };

  // Handle delete customer
  const handleDeleteCustomer = async (id) => {
    if (!window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      return;
    }

    try {
      setLoading(true);
      await customerAPI.deleteCustomer(id);

      // Refresh customer list
      fetchCustomers();
      fetchCustomerStats();

      // Show success message (you could add a toast notification here)
      alert('Customer deleted successfully');
    } catch (err) {
      setError('Failed to delete customer. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Customer Management</div>
          <h1 className="welcome-title">
            Manage Your <span style={{ color: '#25D366' }}>Customers</span>
          </h1>
          <p className="welcome-subtitle">
            View and manage your customer database. Track customer activity, purchase history, and maintain customer relationships.
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Search & Filter</h2>
        </div>
        <div className="card" style={{ padding: 'var(--spacing-lg)' }}>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 'var(--spacing-md)', alignItems: 'center' }}>
            <div style={{ position: 'relative', flex: '1', minWidth: '250px' }}>
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={handleSearchChange}
                style={{
                  width: '100%',
                  padding: '10px 12px 10px 40px',
                  borderRadius: 'var(--border-radius-md)',
                  border: '1px solid rgba(0,0,0,0.1)',
                  fontSize: '14px'
                }}
              />
              <MagnifyingGlassIcon style={{ position: 'absolute', left: '12px', top: '50%', transform: 'translateY(-50%)', width: '20px', height: '20px', color: 'var(--gray)' }} />
            </div>
            <div style={{ display: 'flex', gap: 'var(--spacing-md)' }}>
              <select
                value={statusFilter}
                onChange={handleFilterChange}
                style={{
                  padding: '8px 12px',
                  borderRadius: 'var(--border-radius-md)',
                  border: '1px solid rgba(0,0,0,0.1)',
                  fontSize: '14px',
                  backgroundColor: 'white'
                }}
              >
                {statusOptions.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
              <button className="btn btn-primary" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <FunnelIcon style={{ width: '16px', height: '16px' }} />
                Filter
              </button>
              <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Customers ({totalCustomers})</h2>
        </div>
        <div className="card">
          {error && (
            <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
              {error}
            </div>
          )}

          <div className="table-container">
            <table className="products-table">
              <thead>
                <tr>
                  <th>Customer</th>
                  <th>Contact</th>
                  <th>Orders</th>
                  <th>Total Spent</th>
                  <th>Last Order</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '30px' }}>
                      <div className="loading-spinner"></div>
                      <p>Loading customers...</p>
                    </td>
                  </tr>
                ) : customers.length === 0 ? (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '30px' }}>
                      <p>No customers found. Try adjusting your search or filters.</p>
                    </td>
                  </tr>
                ) : (
                  customers.map((customer) => (
                    <tr key={customer.id}>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <div style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '50%',
                            backgroundColor: 'var(--primary-light)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'var(--primary)',
                            fontWeight: '600',
                            fontSize: '16px'
                          }}>
                            {customer.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <span style={{ fontWeight: '500' }}>{customer.name}</span>
                        </div>
                      </td>
                      <td>
                        <div>
                          <p style={{ margin: '0 0 4px 0' }}>{customer.phone}</p>
                          {customer.email && (
                            <p style={{ margin: '0', fontSize: '12px', color: 'var(--gray)' }}>{customer.email}</p>
                          )}
                        </div>
                      </td>
                      <td>{customer.totalOrders}</td>
                      <td style={{ fontWeight: '600', color: 'var(--primary)' }}>{customer.totalSpent}</td>
                      <td>{customer.lastOrder}</td>
                      <td>
                        <span className={`status-badge ${
                          customer.status === 'Active' ? 'status-completed' : 'status-pending'
                        }`}>
                          {customer.status}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <button className="action-button view" title="View customer details">
                            <EyeIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                          <button className="action-button edit" title="Edit customer">
                            <PencilIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                          <button
                            className="action-button delete"
                            title="Delete customer"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            <TrashIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && totalPages > 1 && (
            <div style={{ display: 'flex', justifyContent: 'center', padding: 'var(--spacing-md)' }}>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <button
                  onClick={() => paginate(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1 || loading}
                  className="pagination-button"
                >
                  <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                </button>
                <span style={{ fontSize: '14px' }}>
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages || loading}
                  className="pagination-button"
                >
                  <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Customer Summary Cards */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Customer Summary</h2>
        </div>
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <div style={{ color: '#25D366', fontWeight: '700', fontSize: '20px' }}>
                {stats.activeCount}
              </div>
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Active Customers</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Customers who have placed orders recently
              </p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <div style={{ color: '#075E54', fontWeight: '700', fontSize: '20px' }}>
                {stats.inactiveCount}
              </div>
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Inactive Customers</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Customers who haven't ordered in 30+ days
              </p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <div style={{ color: '#25D366', fontWeight: '700', fontSize: '20px' }}>
                {stats.totalOrders}
              </div>
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Total Orders</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Orders placed by all customers
              </p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <div style={{ color: '#075E54', fontWeight: '700', fontSize: '20px' }}>
                ₹{parseFloat(stats.totalRevenue || 0).toLocaleString('en-IN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })}
              </div>
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Total Revenue</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Revenue from all customer orders
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
