import { useState, useEffect, useRef } from 'react';
import { PaperAirplaneIcon, PaperClipIcon, MicrophoneIcon } from '@heroicons/react/24/solid';

export default function WhatsAppChat({ storeName = "Vendor Store" }) {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [showOptions, setShowOptions] = useState(false);
  const messagesEndRef = useRef(null);

  // Initial bot messages
  useEffect(() => {
    const initialMessages = [
      {
        id: 1,
        text: `Welcome to ${storeName}! 👋`,
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      },
      {
        id: 2,
        text: 'How can I help you today?',
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      },
      {
        id: 3,
        text: '1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us',
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }
    ];

    setMessages(initialMessages);
  }, [storeName]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (inputMessage.trim() === '') return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages([...messages, userMessage]);
    setInputMessage('');

    // Simulate bot response based on user input
    setTimeout(() => {
      let botResponse;

      if (inputMessage === '1' || inputMessage.toLowerCase().includes('product')) {
        botResponse = {
          id: messages.length + 2,
          text: 'Here are our popular products:\n\n🥗 Fresh Vegetables - $5.99\n🍞 Whole Grain Bread - $3.49\n🥛 Organic Milk - $4.29\n\nReply with the product name to learn more.',
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '2' || inputMessage.toLowerCase().includes('order')) {
        botResponse = {
          id: messages.length + 2,
          text: 'To check your order status, please provide your order number.',
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '3' || inputMessage.toLowerCase().includes('support') || inputMessage.toLowerCase().includes('help')) {
        botResponse = {
          id: messages.length + 2,
          text: 'Our support team is available from 9 AM to 6 PM. You can also email <NAME_EMAIL>.',
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else if (inputMessage === '4' || inputMessage.toLowerCase().includes('about')) {
        botResponse = {
          id: messages.length + 2,
          text: `${storeName} is a local business committed to providing high-quality products to our community. We've been serving customers since 2010.`,
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      } else {
        botResponse = {
          id: messages.length + 2,
          text: "I'm not sure I understand. Please select one of the options:\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us",
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
      }

      setMessages(prevMessages => [...prevMessages, botResponse]);
    }, 1000);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      backgroundColor: '#f3f4f6',
      borderRadius: '0.5rem',
      overflow: 'hidden',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
    }}>
      {/* Chat header */}
      <div className="whatsapp-header" style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ flex: 1 }}>
          <h2 style={{ fontSize: '1.125rem', fontWeight: 500 }}>{storeName}</h2>
          <p style={{ fontSize: '0.75rem' }}>Online</p>
        </div>
      </div>

      {/* Chat messages */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '1rem',
        backgroundColor: '#e5ddd5',
        backgroundOpacity: 0.3,
        backgroundImage: 'url("https://web.whatsapp.com/img/bg-chat-tile-light_04fcacde539c58cca6745483d4858c52.png")'
      }}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              display: 'flex',
              justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
              marginBottom: '1rem'
            }}
          >
            <div className={`chat-bubble ${message.sender === 'user' ? 'chat-bubble-sent' : 'chat-bubble-received'}`}>
              <p style={{ whiteSpace: 'pre-line' }}>{message.text}</p>
              <p style={{ fontSize: '0.75rem', color: '#6b7280', textAlign: 'right', marginTop: '0.25rem' }}>{message.timestamp}</p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Chat input */}
      <div style={{ backgroundColor: '#e5e7eb', padding: '0.75rem' }}>
        <div className="whatsapp-input">
          <button
            style={{
              padding: '0.5rem',
              borderRadius: '9999px',
              color: '#6b7280'
            }}
            onClick={() => setShowOptions(!showOptions)}
          >
            <PaperClipIcon style={{ height: '1.25rem', width: '1.25rem' }} />
          </button>

          <input
            type="text"
            placeholder="Type a message"
            style={{
              flex: 1,
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              paddingLeft: '0.75rem',
              paddingRight: '0.75rem'
            }}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
          />

          {inputMessage ? (
            <button
              style={{
                padding: '0.5rem',
                borderRadius: '9999px',
                color: '#075E54'
              }}
              onClick={handleSendMessage}
            >
              <PaperAirplaneIcon style={{ height: '1.25rem', width: '1.25rem', transform: 'rotate(90deg)' }} />
            </button>
          ) : (
            <button style={{
              padding: '0.5rem',
              borderRadius: '9999px',
              color: '#6b7280'
            }}>
              <MicrophoneIcon style={{ height: '1.25rem', width: '1.25rem' }} />
            </button>
          )}
        </div>

        {/* Quick options */}
        {showOptions && (
          <div style={{
            marginTop: '0.5rem',
            display: 'grid',
            gridTemplateColumns: 'repeat(2, minmax(0, 1fr))',
            gap: '0.5rem'
          }}>
            <button
              style={{
                backgroundColor: 'white',
                padding: '0.5rem',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                color: '#4b5563'
              }}
              onClick={() => {
                setInputMessage('1');
                setShowOptions(false);
                handleSendMessage();
              }}
            >
              View Products
            </button>
            <button
              style={{
                backgroundColor: 'white',
                padding: '0.5rem',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                color: '#4b5563'
              }}
              onClick={() => {
                setInputMessage('2');
                setShowOptions(false);
                handleSendMessage();
              }}
            >
              Check Order Status
            </button>
            <button
              style={{
                backgroundColor: 'white',
                padding: '0.5rem',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                color: '#4b5563'
              }}
              onClick={() => {
                setInputMessage('3');
                setShowOptions(false);
                handleSendMessage();
              }}
            >
              Contact Support
            </button>
            <button
              style={{
                backgroundColor: 'white',
                padding: '0.5rem',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                color: '#4b5563'
              }}
              onClick={() => {
                setInputMessage('4');
                setShowOptions(false);
                handleSendMessage();
              }}
            >
              About Us
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
