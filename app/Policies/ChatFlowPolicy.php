<?php

namespace App\Policies;

use App\Models\ChatFlow;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ChatFlowPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ChatFlow $chatFlow): bool
    {
        return $user->store->id === $chatFlow->store_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->user_type === 'vendor';
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ChatFlow $chatFlow): bool
    {
        return $user->store->id === $chatFlow->store_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ChatFlow $chatFlow): bool
    {
        return $user->store->id === $chatFlow->store_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ChatFlow $chatFlow): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ChatFlow $chatFlow): bool
    {
        return false;
    }
}
