import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  ChartBarIcon, 
  ArrowRightIcon,
  ChatBubbleLeftRightIcon,
  CurrencyRupeeIcon,
  UsersIcon,
  HandThumbUpIcon,
  ShareIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import FacebookAdsManager from '../../components/marketing/FacebookAdsManager';
import WhatsAppAdsManager from '../../components/marketing/WhatsAppAdsManager';
import InstagramAdsManager from '../../components/marketing/InstagramAdsManager';

export default function Marketing() {
  // Mock data for stats
  const platformStats = [
    {
      name: 'Total Impressions',
      stat: '2,250',
      icon: EyeIcon,
      color: 'rgba(59, 130, 246, 0.1)',
      iconColor: '#3B82F6',
      trend: '12%',
      trendType: 'up'
    },
    {
      name: 'Engagement Rate',
      stat: '8.5%',
      icon: HandThumbUpIcon,
      color: 'rgba(16, 185, 129, 0.1)',
      iconColor: '#10B981',
      trend: '3.2%',
      trendType: 'up'
    },
    {
      name: 'Total Clicks',
      stat: '1,150',
      icon: ShareIcon,
      color: 'rgba(139, 92, 246, 0.1)',
      iconColor: '#8B5CF6',
      trend: '5.7%',
      trendType: 'up'
    },
    {
      name: 'Conversion Rate',
      stat: '3.2%',
      icon: UsersIcon,
      color: 'rgba(236, 72, 153, 0.1)',
      iconColor: '#EC4899',
      trend: '1.1%',
      trendType: 'down'
    }
  ];

  const campaignStats = [
    { platform: 'Facebook', impressions: '1,000', clicks: '100', ctr: '10%', cost: '₹5,000' },
    { platform: 'WhatsApp', impressions: '750', clicks: '150', ctr: '20%', cost: '₹3,500' },
    { platform: 'Instagram', impressions: '500', clicks: '75', ctr: '15%', cost: '₹2,500' }
  ];

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Marketing Dashboard</div>
          <h1 className="welcome-title">
            Manage <span style={{ color: '#25D366' }}>Marketing Campaigns</span>
          </h1>
          <p className="welcome-subtitle">
            Monitor and manage all your marketing campaigns across different platforms from one place.
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Campaign Overview</h2>
        </div>
        <div className="stats-grid">
          {platformStats.map((stat, index) => (
            <div key={index} className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: stat.color }}>
                <stat.icon style={{ color: stat.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
              </div>
              <div className="stat-content">
                <h3 className="stat-label">{stat.name}</h3>
                <p className="stat-value">{stat.stat}</p>
                <p className="stat-description">
                  <span className={stat.trendType === 'up' ? 'text-green-500' : 'text-red-500'}>
                    {stat.trendType === 'up' ? '↑' : '↓'} {stat.trend}
                  </span>{' '}
                  vs last week
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Platform Performance */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Platform Performance</h2>
          <Link to="/admin/marketing/analytics" className="btn btn-outline">
            View detailed analytics
            <ArrowRightIcon style={{ width: '16px', height: '16px', marginLeft: '8px' }} />
          </Link>
        </div>
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {campaignStats.map((stat, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {stat.platform}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.impressions}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.clicks}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.ctr}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.cost}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link to={`/admin/marketing/${stat.platform.toLowerCase()}`} className="text-indigo-600 hover:text-indigo-900">
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Actions</h2>
        </div>
        <div className="stats-grid">
          <Link to="/admin/marketing/facebook" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(59, 89, 152, 0.1)' }}>
              <ChatBubbleLeftRightIcon style={{ color: '#3B5998', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Facebook Ads</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Create and manage Facebook ad campaigns
              </p>
            </div>
          </Link>

          <Link to="/admin/marketing/whatsapp" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <ChatBubbleLeftRightIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">WhatsApp Ads</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Set up and monitor WhatsApp campaigns
              </p>
            </div>
          </Link>

          <Link to="/admin/marketing/instagram" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(193, 53, 132, 0.1)' }}>
              <ChatBubbleLeftRightIcon style={{ color: '#C13584', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Instagram Ads</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Manage Instagram marketing campaigns
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}