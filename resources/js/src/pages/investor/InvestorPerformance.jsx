import { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  ChartBarIcon,
  CurrencyRupeeIcon,
  PresentationChartLineIcon,
  ArrowPathIcon,
  CalendarIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorPerformance() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('year');
  const [performanceData, setPerformanceData] = useState({
    investmentPerformance: {
      initialInvestment: 0,
      currentValue: 0,
      totalReturn: 0,
      annualizedReturn: 0,
      valueHistory: []
    },
    companyValuation: {
      initialValuation: 0,
      currentValuation: 0,
      valuationGrowth: 0,
      projectedValuation: {
        oneYear: 0,
        threeYear: 0,
        fiveYear: 0
      },
      valuationHistory: []
    },
    revenuePerformance: {
      monthlyRevenue: [],
      quarterlyRevenue: [],
      yearlyRevenue: [],
      revenueGrowthRate: 0,
      projectedAnnualRevenue: 0
    },
    marketMetrics: {
      totalAddressableMarket: 0,
      marketPenetration: 0,
      competitorComparison: [],
      marketExpansion: {
        currentCities: 0,
        plannedExpansion: 0,
        targetCities: []
      }
    },
    keyPerformanceIndicators: {
      customerAcquisitionCost: 0,
      lifetimeValue: 0,
      ltvToCAC: 0,
      paybackPeriod: 0,
      monthlyActiveVendors: 0,
      vendorRetentionRate: 0,
      averageRevenuePerVendor: 0
    }
  });

  // Fetch performance data
  useEffect(() => {
    const fetchPerformanceData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          investmentPerformance: {
            initialInvestment: 300000,
            currentValue: 345000,
            totalReturn: 15,
            annualizedReturn: 21.2,
            valueHistory: [
              { date: '2023-01-01', value: 300000 },
              { date: '2023-02-01', value: 306000 },
              { date: '2023-03-01', value: 312000 },
              { date: '2023-04-01', value: 318000 },
              { date: '2023-05-01', value: 324000 },
              { date: '2023-06-01', value: 330000 },
              { date: '2023-07-01', value: 345000 }
            ]
          },
          companyValuation: {
            initialValuation: 6000000,
            currentValuation: 6900000,
            valuationGrowth: 15,
            projectedValuation: {
              oneYear: 10400000,
              threeYear: 15600000,
              fiveYear: 35100000
            },
            valuationHistory: [
              { date: '2023-01-01', value: 6000000 },
              { date: '2023-02-01', value: 6150000 },
              { date: '2023-03-01', value: 6300000 },
              { date: '2023-04-01', value: 6450000 },
              { date: '2023-05-01', value: 6600000 },
              { date: '2023-06-01', value: 6750000 },
              { date: '2023-07-01', value: 6900000 }
            ]
          },
          revenuePerformance: {
            monthlyRevenue: [
              { month: 'Jan', value: 85000 },
              { month: 'Feb', value: 92000 },
              { month: 'Mar', value: 98000 },
              { month: 'Apr', value: 105000 },
              { month: 'May', value: 112000 },
              { month: 'Jun', value: 118000 },
              { month: 'Jul', value: 125000 }
            ],
            quarterlyRevenue: [
              { quarter: 'Q1 2023', value: 275000 },
              { quarter: 'Q2 2023', value: 335000 },
              { quarter: 'Q3 2023 (Projected)', value: 390000 },
              { quarter: 'Q4 2023 (Projected)', value: 450000 }
            ],
            yearlyRevenue: [
              { year: '2023', value: 1450000, projected: true },
              { year: '2024', value: 2175000, projected: true },
              { year: '2025', value: 3262500, projected: true },
              { year: '2026', value: 4893750, projected: true },
              { year: '2027', value: 7340625, projected: true }
            ],
            revenueGrowthRate: 8.5,
            projectedAnnualRevenue: 1450000
          },
          marketMetrics: {
            totalAddressableMarket: 500000000,
            marketPenetration: 1.38,
            competitorComparison: [
              { name: 'WhaMart', marketShare: 1.38, growthRate: 15 },
              { name: 'Competitor A', marketShare: 5.2, growthRate: 8 },
              { name: 'Competitor B', marketShare: 3.7, growthRate: 6 },
              { name: 'Competitor C', marketShare: 2.1, growthRate: 10 }
            ],
            marketExpansion: {
              currentCities: 12,
              plannedExpansion: 8,
              targetCities: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad']
            }
          },
          keyPerformanceIndicators: {
            customerAcquisitionCost: 1200,
            lifetimeValue: 12000,
            ltvToCAC: 10,
            paybackPeriod: 4,
            monthlyActiveVendors: 115,
            vendorRetentionRate: 92,
            averageRevenuePerVendor: 976
          }
        };

        setPerformanceData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching performance data:', error);
        setLoading(false);
      }
    };

    fetchPerformanceData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Get performance metrics
  const getPerformanceMetrics = () => {
    if (!performanceData) return [];

    return [
      {
        name: 'Total Return',
        value: formatPercentage(performanceData.investmentPerformance.totalReturn),
        change: performanceData.investmentPerformance.totalReturn,
        trend: performanceData.investmentPerformance.totalReturn >= 0 ? 'up' : 'down',
        icon: ChartBarIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Annualized Return',
        value: formatPercentage(performanceData.investmentPerformance.annualizedReturn),
        change: performanceData.investmentPerformance.annualizedReturn,
        trend: performanceData.investmentPerformance.annualizedReturn >= 0 ? 'up' : 'down',
        icon: PresentationChartLineIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Valuation Growth',
        value: formatPercentage(performanceData.companyValuation.valuationGrowth),
        change: performanceData.companyValuation.valuationGrowth,
        trend: performanceData.companyValuation.valuationGrowth >= 0 ? 'up' : 'down',
        icon: ChartBarIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Revenue Growth',
        value: formatPercentage(performanceData.revenuePerformance.revenueGrowthRate),
        change: performanceData.revenuePerformance.revenueGrowthRate,
        trend: performanceData.revenuePerformance.revenueGrowthRate >= 0 ? 'up' : 'down',
        icon: CurrencyRupeeIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Performance Analytics</div>
          <h1 className="welcome-title">
            Investment <span style={{ color: '#25D366' }}>Performance</span>
          </h1>
          <p className="welcome-subtitle">
            Detailed analysis of your investment performance, company valuation, and key metrics.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Time Range and Export Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                timeRange === 'month'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('month')}
            >
              Month
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium ${
                timeRange === 'quarter'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('quarter')}
            >
              Quarter
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                timeRange === 'year'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('year')}
            >
              Year
            </button>
          </div>

          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Report
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading performance data...</p>
          </div>
        ) : (
          <>
            {/* Performance Metrics */}
            <div className="stats-grid">
              {performanceData && getPerformanceMetrics().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-sm ${item.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                        {item.change > 0 ? '+' : ''}{item.change}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Investment Value Growth */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Investment Value Growth</h2>
              </div>
              {performanceData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="md:col-span-2">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Value Over Time</h3>
                      <div style={{ height: '250px', position: 'relative' }}>
                        {/* This would be a real chart in production */}
                        <div className="bg-gray-100 h-full w-full rounded-lg flex items-center justify-center">
                          <p className="text-gray-500">Investment Value Chart</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Investment Summary</h3>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Initial Investment</p>
                          <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.investmentPerformance.initialInvestment)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Current Value</p>
                          <p className="text-xl font-bold text-green-600">{formatCurrency(performanceData.investmentPerformance.currentValue)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Value Increase</p>
                          <p className="text-xl font-bold text-gray-900">
                            {formatCurrency(performanceData.investmentPerformance.currentValue - performanceData.investmentPerformance.initialInvestment)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Investment Date</p>
                          <p className="text-xl font-bold text-gray-900">Jan 1, 2023</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Company Valuation */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Company Valuation</h2>
              </div>
              {performanceData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Current Valuation</h3>
                      <div className="bg-gray-50 p-6 rounded-lg">
                        <p className="text-sm text-gray-500 mb-1">Company Value</p>
                        <p className="text-3xl font-bold text-gray-900 mb-4">{formatCurrency(performanceData.companyValuation.currentValuation)}</p>

                        <div className="flex items-center text-sm">
                          <span className="text-gray-500 mr-2">Initial:</span>
                          <span className="font-medium text-gray-900 mr-4">{formatCurrency(performanceData.companyValuation.initialValuation)}</span>

                          <span className="text-gray-500 mr-2">Growth:</span>
                          <span className="font-medium text-green-600 flex items-center">
                            <ArrowUpIcon className="h-4 w-4 mr-1" />
                            {formatPercentage(performanceData.companyValuation.valuationGrowth)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Projected Valuation</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500 mb-1">1 Year Projection</p>
                            <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.companyValuation.projectedValuation.oneYear)}</p>
                          </div>
                          <div className="text-sm text-green-600 flex items-center">
                            <ArrowUpIcon className="h-4 w-4 mr-1" />
                            {formatPercentage(Math.round((performanceData.companyValuation.projectedValuation.oneYear / performanceData.companyValuation.currentValuation - 1) * 100))}
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500 mb-1">3 Year Projection</p>
                            <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.companyValuation.projectedValuation.threeYear)}</p>
                          </div>
                          <div className="text-sm text-green-600 flex items-center">
                            <ArrowUpIcon className="h-4 w-4 mr-1" />
                            {formatPercentage(Math.round((performanceData.companyValuation.projectedValuation.threeYear / performanceData.companyValuation.currentValuation - 1) * 100))}
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500 mb-1">5 Year Projection</p>
                            <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.companyValuation.projectedValuation.fiveYear)}</p>
                          </div>
                          <div className="text-sm text-green-600 flex items-center">
                            <ArrowUpIcon className="h-4 w-4 mr-1" />
                            {formatPercentage(Math.round((performanceData.companyValuation.projectedValuation.fiveYear / performanceData.companyValuation.currentValuation - 1) * 100))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Key Performance Indicators */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Key Performance Indicators</h2>
              </div>
              {performanceData && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Economics</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Customer Acquisition Cost</p>
                        <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.keyPerformanceIndicators.customerAcquisitionCost)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Lifetime Value</p>
                        <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.keyPerformanceIndicators.lifetimeValue)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">LTV:CAC Ratio</p>
                        <p className="text-xl font-bold text-green-600">{performanceData.keyPerformanceIndicators.ltvToCAC}:1</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Payback Period</p>
                        <p className="text-xl font-bold text-gray-900">{performanceData.keyPerformanceIndicators.paybackPeriod} months</p>
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Vendor Metrics</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Monthly Active Vendors</p>
                        <p className="text-xl font-bold text-gray-900">{performanceData.keyPerformanceIndicators.monthlyActiveVendors}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Vendor Retention Rate</p>
                        <p className="text-xl font-bold text-green-600">{formatPercentage(performanceData.keyPerformanceIndicators.vendorRetentionRate)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Avg. Revenue Per Vendor</p>
                        <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.keyPerformanceIndicators.averageRevenuePerVendor)}</p>
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Market Position</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Total Addressable Market</p>
                        <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.marketMetrics.totalAddressableMarket)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Market Penetration</p>
                        <p className="text-xl font-bold text-gray-900">{formatPercentage(performanceData.marketMetrics.marketPenetration)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Cities Covered</p>
                        <p className="text-xl font-bold text-gray-900">{performanceData.marketMetrics.marketExpansion.currentCities}</p>
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Metrics</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Monthly Revenue</p>
                        <p className="text-xl font-bold text-gray-900">
                          {formatCurrency(performanceData.revenuePerformance.monthlyRevenue[performanceData.revenuePerformance.monthlyRevenue.length - 1].value)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Projected Annual Revenue</p>
                        <p className="text-xl font-bold text-gray-900">{formatCurrency(performanceData.revenuePerformance.projectedAnnualRevenue)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Revenue Growth Rate</p>
                        <p className="text-xl font-bold text-green-600">{formatPercentage(performanceData.revenuePerformance.revenueGrowthRate)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
