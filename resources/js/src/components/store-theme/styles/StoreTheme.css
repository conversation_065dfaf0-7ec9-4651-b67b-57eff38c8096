/* WhatsApp Store Theme Styles */

/* Main container */
.store-theme {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #E5DDD5;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

/* Loading state */
.store-theme-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: white;
  position: relative;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(37, 211, 102, 0.2);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin 1s linear infinite;
  margin: 20px 0;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #075E54;
  text-align: center;
}

.preloader-tagline {
  font-size: 18px;
  font-weight: 500;
  color: #075E54;
  text-align: center;
  font-family: 'Poppins', sans-serif;
  margin-top: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Header styles */
.chat-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  color: black;
  height: 60px;
  z-index: 10;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.back-button,
.menu-button {
  background: none;
  border: none;
  color: black;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 32px;
  height: 32px;
}

.store-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 4px;
  height: 100%;
}

.store-logo-container {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  flex-shrink: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.store-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-logo-container {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  flex-shrink: 0;
  margin-right: 8px;
}

.store-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-name-container {
  display: flex;
  align-items: center;
  margin-left: 0;
}

.store-name {
  font-weight: 600;
  font-size: 16px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  color: #000000;
}

.verified-badge {
  width: 16px;
  height: 16px;
  margin-left: 6px;
  flex-shrink: 0;
  display: inline-block;
  vertical-align: middle;
}

/* Chat body styles */
.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: transparent;
  position: relative;
  z-index: 2;
}

.official-service-banner {
  background-color: #D1F4FF;
  color: #006781;
  padding: 8px 12px;
  border-radius: 8px;
  margin: 8px auto;
  text-align: center;
  font-size: 13px;
  max-width: 90%;
}

.date-separator {
  text-align: center;
  margin: 16px auto;
  position: relative;
  display: flex;
  justify-content: center;
  z-index: 2;
}

.date-separator span {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.8);
  color: #54656f;
  font-size: 12.5px;
  font-weight: 500;
  padding: 4px 10px;
  border-radius: 8px;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  white-space: nowrap;
  width: fit-content;
}

/* Message styles */
.chat-message {
  display: flex;
  margin-bottom: 8px;
  position: relative;
  padding: 0 8px;
}

.incoming {
  justify-content: flex-start;
}

.outgoing {
  justify-content: flex-end;
}

.message-bubble {
  width: 75%;
  border-radius: 7.5px;
  position: relative;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
}

.incoming .message-bubble {
  background-color: white;
  border-top-left-radius: 0;
  position: relative;
}

.incoming .message-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: -12px;
  width: 20px;
  height: 20px;
  background-color: white;
  clip-path: polygon(100% 0, 0 0, 100% 100%);
  z-index: 0;
}

.outgoing .message-bubble {
  background-color: #DCF8C6;
  border-top-right-radius: 0;
  position: relative;
}

.outgoing .message-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  right: -12px;
  width: 20px;
  height: 20px;
  background-color: #DCF8C6;
  clip-path: polygon(0 0, 100% 0, 0 100%);
  z-index: 0;
}

.message-header {
  padding: 8px 12px 4px;
  font-weight: 600;
  font-size: 14px;
  color: #128C7E;
  border-bottom: none;
}

.message-image {
  width: calc(100% - 12px);
  padding: 0 6px;
  margin: 0 auto;
  overflow: hidden;
}

.message-img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  border-radius: 4px;
}

.message-content {
  padding: 8px 12px;
  word-break: break-word;
  white-space: pre-wrap;
  overflow: visible;
}

.message-footer {
  padding: 4px 12px 8px;
  font-size: 12px;
  color: #8c8c8c;
  font-style: italic;
}

/* Reply message styles */
.message-reply-container {
  padding: 8px 12px 0;
}

.message-reply {
  padding: 6px 8px;
  border-radius: 4px;
  position: relative;
  border-left: 4px solid #128C7E;
  background-color: rgba(0, 0, 0, 0.05);
  margin-bottom: 4px;
}

.reply-sender {
  font-size: 12px;
  font-weight: 600;
  color: #128C7E;
  margin-bottom: 2px;
}

.reply-content {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Product Catalog styles */
.product-catalog {
  width: 100%;
  padding: 8px 0;
}

.catalog-categories {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  margin-bottom: 8px;
  padding: 0 8px;
}

.catalog-categories::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.category-tab {
  padding: 6px 12px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 16px;
  margin-right: 8px;
  font-size: 12px;
  white-space: nowrap;
  cursor: pointer;
}

.category-tab.active {
  background-color: #128C7E;
  color: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 8px;
  padding: 0 8px;
}

.product-card {
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 6px;
}

.product-name {
  font-size: 12px;
  font-weight: 500;
  margin: 0 0 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 12px;
  font-weight: 600;
  color: #128C7E;
  margin: 0;
}

.catalog-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  margin-top: 8px;
}

.pagination-arrow-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #128C7E;
  color: white;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s;
}

.pagination-arrow-button:hover {
  background-color: #0e7163;
}

.pagination-arrow-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f0f0f0;
  color: #999;
}

/* Message template with buttons styles */
.message-divider {
  height: 0.5px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 0;
  width: 100%;
}

.message-button {
  padding: 8px 12px;
  cursor: pointer;
  text-align: center;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #128C7E;
  font-size: 14px;
}

.message-bubble.with-buttons {
  overflow: visible !important;
}

/* Ensure the message tail appears correctly for template messages */
.incoming .message-bubble.with-buttons::before {
  content: '';
  position: absolute;
  top: 0;
  left: -12px;
  width: 20px;
  height: 20px;
  background-color: white;
  clip-path: polygon(100% 0, 0 0, 100% 100%);
  z-index: 0;
}

.outgoing .message-bubble.with-buttons::before {
  content: '';
  position: absolute;
  top: 0;
  right: -12px;
  width: 20px;
  height: 20px;
  background-color: #DCF8C6;
  clip-path: polygon(0 0, 100% 0, 0 100%);
  z-index: 0;
}

.message-metadata {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 8px 4px 0;
  font-size: 11px;
  color: #8c8c8c;
  margin-top: 2px;
}

.message-time {
  margin-right: 4px;
}

.message-status {
  display: flex;
  align-items: center;
}

.status-icon {
  color: #8c8c8c;
}

.double-check {
  display: flex;
  align-items: center;
  position: relative;
  margin-left: 2px;
}

.first-check {
  position: absolute;
  right: 4px;
}

.second-check {
  margin-left: -2px;
}

.double-check.read .status-icon {
  color: #34B7F1; /* WhatsApp blue color */
}

/* Input bar styles */
.chat-input-bar {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: transparent !important;
  position: relative;
  z-index: 2;
}

.attachment-button,
.mic-button {
  color: #919191;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-input {
  flex: 1;
  border: none;
  border-radius: 20px;
  padding: 9px 12px;
  margin: 0 8px;
  background-color: white;
  outline: none;
  font-size: 15px;
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #25D366;
  color: white;
  border: none;
  cursor: pointer;
}

/* Store theme error */
.store-theme-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  padding: 20px;
  text-align: center;
  color: #DC3545;
  background-color: #E5DDD5;
  position: relative;
  overflow: hidden;
}

.store-theme-error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/chat-background.jpg');
  background-repeat: repeat;
  background-size: 300px auto;
  opacity: 0.3;
  z-index: 0;
}

.store-theme-error::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(230, 222, 178, 0.3);
  z-index: 1;
}

.error-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 90%;
}

/* Full-page design */
@media (max-width: 767px) {
  .message-bubble {
    width: 85%;
  }
}
