/* Vendor Dashboard Redesign - Modern WhatsApp Theme */
:root {
  --primary: #25D366;    /* Bright WhatsApp green - 60% */
  --primary-light: rgba(37, 211, 102, 0.1);
  --primary-light-highlight: rgba(37, 211, 102, 0.25); /* Slightly stronger for highlights */
  --secondary: #E9F7EF;  /* Light green background - 30% */
  --accent: #075E54;     /* Dark green accent - 10% */
  --accent-light: rgba(7, 94, 84, 0.1);
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --gray: #6C757D;
  --dark: #212529;
  --error: #DC3545;
  --success: #28a745;
  --warning: #ffc107;
  --info: #17a2b8;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
  --shadow-lg: 0 8px 24px rgba(0,0,0,0.1);
  --shadow-primary: 0 4px 14px rgba(37, 211, 102, 0.2);
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

body {
  font-family: 'Poppins', 'Segoe UI', sans-serif;
  background-color: var(--secondary);
  color: var(--dark);
}

/* Dashboard Container */
.vendor-dashboard {
  padding: var(--spacing-lg);
  max-width: 100%;
  overflow-x: hidden;
}

/* Welcome Section */
.welcome-section {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary);
}

.welcome-decoration {
  position: absolute;
  border-radius: 50%;
  background-color: var(--primary-light);
  z-index: 0;
}

.welcome-decoration-1 {
  width: 200px;
  height: 200px;
  right: -50px;
  top: -50px;
}

.welcome-decoration-2 {
  width: 120px;
  height: 120px;
  right: 100px;
  bottom: -40px;
  opacity: 0.5;
}

.welcome-content {
  position: relative;
  z-index: 1;
}

.welcome-badge {
  display: inline-block;
  padding: 6px 12px;
  background-color: var(--primary-light);
  color: var(--primary);
  border-radius: var(--border-radius-xl);
  font-size: 13px;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.welcome-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--dark);
}

.welcome-subtitle {
  font-size: 15px;
  color: var(--gray);
  margin-bottom: 0;
  max-width: 600px;
}

/* Stats Section */
.stats-section {
  margin-bottom: var(--spacing-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .section-header-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    width: 100%;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--dark);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  width: 100%;
  overflow-x: auto;
  margin: 0;
  padding: 0;
}

@media (max-width: 1023px) {
  .stats-grid {
    grid-template-columns: repeat(4, minmax(200px, 1fr));
  }
}

@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Horizontal Stat Card */
.stat-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin: 0;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-icon-container {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray);
  margin: 0 0 4px 0;
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  color: var(--dark);
  margin: 0 0 4px 0;
}

.stat-description {
  font-size: 12px;
  color: var(--gray);
  margin: 0;
}

.dashboard-text {
  font-size: 15px;
  color: var(--dark);
  margin: 0;
  line-height: 1.5;
}

/* Orders Section */
.orders-section {
  margin-bottom: var(--spacing-xl);
}

.card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.orders-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-item {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  transition: var(--transition-fast);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item:hover {
  background-color: var(--light-gray);
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 600;
}

.status-completed {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.status-processing {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.status-pending {
  background-color: rgba(23, 162, 184, 0.1);
  color: var(--info);
}

/* Store Preview Section */
.store-preview {
  margin-bottom: var(--spacing-xl);
}

.store-card {
  padding: var(--spacing-xl);
}

.store-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Button Styling */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  text-decoration: none;
  border: none;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  background-color: var(--accent);
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.btn-error {
  background-color: transparent;
  color: var(--error);
  border: 1px solid var(--error);
}

.btn-error:hover {
  background-color: rgba(220, 53, 69, 0.1);
  transform: translateY(-2px);
}

.btn-sm {
  padding: 4px 10px;
  font-size: 12px;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 3px solid rgba(37, 211, 102, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: calc(50% - 8px);
  left: calc(50% - 8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .vendor-dashboard {
    padding: var(--spacing-md);
  }

  .welcome-section {
    padding: var(--spacing-lg);
  }

  .welcome-title {
    font-size: 20px;
  }

  .stat-card {
    padding: var(--spacing-md);
  }
}

/* WhatsApp Message Preview Styles */
.whatsapp-message-preview {
  background-color: #E5DDD5;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  padding: 12px;
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.message-header {
  background-color: #F0F0F0;
  padding: 8px 12px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.message-content {
  background-color: #FFFFFF;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  color: #303030;
  margin: 8px 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-footer {
  font-size: 12px;
  color: #8C8C8C;
  padding: 4px 12px;
  background-color: #F0F0F0;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.message-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.message-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-buttons {
  margin-top: 1px;
}

.message-button {
  background-color: #FFFFFF;
  padding: 10px 12px;
  font-size: 14px;
  color: #128C7E;
  border-radius: 4px;
  margin-top: 1px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.message-button:hover {
  background-color: #F5F5F5;
}

.message-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.05);
  margin: 0;
}

/* Products Page Styles */
.product-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-md);
}

@media (min-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.product-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid rgba(0,0,0,0.03);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.product-image-container {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  opacity: 0;
  transition: var(--transition-fast);
}

.product-card:hover .product-actions {
  opacity: 1;
}

.product-action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
}

.product-action-button.edit {
  background-color: var(--white);
  color: var(--primary);
}

.product-action-button.delete {
  background-color: var(--white);
  color: var(--error);
}

.product-action-button:hover {
  transform: scale(1.1);
}

.product-details {
  padding: var(--spacing-md);
}

.product-category {
  font-size: 12px;
  color: var(--gray);
  margin-bottom: 4px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark);
  margin: 0 0 8px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary);
}

.product-stock {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
}

.product-stock.in-stock {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.product-stock.low-stock {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.product-stock.out-stock {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.products-table th,
.products-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.products-table th {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray);
  background-color: var(--light-gray);
}

.products-table tr:hover {
  background-color: var(--light-gray);
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
}

.action-button.edit {
  background-color: rgba(37, 211, 102, 0.1);
  color: var(--primary);
}

.action-button.view {
  background-color: rgba(7, 94, 84, 0.1);
  color: var(--accent);
}

.action-button.delete {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error);
}

.action-button:hover {
  transform: scale(1.1);
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid rgba(0,0,0,0.05);
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  font-size: 14px;
  color: var(--gray);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: var(--border-radius-sm);
  background-color: var(--white);
  color: var(--dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--light-gray);
  border-color: rgba(0,0,0,0.2);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  background-color: var(--white);
  border: 1px solid rgba(0,0,0,0.1);
  color: var(--dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.pagination-number:hover:not(.active) {
  background-color: var(--light-gray);
}

.pagination-number.active {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

/* Analytics Page Styles */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-lg);
}

@media (min-width: 1024px) {
  .analytics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Analytics Stats Grid */
.analytics-stats-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-md);
  width: 100%;
  overflow-x: hidden;
}

@media (min-width: 640px) {
  .analytics-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .analytics-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.analytics-stat-card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}

.analytics-stat-card .stat-icon-container {
  margin-bottom: var(--spacing-sm);
}

.analytics-stat-card .stat-content {
  width: 100%;
}

.analytics-stat-card .stat-label {
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.analytics-stat-card .stat-value {
  font-size: 20px;
}

/* Fix for analytics tables on mobile */
@media (max-width: 768px) {
  .analytics-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: 100%;
  }

  .analytics-table {
    min-width: 500px; /* Ensure table has minimum width */
  }

  .analytics-filter-select {
    flex: 1;
  }

  .analytics-export-btn {
    flex: 1;
  }

  .export-text {
    display: none;
  }

  .analytics-export-btn:hover .export-text {
    display: inline;
  }
}

@media (min-width: 769px) {
  .export-text {
    display: inline;
  }
}

/* Form Styles */
.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 6px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #e5e7eb;
  border-radius: var(--border-radius-md);
  font-size: 15px;
  transition: var(--transition-fast);
  color: var(--dark);
  background-color: var(--white);
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 6px;
}

.form-error {
  color: var(--error);
  font-size: 13px;
  margin-top: 4px;
}

.form-success {
  color: var(--success);
  font-size: 13px;
  margin-top: 4px;
}

/* File Upload Styles */
.file-upload-container {
  position: relative;
  display: inline-block;
}

.file-upload-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--white);
  border: 1px solid var(--primary);
  color: var(--primary);
  border-radius: var(--border-radius-md);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition-normal);
}

.file-upload-button:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.store-logo-container {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-gray);
  border: 2px solid var(--primary);
  margin-bottom: 16px;
  position: relative;
  box-shadow: var(--shadow-sm);
}

.store-logo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-logo-remove {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--error);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  border: 2px solid white;
  transition: var(--transition-fast);
}

.store-logo-remove:hover {
  transform: scale(1.1);
  background-color: #c82333;
}

/* Store Link Styles */
.store-link-container {
  display: flex;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
  word-break: break-all;
}

.store-link-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.store-link-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--primary);
  font-size: 14px;
  max-width: calc(100% - 28px);
}

/* FAQ Styles */
.faq-container {
  width: 100%;
}

.faq-item {
  border-bottom: 1px solid #e5e7eb;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  width: 100%;
  text-align: left;
  padding: 16px;
  background: none;
  border: none;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-answer {
  padding: 0 16px 16px;
  color: var(--gray);
}

/* Contact Methods Grid */
.contact-methods-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 12px;
  width: 100%;
}

@media (max-width: 900px) {
  .contact-methods-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 600px) {
  .contact-methods-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Utility classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: 16px;
}

.bg-primary-light {
  background-color: var(--primary-light);
}

.bg-primary-light-highlight {
  background-color: var(--primary-light-highlight);
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--primary-light);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success and error messages */
.success-message {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
  padding: 12px 16px;
  border-radius: var(--border-radius-md);
  margin-top: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error);
  padding: 12px 16px;
  border-radius: var(--border-radius-md);
  margin-top: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Button loading state */
.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: var(--white);
  border-radius: 50%;
  animation: btn-loading-spinner 1s ease infinite;
}

@keyframes btn-loading-spinner {
  from {
    transform: rotate(0turn);
  }
  to {
    transform: rotate(1turn);
  }
}

/* Store Link Container */
.store-link-container {
  display: flex;
  align-items: center;
  background-color: var(--light-gray);
  border-radius: var(--border-radius-md);
  padding: 10px 16px;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);
  box-shadow: var(--shadow-sm);
  margin-top: 8px;
}

.store-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.store-link-text {
  font-size: 14px;
  color: var(--primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-decoration: none;
  flex: 1;
}

.store-link-text:hover {
  text-decoration: underline;
}