import { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, MagnifyingGlassIcon, FunnelIcon, ArrowDownTrayIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link, useNavigate } from 'react-router-dom';
import '../../../src/styles/vendor-dashboard.css';
import { productAPI } from '../../services/api';

export default function VendorProducts() {
  const navigate = useNavigate();

  // Simple function to replace the BusinessTypeContext
  const getTermFor = (term) => {
    return term; // Just return the term as is for now
  };
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const productsPerPage = 10;

  // Fetch products from backend
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('page', currentPage);
        params.append('limit', productsPerPage);

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        if (categoryFilter) {
          params.append('category', categoryFilter);
        }

        // Fetch products
        const response = await productAPI.getAllProducts(params.toString());

        // Format products data
        const formattedProducts = response.data.products.map(product => ({
          id: product.id,
          name: product.name,
          price: `₹${parseFloat(product.price).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
          category: product.category || 'Uncategorized',
          stock: product.stock || 0,
          image: product.imageUrl
            ? (product.imageUrl.startsWith('http')
              ? product.imageUrl
              : `https://api.whamart.shop${product.imageUrl}`)
            : 'https://via.placeholder.com/150',
          rawData: product // Keep the raw data for reference
        }));

        setProducts(formattedProducts);
        setTotalCount(response.data.count || formattedProducts.length);

      } catch (err) {
        setError('Failed to load products. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage, searchTerm, categoryFilter]);

  // Handle delete product
  const handleDeleteProduct = async (id) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await productAPI.deleteProduct(id);
        // Refresh products list
        setProducts(products.filter(product => product.id !== id));
        setTotalCount(prevCount => prevCount - 1);
      } catch (err) {
        alert('Failed to delete product. Please try again.');
      }
    }
  };

  // Handle edit product
  const handleEditProduct = (id) => {
    navigate(`/vendor/products/edit/${id}`);
  };

  // Filter products based on search term (client-side filtering as backup)
  const filteredProducts = products;

  // Get current products for pagination
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts;

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">{getTermFor('Products Management')}</div>
          <h1 className="welcome-title">
            Manage Your <span style={{ color: '#25D366' }}>{getTermFor('Products')}</span>
          </h1>
          <p className="welcome-subtitle">
            Add, edit, and manage your {getTermFor('product catalog')}. Keep your inventory updated to provide the best shopping experience for your customers.
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card" style={{ marginBottom: 'var(--spacing-xl)' }}>
        <div style={{ padding: 'var(--spacing-lg)', display: 'flex', flexWrap: 'wrap', gap: 'var(--spacing-md)', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ position: 'relative', width: '40%', minWidth: '250px' }}>
            <input
              type="text"
              placeholder={`Search ${getTermFor('products')}...`}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              style={{
                width: '100%',
                padding: '10px 16px 10px 40px',
                borderRadius: 'var(--border-radius-md)',
                border: '1px solid rgba(0,0,0,0.1)',
                fontSize: '14px'
              }}
            />
            <MagnifyingGlassIcon style={{ position: 'absolute', left: '12px', top: '50%', transform: 'translateY(-50%)', width: '20px', height: '20px', color: 'var(--gray)' }} />
          </div>
          <div style={{ display: 'flex', gap: 'var(--spacing-md)' }}>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FunnelIcon style={{ width: '16px', height: '16px' }} />
              Filters
            </button>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
              Export
            </button>
            <Link to="/vendor/products/add" className="btn btn-primary" style={{ display: 'flex', alignItems: 'center', gap: '8px', textDecoration: 'none' }}>
              <PlusIcon style={{ width: '16px', height: '16px' }} />
              {getTermFor('Add Product')}
            </Link>
          </div>
        </div>
      </div>

      {/* Products Table View */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Products ({totalCount})</h2>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-danger" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(220, 53, 69, 0.1)', color: '#dc3545', border: '1px solid #dc3545' }}>
            {error}
          </div>
        )}

        <div className="card">
          <div className="table-container">
            {loading ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p>{getTermFor('Loading products')}...</p>
              </div>
            ) : products.length === 0 ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p>{getTermFor('No products')} found. Add your first {getTermFor('product')} to get started!</p>
                <Link to="/vendor/products/add" className="btn btn-primary" style={{ marginTop: '16px', display: 'inline-flex', alignItems: 'center', gap: '8px' }}>
                  <PlusIcon style={{ width: '16px', height: '16px' }} />
                  {getTermFor('Add Product')}
                </Link>
              </div>
            ) : (
              <table className="products-table">
                <thead>
                  <tr>
                    <th>{getTermFor('Product')}</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentProducts.map((product) => (
                    <tr key={product.id}>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <img src={product.image} alt={product.name} style={{ width: '40px', height: '40px', borderRadius: '8px', objectFit: 'cover' }} />
                          <span style={{ fontWeight: '500' }}>{product.name}</span>
                        </div>
                      </td>
                      <td>{product.category}</td>
                      <td style={{ fontWeight: '600' }}>{product.price}</td>
                      <td>
                        <span className={`status-badge ${
                          product.stock > 20 ? 'status-completed' :
                          product.stock > 10 ? 'status-processing' :
                          'status-pending'
                        }`}>
                          {product.stock}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <button
                            className="action-button edit"
                            onClick={() => handleEditProduct(product.id)}
                          >
                            <PencilIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                          <button
                            className="action-button delete"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            <TrashIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {/* Pagination */}
          <div className="pagination-container">
            <div className="pagination-info">
              Showing {indexOfFirstProduct + 1}-{Math.min(indexOfLastProduct, filteredProducts.length)} of {filteredProducts.length} products
            </div>
            <div className="pagination-controls">
              <button
                className="pagination-button"
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                Previous
              </button>

              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i + 1}
                  onClick={() => paginate(i + 1)}
                  className={`pagination-number ${currentPage === i + 1 ? 'active' : ''}`}
                >
                  {i + 1}
                </button>
              ))}

              <button
                className="pagination-button"
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Actions</h2>
        </div>
        <div className="stats-grid">
          <Link to="/vendor/products/add" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <PlusIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">{getTermFor('Add New Product')}</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                {getTermFor('Add a new product')} to your store catalog
              </p>
            </div>
          </Link>

          <Link to="/vendor/products/categories" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <FunnelIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Manage Categories</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Create and organize product categories
              </p>
            </div>
          </Link>

          <Link to="/vendor/products/import" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <ArrowDownTrayIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Import {getTermFor('Products')}</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Bulk import {getTermFor('products')} from CSV or Excel
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
