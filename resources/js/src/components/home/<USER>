import { StarIcon } from '@heroicons/react/24/solid';

export default function Testimonials() {
  const testimonials = [
    {
      id: 1,
      content: "<PERSON><PERSON><PERSON> has completely transformed my small business. The WhatsApp-style interface is so familiar to my customers that they immediately feel comfortable shopping with me. My sales have increased by 40% since I started using it!",
      author: {
        name: '<PERSON>',
        role: 'Boutique Owner',
        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      },
      rating: 5,
    },
    {
      id: 2,
      content: "As an influencer, I love how easy it is to collaborate with vendors on Whamart. The platform makes it simple to track my referrals and earnings. It's a win-win for both me and the businesses I work with.",
      author: {
        name: '<PERSON>',
        role: 'Fashion Influencer',
        image: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      },
      rating: 5,
    },
    {
      id: 3,
      content: "The automated chat flows are a game-changer. My customers can browse products, ask questions, and complete purchases all in one familiar chat interface. It's like having a 24/7 sales assistant without the overhead.",
      author: {
        name: 'Elena Rodriguez',
        role: 'Handmade Jewelry Shop',
        image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      },
      rating: 4,
    },
  ];

  return (
    <div id="testimonials" className="py-24 bg-whatsapp-teal">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-xl text-center">
          <h2 className="text-base font-semibold leading-8 text-whatsapp-light">Testimonials</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Loved by businesses and influencers
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="flex flex-col justify-between bg-white rounded-2xl shadow-lg p-8 ring-1 ring-gray-200"
            >
              <div>
                <div className="flex gap-x-1 text-whatsapp-default">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-5 w-5 ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-200'}`}
                      aria-hidden="true"
                    />
                  ))}
                </div>
                <div className="mt-6 text-lg leading-8 text-gray-600">
                  "{testimonial.content}"
                </div>
              </div>
              <div className="mt-8 flex items-center gap-x-4">
                <img
                  src={testimonial.author.image}
                  alt=""
                  className="h-12 w-12 rounded-full bg-gray-50"
                />
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.author.name}</div>
                  <div className="text-gray-600">{testimonial.author.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
