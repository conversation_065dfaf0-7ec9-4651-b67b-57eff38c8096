import React, { memo, useState, useEffect } from 'react';
import { <PERSON>le, Position, useStore } from 'reactflow';
import { PencilIcon, TrashIcon, BriefcaseIcon } from '@heroicons/react/24/outline';
import { serviceAPI } from '../../../services/api';

/**
 * ServiceCatalogNode component for the Chat Flow Builder
 * Represents a service catalog message in the flow
 */
const ServiceCatalogNode = ({ data, selected, isConnectable, id }) => {
  const {
    content,
    header,
    footer,
    buttons,
    isFirst,
    categoryFilter,
    onEdit,
    onDelete
  } = data;

  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);

  // Get the zoom level from the store
  const zoom = useStore((state) => state.transform[2]);

  // Fetch services when the node is created
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const response = await serviceAPI.getAllServices();
        if (response.data && response.data.services) {
          setServices(response.data.services);
        }
      } catch (error) {
//         // Removed console.error
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Handle edit button click
  const handleEdit = () => {
    if (onEdit) {
      onEdit(id);
    }
  };

  // Handle delete button click
  const handleDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
  };

  // Determine if this is a source node (can have outgoing connections)
  const isSource = true;

  // Determine if this is a target node (can have incoming connections)
  const isTarget = !isFirst;

  return (
    <div className={`chat-message-node ${selected ? 'selected' : ''}`}>
      {/* Input handle (only if not the first node) */}
      {isTarget && (
        <Handle
          type="target"
          position={Position.Left}
          isConnectable={isConnectable}
          className="handle-input"
        />
      )}

      {/* Node content */}
      <div className="chat-message-content">
        {/* Header */}
        {header && <div className="chat-message-header">{header}</div>}

        {/* Service catalog info box - replacing the service preview grid */}
        <div className="border border-gray-200 rounded-lg p-3 mb-2 bg-gray-50">
          <div className="flex items-center justify-center mb-2">
            <BriefcaseIcon className="h-5 w-5 text-green-600 mr-2" />
            <span className="text-sm font-medium">Service Catalog</span>
          </div>

          <div className="text-xs text-center text-gray-500 mb-2">
            This message will display your store's service catalog when customers view it.
          </div>

          <div className="text-xs text-center font-medium text-blue-600 mb-2">
            Services will be shown in the actual store chat, not in this builder.
          </div>

          <div className="text-xs text-center font-medium text-red-600 bg-red-50 p-2 rounded">
            IMPORTANT: Add a "Service Details Handler" template from the template library to handle service selection!
          </div>
        </div>

        <div className="text-xs bg-blue-50 text-blue-600 p-1 rounded mb-2">
          Category: {categoryFilter === 'all' ? 'All Services' : categoryFilter}
        </div>

        <div className="text-xs bg-green-50 text-green-600 p-1 rounded mb-2">
          Services found: {loading ? 'Loading...' : services.length}
        </div>

        {footer && (
          <div className="text-xs italic text-gray-500 mb-2 p-1 bg-gray-50 rounded">{footer}</div>
        )}

        {/* Buttons */}
        {buttons && buttons.length > 0 && (
          <div className="chat-message-buttons">
            {buttons.map((button, index) => (
              <div key={index} className="chat-message-button">
                {button.text}
              </div>
            ))}
          </div>
        )}

        {/* Edit and Delete buttons */}
        <div className="node-actions">
          <button onClick={handleEdit} className="node-action-button edit">
            <PencilIcon className="h-4 w-4" />
          </button>
          <button onClick={handleDelete} className="node-action-button delete">
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Output handle */}
      {isSource && (
        <Handle
          type="source"
          position={Position.Right}
          isConnectable={isConnectable}
          className="handle-output"
        />
      )}
    </div>
  );
};

export default memo(ServiceCatalogNode);
