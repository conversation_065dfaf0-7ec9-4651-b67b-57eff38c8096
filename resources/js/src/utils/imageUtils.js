/**
 * Utility functions for handling image URLs
 */

/**
 * Ensures a valid image URL by properly formatting relative URLs
 * @param {string|object} imageUrl - The image URL to process
 * @param {string} fallbackUrl - Fallback URL to use if imageUrl is invalid
 * @returns {string} - A properly formatted image URL
 */
export const ensureValidImageUrl = (imageUrl, fallbackUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==') => {
  // Handle null or undefined
  if (imageUrl === null || imageUrl === undefined) {
    return fallbackUrl;
  }

  // Handle data URLs directly - most important for chat flow images
  if (typeof imageUrl === 'string' && imageUrl.startsWith('data:image/')) {
    return imageUrl;
  }

  // Handle object references (common when images are stored in JSON)
  if (typeof imageUrl === 'object') {
    // If the object is an array, try to use the first element
    if (Array.isArray(imageUrl) && imageUrl.length > 0) {
      return ensureValidImageUrl(imageUrl[0], fallbackUrl);
    }

    // SPECIAL CASE: Handle the specific structure we're seeing in the console
    // This is a targeted fix for the specific issue
    if (imageUrl && typeof imageUrl === 'object') {
      // Try to access all possible properties that might contain the image data
      for (const key of Object.keys(imageUrl)) {
        if (typeof imageUrl[key] === 'string' && imageUrl[key].startsWith('data:image/')) {
          return imageUrl[key];
        }
      }
    }

    // Check if it has a url, src, or path property
    if (imageUrl.url) {
      return ensureValidImageUrl(imageUrl.url, fallbackUrl);
    }

    if (imageUrl.src) {
      return ensureValidImageUrl(imageUrl.src, fallbackUrl);
    }

    if (imageUrl.path) {
      return ensureValidImageUrl(imageUrl.path, fallbackUrl);
    }

    // Check for data property (sometimes used in JSON serialization)
    if (imageUrl.data) {
      return ensureValidImageUrl(imageUrl.data, fallbackUrl);
    }

    // If it's a File or Blob object, create an object URL
    if ((imageUrl instanceof Blob || imageUrl instanceof File) && typeof URL !== 'undefined') {
      try {
        return URL.createObjectURL(imageUrl);
      } catch (error) {
        return fallbackUrl;
      }
    }

    // If it has a toString method, try using that
    if (typeof imageUrl.toString === 'function') {
      const stringValue = imageUrl.toString();
      if (stringValue !== '[object Object]') {
        return ensureValidImageUrl(stringValue, fallbackUrl);
      }
    }

    // Try to JSON stringify and parse the object to handle circular references
    try {
      const jsonString = JSON.stringify(imageUrl);

      if (jsonString && jsonString !== '{}' && jsonString !== '[]') {
        // Look for data:image pattern in the JSON string
        const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
        if (dataUrlMatch && dataUrlMatch[1]) {
          return dataUrlMatch[1];
        }

        // Try to find any property that might be a data URL
        const allDataUrlMatches = jsonString.match(/data:image\/[^"]+/g);
        if (allDataUrlMatches && allDataUrlMatches.length > 0) {
          return allDataUrlMatches[0];
        }
      }
    } catch (error) {
      // Silent error handling in production
    }

    return fallbackUrl;
  }

  // Handle empty string or non-string values
  if (typeof imageUrl !== 'string' || imageUrl.trim() === '') {
    return fallbackUrl;
  }

  // Trim the URL to remove any whitespace
  const trimmedUrl = imageUrl.trim();

  try {
    // Check if it's a data URL (base64)
    if (trimmedUrl.startsWith('data:image/')) {
      return trimmedUrl;
    }

    // If it's already a full URL, use it as is
    if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
      return trimmedUrl;
    }

    // For paths with leading slash
    if (trimmedUrl.startsWith('/')) {
      const apiBaseUrl = 'https://api.whamart.shop';
      const fullUrl = `${apiBaseUrl}${trimmedUrl}`;
      return fullUrl;
    }

    // For paths without leading slash
    const apiBaseUrl = 'https://api.whamart.shop';
    const fullUrl = `${apiBaseUrl}/${trimmedUrl}`;
    return fullUrl;
  } catch (error) {
    return fallbackUrl;
  }
};

export default {
  ensureValidImageUrl
};
