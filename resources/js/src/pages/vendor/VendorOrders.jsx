import { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, FunnelIcon, ArrowDownTrayIcon, EyeIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import '../../../src/styles/vendor-dashboard.css';
import { orderAPI } from '../../../src/services/api';

export default function VendorOrders() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const ordersPerPage = 5;

  // Fetch orders from API
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const response = await orderAPI.getAllOrders();
        setOrders(response.data.orders);
        setError(null);
      } catch (err) {
//         // Removed console.error
        setError('Failed to load orders. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Format date function
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
    } catch (error) {
      return dateString;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  // Capitalize first letter
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Filter orders based on search term and status
  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      (order.customerName && order.customerName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (order.orderNumber && order.orderNumber.toString().includes(searchTerm)) ||
      (order.id && order.id.toString().includes(searchTerm));

    const matchesStatus =
      statusFilter === 'All' ||
      (order.status && order.status.toLowerCase() === statusFilter.toLowerCase());

    return matchesSearch && matchesStatus;
  });

  // Get current orders for pagination
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  // Status options for filter
  const statusOptions = ['All', 'Completed', 'Processing', 'Shipped', 'Pending'];

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Orders Management</div>
          <h1 className="welcome-title">
            Manage Your <span style={{ color: '#25D366' }}>Orders</span>
          </h1>
          <p className="welcome-subtitle">
            View and manage all your customer orders in one place. Track order status, process payments, and handle customer requests.
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card" style={{ marginBottom: 'var(--spacing-xl)' }}>
        <div style={{ padding: 'var(--spacing-lg)', display: 'flex', flexWrap: 'wrap', gap: 'var(--spacing-md)', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ position: 'relative', width: '40%', minWidth: '250px' }}>
            <input
              type="text"
              placeholder="Search orders by ID or customer..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
              style={{
                width: '100%',
                padding: '10px 16px 10px 40px',
                borderRadius: 'var(--border-radius-md)',
                border: '1px solid rgba(0,0,0,0.1)',
                fontSize: '14px'
              }}
            />
            <MagnifyingGlassIcon style={{ position: 'absolute', left: '12px', top: '50%', transform: 'translateY(-50%)', width: '20px', height: '20px', color: 'var(--gray)' }} />
          </div>
          <div style={{ display: 'flex', gap: 'var(--spacing-md)' }}>
            <select
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value);
                setCurrentPage(1); // Reset to first page on filter change
              }}
              style={{
                padding: '8px 12px',
                borderRadius: 'var(--border-radius-md)',
                border: '1px solid rgba(0,0,0,0.1)',
                fontSize: '14px',
                backgroundColor: 'white'
              }}
            >
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FunnelIcon style={{ width: '16px', height: '16px' }} />
              More Filters
            </button>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Orders ({filteredOrders.length})</h2>
        </div>
        <div className="card">
          {loading ? (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
              <p>Loading orders...</p>
            </div>
          ) : error ? (
            <div style={{ padding: '2rem', textAlign: 'center', color: 'red' }}>
              <p>{error}</p>
            </div>
          ) : filteredOrders.length === 0 ? (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
              <p>No orders found. Try adjusting your filters or create new orders.</p>
            </div>
          ) : (
            <div className="table-container">
              <table className="products-table">
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Date</th>
                    <th>Items</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentOrders.map((order) => (
                    <tr key={order.id}>
                      <td style={{ fontWeight: '600' }}>#{order.orderNumber || order.id}</td>
                      <td>{order.customerName}</td>
                      <td>{formatDate(order.createdAt)}</td>
                      <td>{order.items?.length || 0} {order.items?.length === 1 ? 'item' : 'items'}</td>
                      <td style={{ fontWeight: '600', color: 'var(--primary)' }}>{formatCurrency(order.totalAmount)}</td>
                      <td>
                        <span className={`status-badge ${
                          order.status === 'completed' ? 'status-completed' :
                          order.status === 'processing' ? 'status-processing' :
                          order.status === 'shipped' ? 'status-completed' :
                          'status-pending'
                        }`}>
                          {order.status ? capitalizeFirstLetter(order.status) : 'Pending'}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                          <button className="action-button edit">
                            <EyeIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          <div className="pagination-container">
            <div className="pagination-info">
              Showing {indexOfFirstOrder + 1}-{Math.min(indexOfLastOrder, filteredOrders.length)} of {filteredOrders.length} orders
            </div>
            <div className="pagination-controls">
              <button
                className="pagination-button"
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                Previous
              </button>

              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i + 1}
                  onClick={() => paginate(i + 1)}
                  className={`pagination-number ${currentPage === i + 1 ? 'active' : ''}`}
                >
                  {i + 1}
                </button>
              ))}

              <button
                className="pagination-button"
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Order Summary Cards */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Order Summary</h2>
        </div>
        {loading ? (
          <div style={{ padding: '1rem', textAlign: 'center' }}>
            <p>Loading order summary...</p>
          </div>
        ) : (
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                <div style={{ color: '#25D366', fontWeight: '700', fontSize: '20px' }}>
                  {orders.filter(order => order.status === 'completed').length}
                </div>
              </div>
              <div className="stat-content">
                <h3 className="stat-label">Completed Orders</h3>
                <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                  Successfully delivered to customers
                </p>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: 'rgba(255, 193, 7, 0.1)' }}>
                <div style={{ color: '#ffc107', fontWeight: '700', fontSize: '20px' }}>
                  {orders.filter(order => order.status === 'processing').length}
                </div>
              </div>
              <div className="stat-content">
                <h3 className="stat-label">Processing Orders</h3>
                <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                  Currently being processed
                </p>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: 'rgba(13, 110, 253, 0.1)' }}>
                <div style={{ color: '#0d6efd', fontWeight: '700', fontSize: '20px' }}>
                  {orders.filter(order => order.status === 'shipped').length}
                </div>
              </div>
              <div className="stat-content">
                <h3 className="stat-label">Shipped Orders</h3>
                <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                  On the way to customers
                </p>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: 'rgba(108, 117, 125, 0.1)' }}>
                <div style={{ color: '#6c757d', fontWeight: '700', fontSize: '20px' }}>
                  {orders.filter(order => order.status === 'pending').length}
                </div>
              </div>
              <div className="stat-content">
                <h3 className="stat-label">Pending Orders</h3>
                <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                  Awaiting processing
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
