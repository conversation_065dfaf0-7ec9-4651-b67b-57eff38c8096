{"version": 3, "names": ["_t", "require", "_index", "isFor", "isForStatement", "isIfStatement", "isStatement", "WithStatement", "node", "word", "space", "token", "print", "object", "printBlock", "IfStatement", "test", "needsBlock", "alternate", "getLastStatement", "consequent", "newline", "indent", "printAndIndentOnComments", "dedent", "endsWith", "statement", "body", "ForStatement", "exit", "enterForStatementInit", "tokenContext", "TokenContext", "forHead", "init", "update", "WhileStatement", "ForXStatement", "isForOf", "type", "await", "noIndentInnerCommentsHere", "forOfHead", "forInHead", "left", "right", "ForInStatement", "exports", "ForOfStatement", "DoWhileStatement", "semicolon", "printStatementAfterKeyword", "printer", "printTerminatorless", "BreakStatement", "label", "ContinueStatement", "ReturnStatement", "argument", "ThrowStatement", "LabeledStatement", "TryStatement", "block", "handlers", "handler", "finalizer", "CatchClause", "param", "typeAnnotation", "SwitchStatement", "discriminant", "printSequence", "cases", "undefined", "addNewlines", "leading", "cas", "length", "rightBrace", "SwitchCase", "DebuggerStatement", "VariableDeclaration", "parent", "declare", "kind", "hasInits", "declar", "declarations", "printList", "occurrenceCount", "VariableDeclarator", "id", "definite"], "sources": ["../../src/generators/statements.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isFor,\n  isForStatement,\n  isIfStatement,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\nimport { TokenContext } from \"../node/index.ts\";\n\nexport function WithStatement(this: Printer, node: t.WithStatement) {\n  this.word(\"with\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.object);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function IfStatement(this: Printer, node: t.IfStatement) {\n  this.word(\"if\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test);\n  this.token(\")\");\n  this.space();\n\n  const needsBlock =\n    node.alternate && isIfStatement(getLastStatement(node.consequent));\n  if (needsBlock) {\n    this.token(\"{\");\n    this.newline();\n    this.indent();\n  }\n\n  this.printAndIndentOnComments(node.consequent);\n\n  if (needsBlock) {\n    this.dedent();\n    this.newline();\n    this.token(\"}\");\n  }\n\n  if (node.alternate) {\n    if (this.endsWith(charCodes.rightCurlyBrace)) this.space();\n    this.word(\"else\");\n    this.space();\n    this.printAndIndentOnComments(node.alternate);\n  }\n}\n\n// Recursively get the last statement.\nfunction getLastStatement(statement: t.Statement): t.Statement {\n  // @ts-expect-error: If statement.body is empty or not a Node, isStatement will return false\n  const { body } = statement;\n  if (isStatement(body) === false) {\n    return statement;\n  }\n\n  return getLastStatement(body);\n}\n\nexport function ForStatement(this: Printer, node: t.ForStatement) {\n  this.word(\"for\");\n  this.space();\n  this.token(\"(\");\n\n  {\n    const exit = this.enterForStatementInit();\n    this.tokenContext |= TokenContext.forHead;\n    this.print(node.init);\n    exit();\n  }\n\n  this.token(\";\");\n\n  if (node.test) {\n    this.space();\n    this.print(node.test);\n  }\n  this.token(\";\", false, 1);\n\n  if (node.update) {\n    this.space();\n    this.print(node.update);\n  }\n\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function WhileStatement(this: Printer, node: t.WhileStatement) {\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nfunction ForXStatement(this: Printer, node: t.ForXStatement) {\n  this.word(\"for\");\n  this.space();\n  const isForOf = node.type === \"ForOfStatement\";\n  if (isForOf && node.await) {\n    this.word(\"await\");\n    this.space();\n  }\n  this.noIndentInnerCommentsHere();\n  this.token(\"(\");\n  {\n    const exit = isForOf ? null : this.enterForStatementInit();\n    this.tokenContext |= isForOf\n      ? TokenContext.forOfHead\n      : TokenContext.forInHead;\n    this.print(node.left);\n    exit?.();\n  }\n  this.space();\n  this.word(isForOf ? \"of\" : \"in\");\n  this.space();\n  this.print(node.right);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport const ForInStatement = ForXStatement;\nexport const ForOfStatement = ForXStatement;\n\nexport function DoWhileStatement(this: Printer, node: t.DoWhileStatement) {\n  this.word(\"do\");\n  this.space();\n  this.print(node.body);\n  this.space();\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test);\n  this.token(\")\");\n  this.semicolon();\n}\n\nfunction printStatementAfterKeyword(printer: Printer, node: t.Node) {\n  if (node) {\n    printer.space();\n    printer.printTerminatorless(node);\n  }\n\n  printer.semicolon();\n}\n\nexport function BreakStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"break\");\n  printStatementAfterKeyword(this, node.label);\n}\n\nexport function ContinueStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"continue\");\n  printStatementAfterKeyword(this, node.label);\n}\n\nexport function ReturnStatement(this: Printer, node: t.ReturnStatement) {\n  this.word(\"return\");\n  printStatementAfterKeyword(this, node.argument);\n}\n\nexport function ThrowStatement(this: Printer, node: t.ThrowStatement) {\n  this.word(\"throw\");\n  printStatementAfterKeyword(this, node.argument);\n}\n\nexport function LabeledStatement(this: Printer, node: t.LabeledStatement) {\n  this.print(node.label);\n  this.token(\":\");\n  this.space();\n  this.print(node.body);\n}\n\nexport function TryStatement(this: Printer, node: t.TryStatement) {\n  this.word(\"try\");\n  this.space();\n  this.print(node.block);\n  this.space();\n\n  // Esprima bug puts the catch clause in a `handlers` array.\n  // see https://code.google.com/p/esprima/issues/detail?id=433\n  // We run into this from regenerator generated ast.\n  // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n  if (node.handlers) {\n    // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n    this.print(node.handlers[0]);\n  } else {\n    this.print(node.handler);\n  }\n\n  if (node.finalizer) {\n    this.space();\n    this.word(\"finally\");\n    this.space();\n    this.print(node.finalizer);\n  }\n}\n\nexport function CatchClause(this: Printer, node: t.CatchClause) {\n  this.word(\"catch\");\n  this.space();\n  if (node.param) {\n    this.token(\"(\");\n    this.print(node.param);\n    this.print(node.param.typeAnnotation);\n    this.token(\")\");\n    this.space();\n  }\n  this.print(node.body);\n}\n\nexport function SwitchStatement(this: Printer, node: t.SwitchStatement) {\n  this.word(\"switch\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.discriminant);\n  this.token(\")\");\n  this.space();\n  this.token(\"{\");\n\n  this.printSequence(\n    node.cases,\n    true,\n    undefined,\n    function addNewlines(leading, cas) {\n      if (!leading && node.cases[node.cases.length - 1] === cas) return -1;\n    },\n  );\n\n  this.rightBrace(node);\n}\n\nexport function SwitchCase(this: Printer, node: t.SwitchCase) {\n  if (node.test) {\n    this.word(\"case\");\n    this.space();\n    this.print(node.test);\n    this.token(\":\");\n  } else {\n    this.word(\"default\");\n    this.token(\":\");\n  }\n\n  if (node.consequent.length) {\n    this.newline();\n    this.printSequence(node.consequent, true);\n  }\n}\n\nexport function DebuggerStatement(this: Printer) {\n  this.word(\"debugger\");\n  this.semicolon();\n}\n\nexport function VariableDeclaration(\n  this: Printer,\n  node: t.VariableDeclaration,\n  parent: t.Node,\n) {\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  const { kind } = node;\n  if (kind === \"await using\") {\n    this.word(\"await\");\n    this.space();\n    this.word(\"using\", true);\n  } else {\n    this.word(kind, kind === \"using\");\n  }\n  this.space();\n\n  let hasInits = false;\n  // don't add whitespace to loop heads\n  if (!isFor(parent)) {\n    for (const declar of node.declarations) {\n      if (declar.init) {\n        // has an init so let's split it up over multiple lines\n        hasInits = true;\n      }\n    }\n  }\n\n  //\n  // use a pretty separator when we aren't in compact mode, have initializers and don't have retainLines on\n  // this will format declarations like:\n  //\n  //   let foo = \"bar\", bar = \"foo\";\n  //\n  // into\n  //\n  //   let foo = \"bar\",\n  //       bar = \"foo\";\n  //\n\n  this.printList(\n    node.declarations,\n    undefined,\n    undefined,\n    node.declarations.length > 1,\n    hasInits\n      ? function (this: Printer, occurrenceCount: number) {\n          this.token(\",\", false, occurrenceCount);\n          this.newline();\n        }\n      : undefined,\n  );\n\n  if (isFor(parent)) {\n    // don't give semicolons to these nodes since they'll be inserted in the parent generator\n    if (isForStatement(parent)) {\n      if (parent.init === node) return;\n    } else {\n      if (parent.left === node) return;\n    }\n  }\n\n  this.semicolon();\n}\n\nexport function VariableDeclarator(this: Printer, node: t.VariableDeclarator) {\n  this.print(node.id);\n  if (node.definite) this.token(\"!\"); // TS\n  // @ts-ignore(Babel 7 vs Babel 8) Property 'typeAnnotation' does not exist on type 'MemberExpression'.\n  this.print(node.id.typeAnnotation);\n  if (node.init) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.init);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAWA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EAV9CE,KAAK;EACLC,cAAc;EACdC,aAAa;EACbC;AAAW,IAAAN,EAAA;AASN,SAASO,aAAaA,CAAgBC,IAAqB,EAAE;EAClE,IAAI,CAACC,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,MAAM,CAAC;EACvB,IAAI,CAACF,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAASO,WAAWA,CAAgBP,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,CAAC;EACrB,IAAI,CAACL,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EAEZ,MAAMO,UAAU,GACdT,IAAI,CAACU,SAAS,IAAIb,aAAa,CAACc,gBAAgB,CAACX,IAAI,CAACY,UAAU,CAAC,CAAC;EACpE,IAAIH,UAAU,EAAE;IACd,IAAI,CAACN,SAAK,IAAI,CAAC;IACf,IAAI,CAACU,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,MAAM,CAAC,CAAC;EACf;EAEA,IAAI,CAACC,wBAAwB,CAACf,IAAI,CAACY,UAAU,CAAC;EAE9C,IAAIH,UAAU,EAAE;IACd,IAAI,CAACO,MAAM,CAAC,CAAC;IACb,IAAI,CAACH,OAAO,CAAC,CAAC;IACd,IAAI,CAACV,SAAK,IAAI,CAAC;EACjB;EAEA,IAAIH,IAAI,CAACU,SAAS,EAAE;IAClB,IAAI,IAAI,CAACO,QAAQ,IAA0B,CAAC,EAAE,IAAI,CAACf,KAAK,CAAC,CAAC;IAC1D,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACa,wBAAwB,CAACf,IAAI,CAACU,SAAS,CAAC;EAC/C;AACF;AAGA,SAASC,gBAAgBA,CAACO,SAAsB,EAAe;EAE7D,MAAM;IAAEC;EAAK,CAAC,GAAGD,SAAS;EAC1B,IAAIpB,WAAW,CAACqB,IAAI,CAAC,KAAK,KAAK,EAAE;IAC/B,OAAOD,SAAS;EAClB;EAEA,OAAOP,gBAAgB,CAACQ,IAAI,CAAC;AAC/B;AAEO,SAASC,YAAYA,CAAgBpB,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EAEf;IACE,MAAMkB,IAAI,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACzC,IAAI,CAACC,YAAY,IAAIC,mBAAY,CAACC,OAAO;IACzC,IAAI,CAACrB,KAAK,CAACJ,IAAI,CAAC0B,IAAI,CAAC;IACrBL,IAAI,CAAC,CAAC;EACR;EAEA,IAAI,CAAClB,SAAK,GAAI,CAAC;EAEf,IAAIH,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACN,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,CAAC;EACvB;EACA,IAAI,CAACL,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;EAEzB,IAAIH,IAAI,CAAC2B,MAAM,EAAE;IACf,IAAI,CAACzB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAAC2B,MAAM,CAAC;EACzB;EAEA,IAAI,CAACxB,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAAS4B,cAAcA,CAAgB5B,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,CAAC;EACrB,IAAI,CAACL,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEA,SAAS6B,aAAaA,CAAgB7B,IAAqB,EAAE;EAC3D,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,MAAM4B,OAAO,GAAG9B,IAAI,CAAC+B,IAAI,KAAK,gBAAgB;EAC9C,IAAID,OAAO,IAAI9B,IAAI,CAACgC,KAAK,EAAE;IACzB,IAAI,CAAC/B,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAAC+B,yBAAyB,CAAC,CAAC;EAChC,IAAI,CAAC9B,SAAK,GAAI,CAAC;EACf;IACE,MAAMkB,IAAI,GAAGS,OAAO,GAAG,IAAI,GAAG,IAAI,CAACR,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACC,YAAY,IAAIO,OAAO,GACxBN,mBAAY,CAACU,SAAS,GACtBV,mBAAY,CAACW,SAAS;IAC1B,IAAI,CAAC/B,KAAK,CAACJ,IAAI,CAACoC,IAAI,CAAC;IACrBf,IAAI,YAAJA,IAAI,CAAG,CAAC;EACV;EACA,IAAI,CAACnB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC6B,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;EAChC,IAAI,CAAC5B,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACqC,KAAK,CAAC;EACtB,IAAI,CAAClC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,MAAMsC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAGT,aAAa;AACpC,MAAMW,cAAc,GAAAD,OAAA,CAAAC,cAAA,GAAGX,aAAa;AAEpC,SAASY,gBAAgBA,CAAgBzC,IAAwB,EAAE;EACxE,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,CAAC;EACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,CAAC;EACrB,IAAI,CAACL,SAAK,GAAI,CAAC;EACf,IAAI,CAACuC,SAAS,CAAC,CAAC;AAClB;AAEA,SAASC,0BAA0BA,CAACC,OAAgB,EAAE5C,IAAY,EAAE;EAClE,IAAIA,IAAI,EAAE;IACR4C,OAAO,CAAC1C,KAAK,CAAC,CAAC;IACf0C,OAAO,CAACC,mBAAmB,CAAC7C,IAAI,CAAC;EACnC;EAEA4C,OAAO,CAACF,SAAS,CAAC,CAAC;AACrB;AAEO,SAASI,cAAcA,CAAgB9C,IAAyB,EAAE;EACvE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB0C,0BAA0B,CAAC,IAAI,EAAE3C,IAAI,CAAC+C,KAAK,CAAC;AAC9C;AAEO,SAASC,iBAAiBA,CAAgBhD,IAAyB,EAAE;EAC1E,IAAI,CAACC,IAAI,CAAC,UAAU,CAAC;EACrB0C,0BAA0B,CAAC,IAAI,EAAE3C,IAAI,CAAC+C,KAAK,CAAC;AAC9C;AAEO,SAASE,eAAeA,CAAgBjD,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnB0C,0BAA0B,CAAC,IAAI,EAAE3C,IAAI,CAACkD,QAAQ,CAAC;AACjD;AAEO,SAASC,cAAcA,CAAgBnD,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB0C,0BAA0B,CAAC,IAAI,EAAE3C,IAAI,CAACkD,QAAQ,CAAC;AACjD;AAEO,SAASE,gBAAgBA,CAAgBpD,IAAwB,EAAE;EACxE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC+C,KAAK,CAAC;EACtB,IAAI,CAAC5C,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,CAAC;AACvB;AAEO,SAASkC,YAAYA,CAAgBrD,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACsD,KAAK,CAAC;EACtB,IAAI,CAACpD,KAAK,CAAC,CAAC;EAMZ,IAAIF,IAAI,CAACuD,QAAQ,EAAE;IAEjB,IAAI,CAACnD,KAAK,CAACJ,IAAI,CAACuD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,MAAM;IACL,IAAI,CAACnD,KAAK,CAACJ,IAAI,CAACwD,OAAO,CAAC;EAC1B;EAEA,IAAIxD,IAAI,CAACyD,SAAS,EAAE;IAClB,IAAI,CAACvD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACyD,SAAS,CAAC;EAC5B;AACF;AAEO,SAASC,WAAWA,CAAgB1D,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIF,IAAI,CAAC2D,KAAK,EAAE;IACd,IAAI,CAACxD,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC2D,KAAK,CAAC;IACtB,IAAI,CAACvD,KAAK,CAACJ,IAAI,CAAC2D,KAAK,CAACC,cAAc,CAAC;IACrC,IAAI,CAACzD,SAAK,GAAI,CAAC;IACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,CAAC;AACvB;AAEO,SAAS0C,eAAeA,CAAgB7D,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC8D,YAAY,CAAC;EAC7B,IAAI,CAAC3D,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,IAAI,CAAC;EAEf,IAAI,CAAC4D,aAAa,CAChB/D,IAAI,CAACgE,KAAK,EACV,IAAI,EACJC,SAAS,EACT,SAASC,WAAWA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACjC,IAAI,CAACD,OAAO,IAAInE,IAAI,CAACgE,KAAK,CAAChE,IAAI,CAACgE,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC;EACtE,CACF,CAAC;EAED,IAAI,CAACE,UAAU,CAACtE,IAAI,CAAC;AACvB;AAEO,SAASuE,UAAUA,CAAgBvE,IAAkB,EAAE;EAC5D,IAAIA,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACP,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,CAAC;IACrB,IAAI,CAACL,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACE,SAAK,GAAI,CAAC;EACjB;EAEA,IAAIH,IAAI,CAACY,UAAU,CAACyD,MAAM,EAAE;IAC1B,IAAI,CAACxD,OAAO,CAAC,CAAC;IACd,IAAI,CAACkD,aAAa,CAAC/D,IAAI,CAACY,UAAU,EAAE,IAAI,CAAC;EAC3C;AACF;AAEO,SAAS4D,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACvE,IAAI,CAAC,UAAU,CAAC;EACrB,IAAI,CAACyC,SAAS,CAAC,CAAC;AAClB;AAEO,SAAS+B,mBAAmBA,CAEjCzE,IAA2B,EAC3B0E,MAAc,EACd;EACA,IAAI1E,IAAI,CAAC2E,OAAO,EAAE;IAEhB,IAAI,CAAC1E,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,MAAM;IAAE0E;EAAK,CAAC,GAAG5E,IAAI;EACrB,IAAI4E,IAAI,KAAK,aAAa,EAAE;IAC1B,IAAI,CAAC3E,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACA,IAAI,CAAC2E,IAAI,EAAEA,IAAI,KAAK,OAAO,CAAC;EACnC;EACA,IAAI,CAAC1E,KAAK,CAAC,CAAC;EAEZ,IAAI2E,QAAQ,GAAG,KAAK;EAEpB,IAAI,CAAClF,KAAK,CAAC+E,MAAM,CAAC,EAAE;IAClB,KAAK,MAAMI,MAAM,IAAI9E,IAAI,CAAC+E,YAAY,EAAE;MACtC,IAAID,MAAM,CAACpD,IAAI,EAAE;QAEfmD,QAAQ,GAAG,IAAI;MACjB;IACF;EACF;EAcA,IAAI,CAACG,SAAS,CACZhF,IAAI,CAAC+E,YAAY,EACjBd,SAAS,EACTA,SAAS,EACTjE,IAAI,CAAC+E,YAAY,CAACV,MAAM,GAAG,CAAC,EAC5BQ,QAAQ,GACJ,UAAyBI,eAAuB,EAAE;IAChD,IAAI,CAAC9E,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE8E,eAAe,CAAC;IACvC,IAAI,CAACpE,OAAO,CAAC,CAAC;EAChB,CAAC,GACDoD,SACN,CAAC;EAED,IAAItE,KAAK,CAAC+E,MAAM,CAAC,EAAE;IAEjB,IAAI9E,cAAc,CAAC8E,MAAM,CAAC,EAAE;MAC1B,IAAIA,MAAM,CAAChD,IAAI,KAAK1B,IAAI,EAAE;IAC5B,CAAC,MAAM;MACL,IAAI0E,MAAM,CAACtC,IAAI,KAAKpC,IAAI,EAAE;IAC5B;EACF;EAEA,IAAI,CAAC0C,SAAS,CAAC,CAAC;AAClB;AAEO,SAASwC,kBAAkBA,CAAgBlF,IAA0B,EAAE;EAC5E,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACmF,EAAE,CAAC;EACnB,IAAInF,IAAI,CAACoF,QAAQ,EAAE,IAAI,CAACjF,SAAK,GAAI,CAAC;EAElC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACmF,EAAE,CAACvB,cAAc,CAAC;EAClC,IAAI5D,IAAI,CAAC0B,IAAI,EAAE;IACb,IAAI,CAACxB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAK,GAAI,CAAC;IACf,IAAI,CAACD,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAAC0B,IAAI,CAAC;EACvB;AACF", "ignoreList": []}