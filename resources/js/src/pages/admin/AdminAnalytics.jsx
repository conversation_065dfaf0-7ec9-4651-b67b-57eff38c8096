import { useState, useEffect } from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  UsersIcon,
  BuildingStorefrontIcon,
  CurrencyRupeeIcon,
  ShoppingBagIcon,
  ChartBarIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminAnalytics() {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [analyticsData, setAnalyticsData] = useState(null);

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          overview: {
            totalRevenue: {
              value: 1245600,
              change: 12.5,
              trend: 'up'
            },
            totalVendors: {
              value: 120,
              change: 8.3,
              trend: 'up'
            },
            totalUsers: {
              value: 580,
              change: 15.2,
              trend: 'up'
            },
            totalOrders: {
              value: 3245,
              change: -2.1,
              trend: 'down'
            }
          },
          vendorGrowth: [
            { month: 'Jan', count: 45 },
            { month: 'Feb', count: 52 },
            { month: 'Mar', count: 61 },
            { month: 'Apr', count: 67 },
            { month: 'May', count: 75 },
            { month: 'Jun', count: 84 },
            { month: 'Jul', count: 91 },
            { month: 'Aug', count: 98 },
            { month: 'Sep', count: 105 },
            { month: 'Oct', count: 112 },
            { month: 'Nov', count: 118 },
            { month: 'Dec', count: 120 }
          ],
          revenueByMonth: [
            { month: 'Jan', revenue: 78500 },
            { month: 'Feb', revenue: 92400 },
            { month: 'Mar', revenue: 105600 },
            { month: 'Apr', revenue: 87300 },
            { month: 'May', revenue: 91200 },
            { month: 'Jun', revenue: 102500 },
            { month: 'Jul', revenue: 108700 },
            { month: 'Aug', revenue: 115400 },
            { month: 'Sep', revenue: 124600 },
            { month: 'Oct', revenue: 132800 },
            { month: 'Nov', revenue: 128900 },
            { month: 'Dec', revenue: 145600 }
          ],
          topVendors: [
            { id: 1, name: 'Rahul Electronics', revenue: 245600, orders: 128 },
            { id: 2, name: 'Fashion Hub', revenue: 189200, orders: 256 },
            { id: 3, name: 'Tech Solutions', revenue: 178500, orders: 95 },
            { id: 4, name: 'Beauty Essentials', revenue: 152800, orders: 142 },
            { id: 5, name: 'Spice Garden', revenue: 132400, orders: 87 }
          ],
          userAcquisition: {
            organic: 65,
            referral: 20,
            social: 15
          }
        };
        
        setAnalyticsData(mockData);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get stats cards data
  const getStatsCards = () => {
    if (!analyticsData) return [];

    return [
      {
        name: 'Total Revenue',
        value: formatCurrency(analyticsData.overview.totalRevenue.value),
        change: analyticsData.overview.totalRevenue.change,
        trend: analyticsData.overview.totalRevenue.trend,
        icon: CurrencyRupeeIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Vendors',
        value: analyticsData.overview.totalVendors.value,
        change: analyticsData.overview.totalVendors.change,
        trend: analyticsData.overview.totalVendors.trend,
        icon: BuildingStorefrontIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Total Users',
        value: analyticsData.overview.totalUsers.value,
        change: analyticsData.overview.totalUsers.change,
        trend: analyticsData.overview.totalUsers.trend,
        icon: UsersIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Orders',
        value: analyticsData.overview.totalOrders.value,
        change: analyticsData.overview.totalOrders.change,
        trend: analyticsData.overview.totalOrders.trend,
        icon: ShoppingBagIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Platform Analytics</div>
          <h1 className="welcome-title">
            Performance <span style={{ color: '#25D366' }}>Insights</span>
          </h1>
          <p className="welcome-subtitle">
            Monitor platform performance, track growth metrics, and analyze user behavior to make data-driven decisions.
          </p>
        </div>
      </div>

      {/* Time Range Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Overview</h2>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 90 Days</option>
              <option value="year">Last 12 Months</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 500);
              }}
            >
              <ArrowPathIcon className="h-5 w-5" />
              Refresh
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading analytics data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              {getStatsCards().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={item.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                        {item.change}%
                      </span>
                      <span className="text-gray-500 ml-1">vs. previous period</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Revenue Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Revenue Trends</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div style={{ height: '300px', position: 'relative' }}>
                  {analyticsData && (
                    <div className="chart-container">
                      <div className="flex justify-between items-end h-64 relative">
                        <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                          <span>₹150K</span>
                          <span>₹120K</span>
                          <span>₹90K</span>
                          <span>₹60K</span>
                          <span>₹30K</span>
                          <span>₹0</span>
                        </div>
                        <div className="ml-10 flex-1 flex items-end justify-between">
                          {analyticsData.revenueByMonth.map((item, index) => (
                            <div key={index} className="flex flex-col items-center">
                              <div 
                                className="w-8 bg-green-500 rounded-t"
                                style={{ 
                                  height: `${(item.revenue / 150000) * 240}px`,
                                  backgroundColor: '#25D366'
                                }}
                              ></div>
                              <span className="text-xs mt-1 text-gray-500">{item.month}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Vendor Growth Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Vendor Growth</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div style={{ height: '300px', position: 'relative' }}>
                  {analyticsData && (
                    <div className="chart-container">
                      <div className="flex justify-between items-end h-64 relative">
                        <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                          <span>120</span>
                          <span>100</span>
                          <span>80</span>
                          <span>60</span>
                          <span>40</span>
                          <span>20</span>
                          <span>0</span>
                        </div>
                        <div className="ml-10 flex-1 flex items-end">
                          {analyticsData.vendorGrowth.map((item, index, arr) => {
                            const prevCount = index > 0 ? arr[index - 1].count : 0;
                            return (
                              <div key={index} className="flex-1 flex flex-col items-center">
                                <div className="relative w-full">
                                  <div 
                                    className="absolute bottom-0 left-0 right-0 border-t-2 border-blue-500"
                                    style={{ 
                                      height: `${(item.count / 120) * 240}px`,
                                      borderColor: '#075E54'
                                    }}
                                  ></div>
                                  <div 
                                    className="w-3 h-3 rounded-full bg-blue-500 absolute"
                                    style={{ 
                                      bottom: `${(item.count / 120) * 240}px`,
                                      left: '50%',
                                      transform: 'translate(-50%, 50%)',
                                      backgroundColor: '#075E54'
                                    }}
                                  ></div>
                                </div>
                                <span className="text-xs mt-2 text-gray-500">{item.month}</span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Top Vendors */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Top Performing Vendors</h2>
              </div>
              <div className="card" style={{ padding: '0' }}>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Vendor</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Revenue</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Orders</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData && analyticsData.topVendors.map((vendor) => (
                      <tr key={vendor.id} className="border-b hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="font-medium text-gray-900">{vendor.name}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{formatCurrency(vendor.revenue)}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{vendor.orders}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* User Acquisition */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">User Acquisition</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                {analyticsData && (
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="w-full md:w-1/2 flex justify-center">
                      <div className="relative w-64 h-64">
                        <svg viewBox="0 0 100 100" className="w-full h-full">
                          {/* Organic slice */}
                          <path
                            d="M 50 50 L 50 0 A 50 50 0 0 1 97.55 34.55 Z"
                            fill="#25D366"
                            stroke="white"
                            strokeWidth="1"
                          />
                          {/* Referral slice */}
                          <path
                            d="M 50 50 L 97.55 34.55 A 50 50 0 0 1 82.14 91.35 Z"
                            fill="#075E54"
                            stroke="white"
                            strokeWidth="1"
                          />
                          {/* Social slice */}
                          <path
                            d="M 50 50 L 82.14 91.35 A 50 50 0 0 1 50 0 Z"
                            fill="#128C7E"
                            stroke="white"
                            strokeWidth="1"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="w-full md:w-1/2 mt-6 md:mt-0">
                      <div className="space-y-4">
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-green-500 mr-2" style={{ backgroundColor: '#25D366' }}></div>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Organic</span>
                              <span className="text-sm font-medium">{analyticsData.userAcquisition.organic}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                              <div className="bg-green-500 h-2 rounded-full" style={{ width: `${analyticsData.userAcquisition.organic}%`, backgroundColor: '#25D366' }}></div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-blue-800 mr-2" style={{ backgroundColor: '#075E54' }}></div>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Referral</span>
                              <span className="text-sm font-medium">{analyticsData.userAcquisition.referral}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                              <div className="bg-blue-800 h-2 rounded-full" style={{ width: `${analyticsData.userAcquisition.referral}%`, backgroundColor: '#075E54' }}></div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-teal-600 mr-2" style={{ backgroundColor: '#128C7E' }}></div>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Social</span>
                              <span className="text-sm font-medium">{analyticsData.userAcquisition.social}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                              <div className="bg-teal-600 h-2 rounded-full" style={{ width: `${analyticsData.userAcquisition.social}%`, backgroundColor: '#128C7E' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
