<?php

namespace App\Http\Controllers;

use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class StoreController extends Controller
{
    /**
     * Get the authenticated vendor's store information.
     */
    public function getStoreInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                Log::error('getStoreInfo: Auth::user() is null. Request IP: ' . $request->ip());
                return response()->json(['message' => 'User not authenticated.'], 401);
            }

            $store = $user->store; // This attempts to load the store relationship

            if (!$store) {
                Log::warning('getStoreInfo: Store not found for user ID: ' . $user->id . '. Request IP: ' . $request->ip());
                return response()->json(['message' => 'Store not found for this vendor.'], 404);
            }

            return response()->json($store->toArray());

        } catch (\Throwable $e) {
            Log::error('Exception in getStoreInfo for user ID: ' . (Auth::check() ? Auth::id() : 'Guest') . ' - Error: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine() . ' Stack trace: ' . $e->getTraceAsString());
            
            return response()->json([
                'message' => 'A server error occurred while fetching store information. Please try again later.',
                'error_details' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the authenticated vendor's store information.
     */
    public function updateStoreInfo(Request $request)
    {
        $store = Auth::user()->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for this vendor.'], 404);
        }

        $validatedData = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Input file is 'logo'
            'storeUrl' => 'sometimes|required|string|unique:stores,storeUrl,' . $store->id,
            // Add other fillable fields from your Store model that can be updated
            'businessCategory' => 'sometimes|string',
            'businessSubcategory' => 'sometimes|string',
            'businessType' => 'sometimes|string',
            'productType' => 'nullable|string', // Nullable if businessType is 'service'
        ]);

        // Prepare data for update, excluding the 'logo' file itself from direct mass assignment initially
        $updateData = collect($validatedData)->except('logo')->all();

        if ($request->hasFile('logo')) {
            // Delete old logo if it exists (using the 'logoUrl' field which stores the path)
            if ($store->logoUrl) {
                Storage::disk('public')->delete($store->logoUrl);
            }
            // Store new logo
            $path = $request->file('logo')->store('logos', 'public');
            $updateData['logoUrl'] = $path; // Save the path to the 'logoUrl' field
        } else {
            // If 'logo' is not in the request, but 'logoUrl' might be (e.g. if we want to clear it by sending empty string for 'logo'),
            // this logic might need adjustment. For now, we assume if no new logo, existing logoUrl remains unless explicitly cleared.
            // If $request->input('logo') is specifically null or empty string, and you want to remove the logo:
            if ($request->exists('logo') && !$request->file('logo') && is_null($request->input('logo'))) {
                 if ($store->logoUrl) {
                    Storage::disk('public')->delete($store->logoUrl);
                }
                $updateData['logoUrl'] = null;
            }
        }

        $store->update($updateData);

        return response()->json([
            'message' => 'Store information updated successfully.',
            'store' => $store
        ]);
    }
}
