<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Order;
use App\Models\Product;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class CheckoutController extends Controller
{
    public function placeOrder(Request $request, $storeUrl)
    {
        $store = Store::where('storeUrl', $storeUrl)->firstOrFail();

        $validatedData = $request->validate([
            'customer.name' => 'required|string|max:255',
            'customer.email' => 'required|email|max:255',
            'customer.phone' => 'nullable|string|max:20',
            'shipping_address' => 'required|string',
            'billing_address' => 'nullable|string',
            'payment_method' => 'required|string',
            'items' => 'required|array',
            'items.*.product_id' => [
                'required',
                'integer',
                Rule::exists('products', 'id')->where(function ($query) use ($store) {
                    return $query->where('store_id', $store->id);
                }),
            ],
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        return DB::transaction(function () use ($validatedData, $store) {
            // Find or create the customer
            $customer = Customer::firstOrCreate(
                [
                    'store_id' => $store->id,
                    'email' => $validatedData['customer']['email'],
                ],
                [
                    'name' => $validatedData['customer']['name'],
                    'phone' => $validatedData['customer']['phone'] ?? null,
                ]
            );

            $totalAmount = 0;
            $orderItemsData = [];

            foreach ($validatedData['items'] as $item) {
                $product = Product::find($item['product_id']);

                if ($product->stock < $item['quantity']) {
                    return response()->json(['message' => 'Product ' . $product->name . ' is out of stock.'], 422);
                }

                $totalAmount += $product->price * $item['quantity'];
                $orderItemsData[] = [
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price' => $product->price,
                ];

                // Decrease stock
                $product->decrement('stock', $item['quantity']);
            }

            // Create the order
            $order = $store->orders()->create([
                'customer_id' => $customer->id,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'shipping_address' => $validatedData['shipping_address'],
                'billing_address' => $validatedData['billing_address'] ?? $validatedData['shipping_address'],
                'payment_method' => $validatedData['payment_method'],
                'payment_status' => 'pending',
            ]);

            // Create order items
            $order->items()->createMany($orderItemsData);

            return response()->json([
                'message' => 'Order placed successfully.',
                'order' => $order->load('items')
            ], 201);
        });
    }
}
