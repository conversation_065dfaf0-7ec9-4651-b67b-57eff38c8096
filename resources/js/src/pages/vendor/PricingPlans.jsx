import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { CheckIcon } from '@heroicons/react/24/solid';
import { subscriptionAPI } from '../../services/api';
import '../../styles/vendor-dashboard.css';

export default function PricingPlans() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  
  // Fetch plans and current subscription
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch all plans
        const plansResponse = await subscriptionAPI.getAllPlans();
        setPlans(plansResponse.data.plans || []);
        
        // Fetch current subscription
        const subscriptionResponse = await subscriptionAPI.getCurrentSubscription();
        if (subscriptionResponse.data.subscription) {
          setCurrentSubscription(subscriptionResponse.data.subscription);
        } else if (subscriptionResponse.data.defaultPlan) {
          // If no subscription found, use the default plan
          setCurrentSubscription({
            plan: {
              id: 0,
              name: subscriptionResponse.data.defaultPlan.name
            }
          });
        }
      } catch (error) {
        console.error('Error fetching plans:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Format price to INR
  const formatPrice = (price) => {
    return `₹${parseFloat(price).toLocaleString('en-IN')}`;
  };
  
  // Handle plan upgrade
  const handleUpgrade = async (planId) => {
    try {
      // In a real implementation, this would redirect to a payment page
      // or open a payment modal
      alert(`Redirecting to payment page for plan ID: ${planId}`);
      
      // For demo purposes, we'll just create a subscription directly
      // await subscriptionAPI.createSubscription({
      //   planId,
      //   paymentMethod: 'upi',
      //   transactionId: `DEMO-${Date.now()}`,
      // });
      
      // Refresh the page after subscription
      // window.location.reload();
    } catch (error) {
      console.error('Error upgrading plan:', error);
      alert('Failed to upgrade plan. Please try again.');
    }
  };
  
  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Subscription Plans</div>
          <h1 className="welcome-title">
            Choose Your Plan
          </h1>
          <p className="welcome-subtitle">
            Select the plan that best fits your business needs. Upgrade anytime to access more features.
          </p>
        </div>
      </div>
      
      {/* Plans Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Available Plans</h2>
        </div>
        
        {loading ? (
          <div className="card" style={{ padding: '40px', textAlign: 'center' }}>
            <p>Loading subscription plans...</p>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px',
            marginBottom: '40px'
          }}>
            {plans.map((plan) => {
              const isCurrentPlan = currentSubscription && 
                currentSubscription.plan && 
                currentSubscription.plan.id === plan.id;
              
              return (
                <div key={plan.id} style={{
                  backgroundColor: '#fff',
                  borderRadius: '16px',
                  padding: '30px',
                  boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                  display: 'flex',
                  flexDirection: 'column',
                  border: isCurrentPlan ? '2px solid #25D366' : '1px solid rgba(0,0,0,0.03)',
                  position: 'relative',
                  height: '100%'
                }}>
                  {isCurrentPlan && (
                    <div style={{
                      position: 'absolute',
                      top: '0',
                      right: '30px',
                      transform: 'translateY(-50%)',
                      backgroundColor: '#25D366',
                      color: 'white',
                      padding: '8px 16px',
                      borderRadius: '50px',
                      fontSize: '14px',
                      fontWeight: 600
                    }}>
                      Current Plan
                    </div>
                  )}
                  
                  {plan.isPopular && !isCurrentPlan && (
                    <div style={{
                      position: 'absolute',
                      top: '0',
                      right: '30px',
                      transform: 'translateY(-50%)',
                      backgroundColor: '#075E54',
                      color: 'white',
                      padding: '8px 16px',
                      borderRadius: '50px',
                      fontSize: '14px',
                      fontWeight: 600
                    }}>
                      Most Popular
                    </div>
                  )}
                  
                  <div style={{ marginBottom: '24px' }}>
                    <h3 style={{
                      fontSize: '24px',
                      fontWeight: 700,
                      marginBottom: '8px',
                      color: '#212529'
                    }}>
                      {plan.name}
                    </h3>
                    <p style={{
                      fontSize: '16px',
                      color: '#6C757D',
                      marginBottom: '24px'
                    }}>
                      {plan.description}
                    </p>
                    
                    <div style={{
                      display: 'flex',
                      alignItems: 'baseline',
                      marginBottom: '8px'
                    }}>
                      {plan.originalPrice && (
                        <span style={{
                          fontSize: '14px',
                          color: '#6C757D',
                          textDecoration: 'line-through',
                          marginRight: '8px'
                        }}>
                          {formatPrice(plan.originalPrice)}/year
                        </span>
                      )}
                      
                      {plan.originalPrice && (
                        <span style={{
                          fontSize: '14px',
                          color: '#25D366',
                          fontWeight: 600,
                          backgroundColor: 'rgba(37, 211, 102, 0.1)',
                          padding: '4px 8px',
                          borderRadius: '4px'
                        }}>
                          Save 25%
                        </span>
                      )}
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      alignItems: 'baseline'
                    }}>
                      <span style={{
                        fontSize: '36px',
                        fontWeight: 700,
                        color: '#212529'
                      }}>
                        {formatPrice(plan.price)}
                      </span>
                      <span style={{
                        fontSize: '16px',
                        color: '#6C757D',
                        marginLeft: '4px'
                      }}>
                        /year
                      </span>
                    </div>
                  </div>
                  
                  <div style={{ marginBottom: '24px' }}>
                    <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                      {plan.features && plan.features.map((feature, index) => (
                        <li key={index} style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: '12px'
                        }}>
                          <CheckIcon style={{
                            width: '20px',
                            height: '20px',
                            color: '#25D366',
                            marginRight: '12px',
                            flexShrink: 0
                          }} />
                          <span style={{ fontSize: '15px', color: '#212529' }}>
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div style={{ marginTop: 'auto' }}>
                    {isCurrentPlan ? (
                      <button
                        disabled
                        style={{
                          width: '100%',
                          padding: '12px',
                          backgroundColor: '#E9ECEF',
                          color: '#6C757D',
                          border: 'none',
                          borderRadius: '8px',
                          fontSize: '16px',
                          fontWeight: 600,
                          cursor: 'not-allowed'
                        }}
                      >
                        Current Plan
                      </button>
                    ) : (
                      <button
                        onClick={() => handleUpgrade(plan.id)}
                        style={{
                          width: '100%',
                          padding: '12px',
                          backgroundColor: '#25D366',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          fontSize: '16px',
                          fontWeight: 600,
                          cursor: 'pointer',
                          transition: 'background-color 0.2s'
                        }}
                      >
                        Upgrade to {plan.name}
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
