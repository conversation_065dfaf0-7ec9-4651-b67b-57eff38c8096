import React from 'react';
import { usePreloader } from '../../contexts/PreloaderContext';

/**
 * GlobalPreloader component
 * Used to show a loading screen across the entire application
 */
const GlobalPreloader = () => {
  const { loading } = usePreloader();

  const [visible, setVisible] = React.useState(loading);

  // Use effect to handle the visibility with animation
  React.useEffect(() => {
    if (loading) {
      setVisible(true);
    } else {
      // Delay hiding the component to allow for fade-out animation
      const timer = setTimeout(() => {
        setVisible(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [loading]);

  if (!visible) return null;

  return (
    <div className="global-preloader" style={{ opacity: loading ? 1 : 0 }}>
      <div className="preloader-content">
        <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="preloader-logo" />
        <div className="preloader-spinner"></div>
        <div className="preloader-tagline">दुकान Online है ❤️</div>
      </div>
    </div>
  );
};

export default GlobalPreloader;
