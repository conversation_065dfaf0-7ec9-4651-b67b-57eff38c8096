import { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  PresentationChartLineIcon,
  ChartBarIcon,
  CurrencyRupeeIcon,
  BuildingStorefrontIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorProjections() {
  const [loading, setLoading] = useState(true);
  const [projectionData, setProjectionData] = useState({
    scenarios: {
      conservative: {
        growthRate: 0,
        vendorGrowth: 0,
        revenueGrowth: 0,
        profitMarginGrowth: 0
      },
      moderate: {
        growthRate: 0,
        vendorGrowth: 0,
        revenueGrowth: 0,
        profitMarginGrowth: 0
      },
      aggressive: {
        growthRate: 0,
        vendorGrowth: 0,
        revenueGrowth: 0,
        profitMarginGrowth: 0
      }
    },
    currentMetrics: {
      companyValuation: 0,
      totalVendors: 0,
      monthlyRevenue: 0,
      annualRevenue: 0,
      profitMargin: 0
    },
    projections: {
      valuation: {
        conservative: [],
        moderate: [],
        aggressive: []
      },
      vendors: {
        conservative: [],
        moderate: [],
        aggressive: []
      },
      revenue: {
        conservative: [],
        moderate: [],
        aggressive: []
      },
      profitMargin: {
        conservative: [],
        moderate: [],
        aggressive: []
      }
    },
    keyAssumptions: {
      conservative: [],
      moderate: [],
      aggressive: []
    },
    riskFactors: []
  });
  const [projectionScenario, setProjectionScenario] = useState('moderate');

  // Fetch projection data
  useEffect(() => {
    const fetchProjectionData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          scenarios: {
            conservative: {
              growthRate: 35,
              vendorGrowth: 25,
              revenueGrowth: 35,
              profitMarginGrowth: 5
            },
            moderate: {
              growthRate: 50,
              vendorGrowth: 40,
              revenueGrowth: 50,
              profitMarginGrowth: 10
            },
            aggressive: {
              growthRate: 75,
              vendorGrowth: 60,
              revenueGrowth: 75,
              profitMarginGrowth: 15
            }
          },
          currentMetrics: {
            companyValuation: 30000000, // 3 करोड़
            totalVendors: 780,
            monthlyRevenue: 117000,
            annualRevenue: 1404000,
            profitMargin: 25.6,
            totalMarketSize: 500000, // 5 लाख वेंडर्स
            currentMarketShare: 0.156, // 0.156%
            targetMarketShare: 5 // 5% in 5 years
          },
          projections: {
            valuation: {
              conservative: [
                { year: '2025', value: 30000000 }, // 3 करोड़
                { year: '2026', value: 60000000 }, // 6 करोड़
                { year: '2027', value: ********* }, // 12 करोड़
                { year: '2028', value: ********* }, // 24 करोड़
                { year: '2029', value: ********* } // 48 करोड़
              ],
              moderate: [
                { year: '2025', value: 30000000 }, // 3 करोड़
                { year: '2026', value: ********* }, // 10 करोड़
                { year: '2027', value: 300000000 }, // 30 करोड़
                { year: '2028', value: 750000000 }, // 75 करोड़
                { year: '2029', value: 1500000000 } // 150 करोड़
              ],
              aggressive: [
                { year: '2025', value: 30000000 }, // 3 करोड़
                { year: '2026', value: 150000000 }, // 15 करोड़
                { year: '2027', value: 600000000 }, // 60 करोड़
                { year: '2028', value: 1800000000 }, // 180 करोड़
                { year: '2029', value: 4500000000 } // 450 करोड़
              ]
            },
            vendors: {
              conservative: [
                { year: '2025', value: 2500 },
                { year: '2026', value: 5000 },
                { year: '2027', value: 8500 },
                { year: '2028', value: 12000 },
                { year: '2029', value: 15000 }
              ],
              moderate: [
                { year: '2025', value: 2500 },
                { year: '2026', value: 6000 },
                { year: '2027', value: 12000 },
                { year: '2028', value: 18000 },
                { year: '2029', value: 25000 }
              ],
              aggressive: [
                { year: '2025', value: 2500 },
                { year: '2026', value: 8000 },
                { year: '2027', value: 16000 },
                { year: '2028', value: 25000 },
                { year: '2029', value: 35000 }
              ]
            },
            revenue: {
              conservative: [
                { year: '2025', value: 3750000 }, // 2500 vendors * avg 1500 per vendor
                { year: '2026', value: 7500000 }, // 5000 vendors * avg 1500 per vendor
                { year: '2027', value: 12750000 }, // 8500 vendors * avg 1500 per vendor
                { year: '2028', value: 18000000 }, // 12000 vendors * avg 1500 per vendor
                { year: '2029', value: 22500000 } // 15000 vendors * avg 1500 per vendor
              ],
              moderate: [
                { year: '2025', value: 3750000 }, // 2500 vendors * avg 1500 per vendor
                { year: '2026', value: 12000000 }, // 6000 vendors * avg 2000 per vendor
                { year: '2027', value: 30000000 }, // 12000 vendors * avg 2500 per vendor
                { year: '2028', value: 54000000 }, // 18000 vendors * avg 3000 per vendor
                { year: '2029', value: 87500000 } // 25000 vendors * avg 3500 per vendor
              ],
              aggressive: [
                { year: '2025', value: 3750000 }, // 2500 vendors * avg 1500 per vendor
                { year: '2026', value: 20000000 }, // 8000 vendors * avg 2500 per vendor
                { year: '2027', value: 56000000 }, // 16000 vendors * avg 3500 per vendor
                { year: '2028', value: ********* }, // 25000 vendors * avg 4000 per vendor
                { year: '2029', value: 175000000 } // 35000 vendors * avg 5000 per vendor
              ]
            },
            profitMargin: {
              conservative: [
                { year: '2025', value: 25.6 },
                { year: '2026', value: 26.88 },
                { year: '2027', value: 28.22 },
                { year: '2028', value: 29.63 },
                { year: '2029', value: 31.12 }
              ],
              moderate: [
                { year: '2025', value: 25.6 },
                { year: '2026', value: 28.16 },
                { year: '2027', value: 30.98 },
                { year: '2028', value: 34.07 },
                { year: '2029', value: 37.48 }
              ],
              aggressive: [
                { year: '2025', value: 25.6 },
                { year: '2026', value: 29.44 },
                { year: '2027', value: 33.86 },
                { year: '2028', value: 38.94 },
                { year: '2029', value: 44.78 }
              ]
            }
          },
          keyAssumptions: {
            conservative: [
              "100% vendor growth in first year, then 50-70% YoY",
              "100% annual revenue growth rate",
              "5% annual profit margin improvement",
              "Limited geographic expansion (5-8 cities per year)",
              "Minimal product feature additions",
              "3% market share by 2029"
            ],
            moderate: [
              "100% vendor growth in first year, then 80-100% YoY",
              "150-200% annual revenue growth rate",
              "10% annual profit margin improvement",
              "Steady geographic expansion (8-12 cities per year)",
              "Regular product feature additions",
              "5% market share by 2029"
            ],
            aggressive: [
              "100% vendor growth in first year, then 100-120% YoY",
              "200-300% annual revenue growth rate",
              "15% annual profit margin improvement",
              "Rapid geographic expansion (12-15 cities per year)",
              "Accelerated product development and feature additions",
              "7% market share by 2029"
            ]
          },
          riskFactors: [
            {
              factor: "Market Competition",
              impact: "High",
              mitigation: "Continuous product innovation and strong vendor relationships"
            },
            {
              factor: "Regulatory Changes",
              impact: "Medium",
              mitigation: "Proactive compliance monitoring and adaptable business model"
            },
            {
              factor: "Technology Disruption",
              impact: "Medium",
              mitigation: "Investment in R&D and staying ahead of technology trends"
            },
            {
              factor: "Economic Downturn",
              impact: "Medium",
              mitigation: "Diversified revenue streams and prudent financial management"
            },
            {
              factor: "Scaling Challenges",
              impact: "High",
              mitigation: "Robust infrastructure planning and phased expansion approach"
            }
          ]
        };

        setProjectionData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching projection data:', error);
        setLoading(false);
      }
    };

    fetchProjectionData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Get projection metrics
  const getProjectionMetrics = () => {
    if (!projectionData) return [];

    const scenario = projectionData.scenarios[projectionScenario];

    return [
      {
        name: 'Growth Rate',
        value: formatPercentage(scenario.growthRate),
        icon: PresentationChartLineIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Vendor Growth',
        value: formatPercentage(scenario.vendorGrowth),
        icon: BuildingStorefrontIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Revenue Growth',
        value: formatPercentage(scenario.revenueGrowth),
        icon: CurrencyRupeeIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Profit Margin Growth',
        value: formatPercentage(scenario.profitMarginGrowth),
        icon: ChartBarIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Growth Projections</div>
          <h1 className="welcome-title">
            Future <span style={{ color: '#25D366' }}>Growth</span>
          </h1>
          <p className="welcome-subtitle">
            Explore different growth scenarios and projections for the next 5 years.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Scenario Selection and Export Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                projectionScenario === 'conservative'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setProjectionScenario('conservative')}
            >
              Conservative
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium ${
                projectionScenario === 'moderate'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setProjectionScenario('moderate')}
            >
              Moderate
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                projectionScenario === 'aggressive'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setProjectionScenario('aggressive')}
            >
              Aggressive
            </button>
          </div>

          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Projections
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading projection data...</p>
          </div>
        ) : (
          <>
            {/* Projection Metrics */}
            <div className="stats-grid">
              {projectionData && getProjectionMetrics().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Valuation Projection */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Valuation Projection</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div style={{ height: '300px', position: 'relative' }}>
                  {/* This would be a real chart in production */}
                  <div className="bg-gray-100 h-full w-full rounded-lg flex items-center justify-center">
                    <div className="flex items-end h-64 w-full px-8">
                      {projectionData.projections.valuation[projectionScenario].map((item, index) => {
                        const maxValue = Math.max(...projectionData.projections.valuation[projectionScenario].map(i => i.value));
                        const height = (item.value / maxValue) * 240;

                        return (
                          <div key={index} className="flex-1 flex flex-col items-center">
                            <div
                              className={`w-16 rounded-t-md ${index > 0 ? 'border border-dashed border-green-500' : ''}`}
                              style={{
                                height: `${height}px`,
                                backgroundColor: index === 0 ? '#25D366' : 'rgba(37, 211, 102, 0.2)'
                              }}
                            ></div>
                            <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                            <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-sm text-gray-500 text-center">
                    Projected company valuation growth under {projectionScenario} scenario
                  </p>
                </div>
              </div>
            </div>

            {/* Revenue and Vendor Projections */}
            <div className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="section-header">
                    <h2 className="section-title">Revenue Projection</h2>
                  </div>
                  <div className="card" style={{ padding: '24px' }}>
                    <div className="space-y-4">
                      {projectionData.projections.revenue[projectionScenario].map((item, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-16 text-sm font-medium text-gray-900">{item.year}</div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full bg-green-500"
                                style={{
                                  width: `${(item.value / projectionData.projections.revenue[projectionScenario][projectionData.projections.revenue[projectionScenario].length - 1].value) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                          <div className="w-32 text-right text-sm font-medium text-gray-900">{formatCurrency(item.value)}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="section-header">
                    <h2 className="section-title">Vendor Projection</h2>
                  </div>
                  <div className="card" style={{ padding: '24px' }}>
                    <div className="space-y-4">
                      {projectionData.projections.vendors[projectionScenario].map((item, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-16 text-sm font-medium text-gray-900">{item.year}</div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full bg-accent"
                                style={{
                                  width: `${(item.value / projectionData.projections.vendors[projectionScenario][projectionData.projections.vendors[projectionScenario].length - 1].value) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                          <div className="w-16 text-right text-sm font-medium text-gray-900">{item.value}</div>
                          {index > 0 && (
                            <div className="w-24 text-right text-xs text-green-600">
                              +{Math.round((item.value / projectionData.projections.vendors[projectionScenario][index - 1].value - 1) * 100)}%
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Profit Margin Projection */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Profit Margin Projection</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="space-y-4">
                  {projectionData.projections.profitMargin[projectionScenario].map((item, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-16 text-sm font-medium text-gray-900">{item.year}</div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full bg-green-500"
                            style={{ width: `${item.value}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="w-16 text-right text-sm font-medium text-gray-900">{formatPercentage(item.value)}</div>
                      {index > 0 && (
                        <div className="w-24 text-right text-xs text-green-600">
                          +{(item.value - projectionData.projections.profitMargin[projectionScenario][index - 1].value).toFixed(2)}%
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Key Assumptions */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Key Assumptions</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {projectionScenario.charAt(0).toUpperCase() + projectionScenario.slice(1)} Scenario Assumptions
                </h3>
                <ul className="space-y-2 pl-5 list-disc">
                  {projectionData.keyAssumptions[projectionScenario].map((assumption, index) => (
                    <li key={index} className="text-gray-700">{assumption}</li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Risk Factors */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Risk Factors</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Risk Factor
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Impact
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Mitigation Strategy
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {projectionData.riskFactors.map((risk, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {risk.factor}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              risk.impact === 'High'
                                ? 'bg-red-100 text-red-800'
                                : risk.impact === 'Medium'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                            }`}>
                              {risk.impact}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {risk.mitigation}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
