import React from 'react';
import ChatMessage from './ChatMessage';
import DateSeparator from './DateSeparator';
import OfficialServiceBanner from './OfficialServiceBanner';

/**
 * ChatBody component
 * Displays the chat messages with date separators and official service banner
 */
const ChatBody = ({ messages, messagesEndRef }) => {
  // Determine if we should show a date separator between messages
  const shouldShowDateSeparator = (currentMsg, prevMsg) => {
    if (!prevMsg) return true;

    const currentDate = new Date(currentMsg.timestamp).setHours(0, 0, 0, 0);
    const prevDate = new Date(prevMsg.timestamp).setHours(0, 0, 0, 0);

    return currentDate !== prevDate;
  };

  // Create inline style with direct background image and overlay
  const chatBodyStyle = {
    flex: 1,
    overflowY: 'auto',
    padding: '16px',
    backgroundColor: '#E5DDD5',
    backgroundImage: "url('/chat-background.jpg')",
    backgroundRepeat: 'repeat',
    backgroundSize: '300px auto', // Slightly larger size for WhatsApp style
    position: 'relative' // For positioning the overlay
  };

  // Overlay style with 10% opacity of #E6DEB2 color
  const overlayStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(230, 222, 178, 0.15)', // #E6DEB2 with 15% opacity
    pointerEvents: 'none', // Allow clicks to pass through to elements below
    zIndex: 1 // Ensure overlay is above the background but below the content
  };

  // Content container style to position above the overlay
  const contentStyle = {
    position: 'relative',
    zIndex: 2 // Higher than the overlay
  };

  return (
    <div className="chat-body" style={chatBodyStyle}>
      {/* Gray overlay */}
      <div style={overlayStyle}></div>

      {/* Content container - positioned above the overlay */}
      <div style={contentStyle}>
        {/* Official service banner */}
        <OfficialServiceBanner />

        {/* Messages with date separators */}
        {messages.map((message, index) => {
          const prevMessage = index > 0 ? messages[index - 1] : null;
          const showDateSeparator = shouldShowDateSeparator(message, prevMessage);

          return (
            <React.Fragment key={message.id}>
              {showDateSeparator && (
                <DateSeparator date={new Date(message.timestamp)} />
              )}
              <ChatMessage message={message} />
            </React.Fragment>
          );
        })}

        {/* Reference for auto-scrolling */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatBody;
