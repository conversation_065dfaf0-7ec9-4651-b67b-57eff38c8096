import React, { useState, useEffect, useRef } from 'react';
import Cha<PERSON><PERSON>eader from './ChatHeader';
import Chat<PERSON><PERSON> from './ChatBody';
import ChatInputBar from './ChatInputBar';
import { productAPI } from '../../services/api';
import { ensureValidImageUrl } from '../../utils/imageUtils';
import './styles/StoreTheme.css';
import './styles/background.css';

/**
 * StoreTheme component
 * Main container for the WhatsApp-style store interface
 * This is an independent component without sidebar or theme header
 */
const StoreTheme = ({ storeData = {}, chatFlow = null }) => {
  // Default store data if not provided
  const defaultStoreData = {
    name: 'Store Name',
    isVerified: true,
    id: '123'
  };

  // Merge provided store data with defaults
  const store = { ...defaultStoreData, ...storeData };

  // Store data is merged with defaults

  // State for messages and products
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [vendorProducts, setVendorProducts] = useState(null);
  const messagesEndRef = useRef(null);

  // Store the full chat flow data in a ref for later use
  const chatFlowRef = useRef(null);

  // Initialize with only the first message from chat flow
  useEffect(() => {
    // Process chat flow data if available
    if (chatFlow && chatFlow.flowData && chatFlow.flowData.nodes) {
      try {
        // Store the full chat flow data for later use
        chatFlowRef.current = chatFlow.flowData;

        // Find the starting node (usually the first node)
        const startNode = chatFlow.flowData.nodes.find(node =>
          node.type === 'startNode' ||
          node.id === chatFlow.flowData.startNodeId ||
          node.position?.x === 0
        ) || chatFlow.flowData.nodes[0];

        if (startNode && startNode.data && startNode.data.content) {
          // Create a message from the starting node only
          const message = {
            id: startNode.id,
            content: startNode.data.content,
            sender: 'store',
            timestamp: new Date(),
            status: 'read',
            nodeId: startNode.id // Store the node ID for reference
          };

          // Add buttons if present
          if (startNode.data.buttons && startNode.data.buttons.length > 0) {
            message.buttons = startNode.data.buttons.map(button => ({
              type: button.type || 'quick_reply',
              text: button.text,
              url: button.url,
              phoneNumber: button.phoneNumber,
              nextNodeId: button.nextNodeId // Store the next node ID if available
            }));
            message.onQuickReply = handleQuickReply;
          } else {
            // If no buttons are defined but there are outgoing edges, create default buttons
            if (chatFlow.flowData.edges) {
              const outgoingEdges = chatFlow.flowData.edges.filter(edge => edge.source === startNode.id);

              if (outgoingEdges.length > 0) {
                message.buttons = outgoingEdges.map((edge, index) => {
                  // Find the target node to get its label if available
                  const targetNode = chatFlow.flowData.nodes.find(node => node.id === edge.target);
                  return {
                    type: 'quick_reply',
                    text: edge.label || targetNode?.data?.buttonLabel || `Option ${index + 1}`,
                    nextNodeId: edge.target
                  };
                });
                message.onQuickReply = handleQuickReply;
              }
            }
          }

          // Add header if present
          if (startNode.data.header) {
            message.header = startNode.data.header;
          }

          // Add image if present
          if (startNode.data.image) {
            // Handle image object
            if (typeof startNode.data.image === 'object') {
              // Try to find any property that might contain a data URL
              for (const key of Object.keys(startNode.data.image)) {
                if (typeof startNode.data.image[key] === 'string' && startNode.data.image[key].startsWith('data:image/')) {
                  message.image = startNode.data.image[key];
                  break;
                }
              }

              // If we couldn't find a direct data URL, try to extract from JSON
              if (typeof message.image === 'object') {
                try {
                  const jsonString = JSON.stringify(startNode.data.image);
                  const dataUrlMatch = jsonString.match(/data:image\/[^"',}\]]+/g);
                  if (dataUrlMatch && dataUrlMatch.length > 0) {
                    message.image = dataUrlMatch[0];
                  } else {
                    // Just use the object as is, our enhanced image handling in ChatMessage will try to extract it
                    message.image = startNode.data.image;
                  }
                } catch (err) {
                  message.image = startNode.data.image;
                }
              }
            } else {
              // Direct string assignment
              message.image = startNode.data.image;
            }
          }

          setMessages([message]);
          setLoading(false);
          return;
        }
      } catch (error) {
        // Show loading message instead of default welcome message
        setLoading(true);
      }
    } else {
      // Show loading message if no chat flow is available
      setLoading(true);
    }

    // Hide loading state after a delay if we're still loading
    setTimeout(() => {
      setLoading(false);
    }, 1500);
  }, [store.name, chatFlow]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Fetch vendor products when store data is available
  useEffect(() => {
    const fetchVendorProducts = async () => {
      if (store && store.id) {
        try {
          // Use the store ID directly
          let storeIdToUse = store.id;

          // Simple API call to get products
          const response = await productAPI.getProductsByStoreId(storeIdToUse);

          if (response && response.data && response.data.products) {
            // Process the products
            const productsData = response.data.products;

            // Validate and format products
            const validatedProducts = productsData.map(product => ({
              id: product.id || `product-${Date.now()}`,
              name: product.name || 'Product',
              price: parseFloat(product.price) || 0,
              category: product.category || 'Uncategorized',
              description: product.description || '',
              image: ensureValidImageUrl(product.imageUrl || product.image || '/default-product-image.jpg'),
              imageUrl: ensureValidImageUrl(product.imageUrl || product.image || '/default-product-image.jpg'),
              stock: product.stock || 0,
              isActive: true,
              storeId: product.storeId || store.id
            }));

            // Group products by category
            const productsByCategory = {
              'all': {
                id: 'all',
                name: store.businessType === 'service' ? 'All Services' : 'All Products',
                products: validatedProducts
              }
            };

            // Group by actual categories
            validatedProducts.forEach(product => {
              const category = product.category || 'Uncategorized';

              if (!productsByCategory[category]) {
                productsByCategory[category] = {
                  id: category,
                  name: category,
                  products: []
                };
              }

              productsByCategory[category].products.push(product);
            });

            // Convert to array
            const categoriesArray = Object.values(productsByCategory);

            // Store in state and session storage
            setVendorProducts(categoriesArray);
            sessionStorage.setItem('vendorProducts', JSON.stringify(categoriesArray));
          } else {
            // Try to get from session storage
            const cachedProducts = sessionStorage.getItem('vendorProducts');
            if (cachedProducts) {
              const parsedProducts = JSON.parse(cachedProducts);
              if (Array.isArray(parsedProducts) && parsedProducts.length > 0) {
                setVendorProducts(parsedProducts);
                return;
              }
            }

            // If no products found, set empty array
            setVendorProducts([{
              id: 'uncategorized',
              name: store.businessType === 'service' ? 'All Services' : 'All Products',
              products: []
            }]);
          }
        } catch (error) {
          // Try to get from session storage
          try {
            const cachedProducts = sessionStorage.getItem('vendorProducts');
            if (cachedProducts) {
              const parsedProducts = JSON.parse(cachedProducts);
              if (Array.isArray(parsedProducts) && parsedProducts.length > 0) {
                setVendorProducts(parsedProducts);
                return;
              }
            }
          } catch (storageError) {
            // Ignore storage errors
          }

          // If all else fails, set empty array
          setVendorProducts([{
            id: 'uncategorized',
            name: store.businessType === 'service' ? 'All Services' : 'All Products',
            products: []
          }]);
        }
      } else {
        // No store ID available
        setVendorProducts([{
          id: 'uncategorized',
          name: store.businessType === 'service' ? 'All Services' : 'All Products',
          products: []
        }]);
      }
    };

    fetchVendorProducts();
  }, [store.id]);

  // Empty product catalog data (no default products)
  const productCatalogData = [];

  // Handle product selection
  const handleProductSelect = (product) => {
    // Check if we have a chat flow with a node that handles product selection
    if (chatFlowRef.current && chatFlowRef.current.nodes) {
      // Look for a node that is configured to handle product selection
      const productNode = chatFlowRef.current.nodes.find(node =>
        node.data && node.data.handleProductSelection === true
      );

      if (productNode) {
        const nextMessage = createMessageFromNode(productNode);

        if (nextMessage) {
          // Add product information to the message
          nextMessage.header = store.businessType === 'service' ? 'SERVICE DETAILS' : 'PRODUCT DETAILS';

          // Create product name in uppercase
          const productName = product.name ? product.name.toUpperCase() : (store.businessType === 'service' ? 'SERVICE' : 'PRODUCT');

          // Format price
          const formattedPrice = `₹${parseFloat(product.price).toFixed(2)}`;

          // Create content with product details
          nextMessage.content = `${productName}\n\nPrice: ${formattedPrice}\n\nThis ${store.businessType === 'service' ? 'service' : 'product'} is currently available. Would you like to place an order?`;

          // Use product image if available
          if (product.image || product.imageUrl) {
            const imageSource = product.image || product.imageUrl;

            // Handle image object
            if (typeof imageSource === 'object') {
              // Try to find any property that might contain a data URL
              let foundDataUrl = false;
              for (const key of Object.keys(imageSource)) {
                if (typeof imageSource[key] === 'string' && imageSource[key].startsWith('data:image/')) {
                  nextMessage.image = imageSource[key];
                  foundDataUrl = true;
                  break;
                }
              }

              // If we couldn't find a direct data URL, try to extract from JSON
              if (!foundDataUrl) {
                try {
                  const jsonString = JSON.stringify(imageSource);
                  const dataUrlMatch = jsonString.match(/data:image\/[^"',}\]]+/g);
                  if (dataUrlMatch && dataUrlMatch.length > 0) {
                    nextMessage.image = dataUrlMatch[0];
                  } else {
                    // Use the utility function as a last resort
                    nextMessage.image = ensureValidImageUrl(imageSource);
                  }
                } catch (err) {
                  nextMessage.image = ensureValidImageUrl(imageSource);
                }
              }
            } else if (typeof imageSource === 'string') {
              // Direct string handling
              if (imageSource.startsWith('data:image/')) {
                nextMessage.image = imageSource;
              } else {
                nextMessage.image = ensureValidImageUrl(imageSource);
              }
            } else {
              // Fallback to the utility function
              nextMessage.image = ensureValidImageUrl(imageSource);
            }
          }

          setMessages(prev => [...prev, nextMessage]);
          return;
        }
      } else {
        // If no product node is found in the flow, just add messages

        // Add a user message showing they clicked on a product
        const userMessage = {
          id: Date.now(),
          content: `Selected: ${product.name}`,
          sender: 'visitor',
          timestamp: new Date(),
          status: 'read'
        };

        setMessages(prev => [...prev, userMessage]);

        // Add a message explaining that product details are not configured
        const infoMessage = {
          id: Date.now() + 1,
          content: `${store.businessType === 'service' ? 'Service' : 'Product'} details are not configured in this chat flow. Please add a node with 'handleProductSelection' set to true in your chat flow.`,
          sender: 'store',
          timestamp: new Date(),
          status: 'read'
        };

        setMessages(prev => [...prev, infoMessage]);
        return;
      }
    }
  };

  // Function to create a message from a node
  const createMessageFromNode = (node) => {
    if (!node || !node.data || !node.data.content) return null;

    const message = {
      id: `${node.id}_${Date.now()}`,
      nodeId: node.id,
      content: node.data.content,
      sender: 'store',
      timestamp: new Date(),
      status: 'read',
      handleProductSelection: node.data.handleProductSelection || false,
      storeId: store.id // Pass the store ID to the message
    };

    // Add buttons if present
    if (node.data.buttons && node.data.buttons.length > 0) {
      message.buttons = node.data.buttons.map(button => ({
        type: button.type || 'quick_reply',
        text: button.text,
        url: button.url,
        phoneNumber: button.phoneNumber,
        nextNodeId: button.nextNodeId
      }));
      message.onQuickReply = handleQuickReply;
    } else if (chatFlowRef.current && chatFlowRef.current.edges) {
      // If no buttons are defined but there are outgoing edges, create default buttons
      const outgoingEdges = chatFlowRef.current.edges.filter(edge => edge.source === node.id);

      if (outgoingEdges.length > 0) {
        message.buttons = outgoingEdges.map((edge, index) => {
          // Find the target node to get its label if available
          const targetNode = chatFlowRef.current.nodes.find(n => n.id === edge.target);
          return {
            type: 'quick_reply',
            text: edge.label || targetNode?.data?.buttonLabel || `Option ${index + 1}`,
            nextNodeId: edge.target
          };
        });
        message.onQuickReply = handleQuickReply;
      }
    }

    // Add header if present
    if (node.data.header) {
      message.header = node.data.header;
    }

    // Add image if present
    if (node.data.image) {
      // Handle image object
      if (typeof node.data.image === 'object') {
        // Try to find any property that might contain a data URL
        for (const key of Object.keys(node.data.image)) {
          if (typeof node.data.image[key] === 'string' && node.data.image[key].startsWith('data:image/')) {
            message.image = node.data.image[key];
            break;
          }
        }

        // If we couldn't find a direct data URL, try to extract from JSON
        if (typeof message.image === 'object') {
          try {
            const jsonString = JSON.stringify(node.data.image);
            const dataUrlMatch = jsonString.match(/data:image\/[^"',}\]]+/g);
            if (dataUrlMatch && dataUrlMatch.length > 0) {
              message.image = dataUrlMatch[0];
            } else {
              // Just use the object as is, our enhanced image handling in ChatMessage will try to extract it
              message.image = node.data.image;
            }
          } catch (err) {
            message.image = node.data.image;
          }
        }
      } else {
        // Direct string assignment
        message.image = node.data.image;
      }
    }

    // Check if this is a product catalog node
    if (node.data.type === 'product_catalog' || node.data.showProductCatalog) {
      // Get all products from all categories
      let allProducts = [];
      let allCategories = [];

      // Only use vendor products, no fallback to sample data
      if (vendorProducts && vendorProducts.length > 0) {
        vendorProducts.forEach(category => {
          if (!category) {
            return; // Skip this category
          }

          // Always add the category to the list, even if it has no products
          allCategories.push({
            id: category.id || `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: category.name || 'Uncategorized'
          });

          // Check if category has products
          if (category.products && Array.isArray(category.products)) {
            // Add each product to the all products list with its category
            category.products.forEach(product => {
              if (!product) {
                return; // Skip this product
              }

              const productWithCategory = {
                ...product,
                id: product.id || `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                name: product.name || (store.businessType === 'service' ? 'Unnamed Service' : 'Unnamed Product'),
                price: parseFloat(product.price) || 0,
                category: category.name || 'Uncategorized'
              };

              allProducts.push(productWithCategory);
            });
          }
        });
      } else {
        // Add a default category even when no vendor products are available
        allCategories.push({
          id: 'uncategorized',
          name: store.businessType === 'service' ? 'All Services' : 'All Products'
        });
      }

      // Process products and categories

      // Filter products by category if specified
      let filteredProducts = [...allProducts];

      // If a specific category is selected (not 'all'), filter the products
      if (node.data.categoryFilter && node.data.categoryFilter !== 'all') {
        // Get available categories
        const availableCategories = [...new Set(allProducts.map(p => p.category || 'Uncategorized'))];

        // Try to filter by category
        const categoryMatches = allProducts.filter(product => {
          if (!product || !product.category) return false;

          const productCategory = (product.category || '').toLowerCase();
          const filterCategory = node.data.categoryFilter.toLowerCase();

          // Check for exact or partial match
          return productCategory === filterCategory ||
                 productCategory.includes(filterCategory) ||
                 filterCategory.includes(productCategory);
        });

        // Use filtered products if found, otherwise use all products
        if (categoryMatches.length > 0) {
          filteredProducts = categoryMatches;
        } else {
          filteredProducts = [...allProducts];
        }
      }

      // Create a single category with all filtered products
      const catalogName = node.data.categoryFilter === 'all' ? 'All Products' : node.data.categoryFilter;

      // If no filtered products but we have all products, use all products as fallback
      if (filteredProducts.length === 0 && allProducts.length > 0) {
        filteredProducts = [...allProducts];
      }

      // Always ensure we have at least one category with all products
      const catalogData = [
        {
          id: 'all-products',
          name: catalogName,
          products: filteredProducts.length > 0 ? filteredProducts : []
        }
      ];

      // Use the catalog data for the message

      // Use the catalog data
      message.productCatalog = catalogData;
      message.onProductSelect = handleProductSelect;
      message.isProductCatalog = true;
      message.header = 'Product Catalog';

      // Update footer based on whether products are available
      if (catalogData[0].products.length === 0) {
        message.footer = 'No products available in this store yet';
        message.noProductsMessage = 'Add products in the vendor dashboard to display them here.';

        // Add a message to the catalog data to make it clear there are no products
        catalogData[0].products = [];
        catalogData[0].noProducts = true;
      } else {
        // Check if there's a product details node in the flow
        const hasProductDetailsNode = chatFlowRef.current.nodes.some(node =>
          node.data && node.data.handleProductSelection === true
        );

        if (hasProductDetailsNode) {
          message.footer = 'Select a product to view details';
        } else {
          message.footer = 'Product details not configured in this flow';
        }
      }

      // Add a Home button to the product catalog message
      message.buttons = [
        { type: 'quick_reply', text: 'Home', value: 'home' }
      ];
      message.onQuickReply = handleQuickReply;
    }

    return message;
  };

  // Handle quick reply button click
  const handleQuickReply = (replyText, originalMessage, button) => {
    // Special handling for specific button values
    if (button.value === 'home') {
      // Find the first node in the flow (welcome message)
      const firstNode = chatFlowRef.current.nodes.find(node => node.data && node.data.isFirst);

      if (firstNode) {
        // Add user message
        const userMessage = {
          id: Date.now(),
          content: replyText,
          sender: 'visitor',
          timestamp: new Date(),
          status: 'read',
          replyTo: {
            id: originalMessage.id,
            content: originalMessage.content,
            sender: originalMessage.sender
          }
        };

        // Create welcome message but keep conversation history
        const welcomeMessage = createMessageFromNode(firstNode);
        if (welcomeMessage) {
          // Add user message and welcome message to existing conversation
          setMessages(prev => [...prev, userMessage, welcomeMessage]);

          // Return after adding messages
          return;
        }
      }
    }

    // Handle "Back to Catalog" button
    if (button.value === 'back_to_catalog') {
      // Find the product catalog node
      const catalogNode = chatFlowRef.current.nodes.find(
        node => node.data && (node.data.type === 'product_catalog' || node.data.showProductCatalog)
      );

      if (catalogNode) {
        // Add user message
        const userMessage = {
          id: Date.now(),
          content: replyText,
          sender: 'visitor',
          timestamp: new Date(),
          status: 'read',
          replyTo: {
            id: originalMessage.id,
            content: originalMessage.content,
            sender: originalMessage.sender
          }
        };

        // Find the last catalog message in the history
        const catalogMessageIndex = messages.findIndex(msg =>
          msg.isProductCatalog && msg.nodeId === catalogNode.id
        );

        if (catalogMessageIndex >= 0) {
          // Get messages up to the catalog
          const previousMessages = messages.slice(0, catalogMessageIndex + 1);
          setMessages([...previousMessages, userMessage, messages[catalogMessageIndex]]);
          return;
        } else {
          // Create a new catalog message
          const catalogMessage = createMessageFromNode(catalogNode);
          if (catalogMessage) {
            setMessages([...messages, userMessage, catalogMessage]);
            return;
          }
        }
      }
    }

    // Add user message as a reply to the original message
    const userMessage = {
      id: Date.now(),
      content: replyText,
      sender: 'visitor',
      timestamp: new Date(),
      status: 'sent',
      replyTo: {
        id: originalMessage.id,
        content: originalMessage.content,
        sender: originalMessage.sender
      }
    };

    setMessages(prev => [...prev, userMessage]);

    // Simulate message being delivered
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'delivered' }
            : msg
        )
      );
    }, 500);

    // Simulate message being read
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'read' }
            : msg
        )
      );
    }, 800);

    // Process the next node in the chat flow
    setTimeout(() => {
      // Check if we have a chat flow and a next node ID from the button
      if (chatFlowRef.current && button && button.nextNodeId) {
        const nextNode = chatFlowRef.current.nodes.find(node => node.id === button.nextNodeId);

        if (nextNode) {
          const nextMessage = createMessageFromNode(nextNode);

          if (nextMessage) {
            setMessages(prev => [...prev, nextMessage]);
            return;
          }
        }
      }

      // If no next node found, show a message indicating the flow is incomplete
      const noNodeMessage = {
        id: Date.now() + 1,
        content: "This part of the chat flow hasn't been configured yet. Please complete the chat flow in the Chat Flow Builder.",
        sender: 'store',
        timestamp: new Date(),
        status: 'read',
        replyTo: {
          id: userMessage.id,
          content: userMessage.content,
          sender: userMessage.sender
        }
      };

      setMessages(prev => [...prev, noNodeMessage]);
    }, 1500);
  };

  // Handle sending a new message
  const handleSendMessage = (content) => {
    if (!content.trim()) return;

    // Create user message
    const userMessage = {
      id: Date.now(),
      content: content.trim(),
      sender: 'visitor',
      timestamp: new Date(),
      status: 'sent'
    };

    // Add to messages
    setMessages(prev => [...prev, userMessage]);

    // Simulate message being delivered
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'delivered' }
            : msg
        )
      );
    }, 500);

    // Simulate message being read
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === userMessage.id
            ? { ...msg, status: 'read' }
            : msg
        )
      );
    }, 800);

    // Simulate store response after a delay
    setTimeout(() => {
      // Check if we have a chat flow with a node that handles free text input
      if (chatFlowRef.current && chatFlowRef.current.nodes) {
        // Look for a node that is configured to handle free text input
        const freeTextNode = chatFlowRef.current.nodes.find(node =>
          node.data && node.data.handleFreeText === true
        );

        if (freeTextNode) {
          const nextMessage = createMessageFromNode(freeTextNode);

          if (nextMessage) {
            setMessages(prev => [...prev, nextMessage]);
            return;
          }
        }
      }

      // If no free text node found in the chat flow, show a generic response
      const responseMessage = {
        id: Date.now(),
        content: "I can only respond to button clicks based on the configured chat flow. Please use the buttons to navigate the conversation.",
        sender: 'store',
        timestamp: new Date(),
        status: 'read'
      };

      setMessages(prev => [...prev, responseMessage]);
    }, 1500);
  };

  if (loading) {
    return (
      <div className="store-theme-loading">
        <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style={{ width: '120px', marginBottom: '20px' }} />
        <div className="loading-spinner"></div>
        <div className="loading-text">Loading Store...</div>
        <div className="preloader-tagline">दुकान Online है ❤️</div>
      </div>
    );
  }

  return (
    <div className="store-theme">
      <ChatHeader
        storeName={store.name}
        storeLogoUrl={store.logoUrl}
        isVerified={store.isVerified}
      />
      <ChatBody
        messages={messages}
        messagesEndRef={messagesEndRef}
      />
      <ChatInputBar onSendMessage={handleSendMessage} />
    </div>
  );
};

export default StoreTheme;
