import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

export default function PrivacyPolicy() {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens (same as Home.jsx)
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529'
  };

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      margin: 0,
      padding: 0,
      color: colors.dark,
      backgroundColor: colors.white
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: colors.white,
        color: colors.dark,
        padding: '15px 5%',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: isMobile ? '15px' : '0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/">
            <img
              src="/WhaMart_Logo.png"
              alt="WhaMart Logo"
              style={{
                height: '40px',
                marginRight: '10px'
              }}
            />
          </Link>
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '30px'
        }}>
          {!isMobile && (
            <nav>
              <ul style={{
                display: 'flex',
                listStyle: 'none',
                gap: '30px',
                margin: 0,
                padding: 0
              }}>
                <li><Link to="/#features" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Features</Link></li>
                <li><Link to="/#how-it-works" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>How It Works</Link></li>
                <li><Link to="/#pricing" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Pricing</Link></li>
                <li><Link to="/blog" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Blog</Link></li>
              </ul>
            </nav>
          )}

          <div style={{ display: 'flex', gap: '15px' }}>
            <Link to="/login" style={{
              padding: '10px 20px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              transition: 'all 0.3s ease'
            }}>
              Sign In
            </Link>
            <Link to="/register" style={{
              padding: '10px 20px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Privacy Policy Hero Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '60px 5%',
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '10%',
          right: '5%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <h1 style={{
            fontSize: isMobile ? '32px' : '42px',
            fontWeight: 700,
            marginBottom: '16px',
            color: colors.dark
          }}>
            Privacy <span style={{ color: colors.primary }}>Policy</span>
          </h1>
          <p style={{
            fontSize: '18px',
            maxWidth: '700px',
            margin: '0 auto 32px',
            color: colors.gray,
            lineHeight: 1.6
          }}>
            Last Updated: April 15, 2023
          </p>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section style={{
        padding: '80px 5%',
        backgroundColor: colors.white
      }}>
        <div style={{ 
          maxWidth: '900px', 
          margin: '0 auto',
          lineHeight: 1.7
        }}>
          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginBottom: '24px',
            color: colors.dark
          }}>
            Introduction
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '24px'
          }}>
            At WhaMart, we respect your privacy and are committed to protecting the personal information that you share with us. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our website, mobile application, and services (collectively, the "Service").
          </p>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '24px'
          }}>
            Please read this Privacy Policy carefully. By accessing or using the Service, you acknowledge that you have read, understood, and agree to be bound by all the terms of this Privacy Policy and our Terms of Service.
          </p>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Information We Collect
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '16px'
          }}>
            We may collect several types of information from and about users of our Service, including:
          </p>
          <ul style={{
            paddingLeft: '20px',
            marginBottom: '24px'
          }}>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Personal Data:</strong> Personal information that can be used to identify you such as your name, email address, telephone number, postal address, billing information, and other information you choose to provide.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Business Information:</strong> If you are a vendor or business owner, we collect information about your business such as business name, description, category, contact details, and payment information.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Usage Data:</strong> Information about how you use our Service, including your browsing actions, search queries, and other data collected through cookies and similar technologies.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Device Information:</strong> Information about your device, including IP address, device type, operating system, browser type, and mobile network information.
            </li>
          </ul>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            How We Use Your Information
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '16px'
          }}>
            We may use the information we collect for various purposes, including to:
          </p>
          <ul style={{
            paddingLeft: '20px',
            marginBottom: '24px'
          }}>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Provide, maintain, and improve our Service
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Process transactions and send related information, including confirmations and receipts
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Send administrative information such as changes to our terms, conditions, and policies
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Respond to your comments, questions, and requests
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Send promotional communications, such as special offers or other information that may be of interest to you
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Monitor and analyze trends, usage, and activities in connection with our Service
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Detect, investigate, and prevent fraudulent transactions and other illegal activities
            </li>
          </ul>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Sharing Your Information
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '16px'
          }}>
            We may share your personal information in the following situations:
          </p>
          <ul style={{
            paddingLeft: '20px',
            marginBottom: '24px'
          }}>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>With Service Providers:</strong> We may share your information with third-party vendors, consultants, and other service providers who need access to such information to carry out work on our behalf.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>For Business Transfers:</strong> We may share or transfer your information in connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business to another company.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>With Your Consent:</strong> We may share your information with your consent or at your direction.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Compliance with Laws:</strong> We may disclose your information where required to do so by law or subpoena or if we believe that such action is necessary to comply with the law and the reasonable requests of law enforcement.
            </li>
          </ul>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Your Choices
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '16px'
          }}>
            You have several choices regarding the use of information on our Service:
          </p>
          <ul style={{
            paddingLeft: '20px',
            marginBottom: '24px'
          }}>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Account Information:</strong> You may update, correct, or delete your account information at any time by logging into your account settings.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Marketing Communications:</strong> You can opt out of receiving promotional emails from us by following the instructions in those emails or by contacting us. If you opt out, we may still send you non-promotional emails, such as those about your account or our ongoing business relations.
            </li>
            <li style={{
              fontSize: '16px',
              color: colors.dark,
              marginBottom: '12px'
            }}>
              <strong>Cookies:</strong> Most web browsers are set to accept cookies by default. If you prefer, you can usually choose to set your browser to remove or reject browser cookies. Please note that if you choose to remove or reject cookies, this could affect the availability and functionality of our Service.
            </li>
          </ul>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Data Security
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '24px'
          }}>
            We have implemented measures designed to secure your personal information from accidental loss and from unauthorized access, use, alteration, and disclosure. However, no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
          </p>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Changes to Our Privacy Policy
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '24px'
          }}>
            We may update our Privacy Policy from time to time. If we make material changes to how we treat our users' personal information, we will post the new Privacy Policy on this page and notify you by email to the primary email address specified in your account. The date the Privacy Policy was last revised is identified at the top of the page.
          </p>

          <h2 style={{
            fontSize: '28px',
            fontWeight: 700,
            marginTop: '40px',
            marginBottom: '24px',
            color: colors.dark
          }}>
            Contact Us
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '24px'
          }}>
            If you have any questions about this Privacy Policy, please contact us at:
          </p>
          <p style={{
            fontSize: '16px',
            color: colors.dark,
            marginBottom: '40px'
          }}>
            <strong>Email:</strong> <a href="mailto:<EMAIL>" style={{ color: colors.primary, textDecoration: 'none' }}><EMAIL></a><br />
            <strong>Address:</strong> WhaMart Inc., 123 Tech Street, San Francisco, CA 94107
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer id="footer" style={{
        backgroundColor: colors.dark,
        padding: '80px 5% 40px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '2fr 1fr 1fr 1fr',
          gap: '40px',
          marginBottom: '50px'
        }}>
          {/* Company Info */}
          <div>
            <h3 style={{
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              color: colors.white
            }}>
              Whamart
            </h3>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              marginBottom: '24px',
              fontSize: '15px',
              lineHeight: 1.6
            }}>
              Turn your WhatsApp Business into a powerful e-commerce platform with our innovative catalog and automated response solutions.
            </p>
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px'
            }}>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Product
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#features" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Features
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#how-it-works" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  How It Works
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#pricing" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Company
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/about-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  About Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/blog" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Blog
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/press" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Support
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/help-center" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Help Center
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/contact-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Contact Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/terms-of-service" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Terms of Service
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/privacy-policy" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div style={{
          borderTop: '1px solid rgba(255,255,255,0.1)',
          paddingTop: '30px',
          textAlign: 'center',
          color: 'rgba(255,255,255,0.5)',
          fontSize: '14px'
        }}>
          © {new Date().getFullYear()} Whamart. All rights reserved.
        </div>
      </footer>
    </div>
  );
}