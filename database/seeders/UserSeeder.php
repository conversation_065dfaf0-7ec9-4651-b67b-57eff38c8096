<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin User
        \App\Models\User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'userType' => 'admin',
        ]);

        // Vendor Users
        \App\Models\User::create([
            'name' => 'Vendor One',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'userType' => 'vendor',
        ]);

        \App\Models\User::create([
            'name' => 'Vendor Two',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'userType' => 'vendor',
        ]);

        // Customer User
        \App\Models\User::create([
            'name' => 'Customer User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'userType' => 'user',
        ]);
    }
}
