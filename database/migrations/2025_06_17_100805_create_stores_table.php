<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('businessCategory')->nullable();
            $table->string('businessSubcategory')->nullable();
            $table->enum('businessType', ['product', 'service'])->default('product');
            $table->enum('productType', ['physical', 'digital'])->default('physical')->nullable();
            $table->string('logoUrl')->nullable();
            $table->string('storeUrl')->unique()->nullable();
            $table->boolean('isVerified')->default(false);
            $table->boolean('isActive')->default(true);
            $table->string('theme')->default('default')->nullable();
            $table->string('contactPhone')->nullable();
            $table->string('contactEmail')->nullable();
            $table->text('address')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stores');
    }
};
