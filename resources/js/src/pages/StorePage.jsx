import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import StoreTheme from '../components/store-theme/StoreTheme';
import { storeAPI, chatFlowAPI } from '../services/api';

/**
 * StorePage component
 * Displays a vendor's store as a full-page WhatsApp chat interface
 * Accessed via a unique URL: /store/:storeId
 */
const StorePage = () => {
  const { storeId } = useParams();
  const [storeData, setStoreData] = useState(null);
  const [chatFlow, setChatFlow] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Set body styles to ensure full-page experience
  useEffect(() => {
    // Save original styles
    const originalOverflow = document.body.style.overflow;
    const originalMargin = document.body.style.margin;
    const originalPadding = document.body.style.padding;

    // Apply full-page styles
    document.body.style.overflow = 'hidden';
    document.body.style.margin = '0';
    document.body.style.padding = '0';

    // Restore original styles on unmount
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.margin = originalMargin;
      document.body.style.padding = originalPadding;
    };
  }, []);

  // Fetch store data based on storeId
  useEffect(() => {
    const fetchStoreData = async () => {
      try {
        setLoading(true);

        // Make API call to get store data by URL
        // The storeId parameter is actually the store URL in format "store-name-id"
        const response = await storeAPI.getStoreByUrl(storeId);
        const store = response.data.store;

        // Format store data for StoreTheme component
        // Process the logo URL from the API
        let logoUrl = null;

        if (store.logoUrl) {
          // Always store the original URL from the API
          // The ChatHeader component will handle the URL formatting
          logoUrl = store.logoUrl;
        }

        // Extract numeric ID from the store URL if possible
        let numericId = null;
        if (storeId) {
          const matches = storeId.match(/\d+$/);
          if (matches && matches[0]) {
            numericId = parseInt(matches[0]);
          }
        }

        const formattedStoreData = {
          name: store.name,
          logoUrl: logoUrl,
          isVerified: store.isVerified,
          id: store.id,
          numericId: numericId, // Add the extracted numeric ID
          description: store.description,
          businessCategory: store.businessCategory,
          storeUrl: storeId // Save the original store URL
        };

        setStoreData(formattedStoreData);

        // Update document title with store name
        document.title = `${store.name} - WhaMart Store`;

        // Fetch chat flow for this store
        try {
          const chatFlowResponse = await chatFlowAPI.getDefaultChatFlowByStoreUrl(storeId);

          if (chatFlowResponse.data.chatFlow &&
              chatFlowResponse.data.chatFlow.flowData &&
              chatFlowResponse.data.chatFlow.flowData.nodes &&
              chatFlowResponse.data.chatFlow.flowData.nodes.length > 0) {
            setChatFlow(chatFlowResponse.data.chatFlow);
          } else {
            setChatFlow(null);
          }
        } catch (chatFlowErr) {
          setChatFlow(null);
        }

        setLoading(false);
      } catch (err) {
        setError('Failed to load store data. The store may not exist or there was a server error.');
        setLoading(false);
      }
    };

    if (storeId) {
      fetchStoreData();
    }
  }, [storeId]);

  // Show loading state
  if (loading) {
    return (
      <div className="store-theme-loading">
        <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style={{ width: '120px', marginBottom: '20px' }} />
        <div className="loading-spinner"></div>
        <div className="loading-text">Loading Store...</div>
        <div className="preloader-tagline">दुकान Online है ❤️</div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="store-theme-error">
        <div className="error-content">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style={{ width: '120px', marginBottom: '20px' }} />
          <h2 style={{ color: '#DC3545', marginTop: '20px' }}>Error</h2>
          <p style={{ marginTop: '10px', textAlign: 'center' }}>{error}</p>
          <div className="preloader-tagline" style={{ margin: '15px 0' }}>दुकान Online है ❤️</div>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: '20px',
              padding: '8px 16px',
              backgroundColor: '#25D366',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Show store theme
  return <StoreTheme storeData={storeData} chatFlow={chatFlow} />;
};

export default StorePage;
