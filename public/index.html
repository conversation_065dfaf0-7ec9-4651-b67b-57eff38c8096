<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <title>Whamart - WhatsApp-Style E-commerce Platform</title>
    <style>
      /* Initial preloader styles */
      #initial-preloader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        transition: opacity 0.5s ease-in-out;
      }

      #initial-preloader img {
        width: 120px;
        margin-bottom: 20px;
      }

      #initial-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(37, 211, 102, 0.2);
        border-radius: 50%;
        border-top-color: #25D366;
        animation: initial-spin 1s linear infinite;
        margin: 20px 0;
      }

      #initial-tagline {
        font-size: 18px;
        font-weight: 500;
        color: #075E54;
        text-align: center;
        font-family: 'Poppins', sans-serif;
      }

      @keyframes initial-spin {
        to { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-911ee891.js"></script>
    <link rel="stylesheet" href="/assets/index-5a53f61a.css">
  </head>
  <body>
    <div id="preloader" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: white; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 9999;">
      <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style="width: 120px; margin-bottom: 20px;">
      <div style="width: 40px; height: 40px; border: 3px solid rgba(37, 211, 102, 0.2); border-radius: 50%; border-top-color: #25D366; animation: spin 1s linear infinite; margin: 20px 0;"></div>
      <div style="font-size: 18px; font-weight: 500; color: #075E54; text-align: center; font-family: 'Poppins', sans-serif;">दुकान Online है ❤️</div>
    </div>
    <div id="root"></div>
    
    <style>
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
    <script>
      // Hide preloader when page is loaded
      window.addEventListener('load', function() {
        setTimeout(function() {
          var preloader = document.getElementById('preloader');
          if (preloader) {
            preloader.style.opacity = '0';
            preloader.style.transition = 'opacity 0.5s ease';
            setTimeout(function() {
              preloader.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
