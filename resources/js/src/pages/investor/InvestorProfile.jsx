import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  BuildingOfficeIcon,
  IdentificationIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  CameraIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorProfile() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    designation: '',
    investorType: '',
    investmentFocus: '',
    bio: '',
    address: '',
    panNumber: '',
    bankName: '',
    accountNumber: '',
    ifscCode: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [profileImage, setProfileImage] = useState(null);
  const [profileImagePreview, setProfileImagePreview] = useState(null);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          name: currentUser?.name || 'Investor Name',
          email: currentUser?.email || '<EMAIL>',
          phone: '+91 **********',
          company: 'Investor Ventures',
          designation: 'Managing Partner',
          investorType: 'Angel Investor',
          investmentFocus: 'Early Stage Tech Startups',
          bio: 'Experienced angel investor with a portfolio of 15+ successful startups. Focused on early-stage technology companies with strong growth potential.',
          profileImage: 'https://randomuser.me/api/portraits/men/5.jpg',
          address: '123 Investor Street, Mumbai, Maharashtra 400001',
          panNumber: '**********',
          bankDetails: {
            bankName: 'HDFC Bank',
            accountNumber: 'XXXX XXXX XXXX 1234',
            ifscCode: 'HDFC0001234'
          },
          investmentDetails: {
            initialInvestment: 300000,
            investmentDate: '2023-01-01',
            equityPercentage: 5
          }
        };
        
        setProfileData(mockData);
        setFormData({
          name: mockData.name,
          email: mockData.email,
          phone: mockData.phone,
          company: mockData.company,
          designation: mockData.designation,
          investorType: mockData.investorType,
          investmentFocus: mockData.investmentFocus,
          bio: mockData.bio,
          address: mockData.address,
          panNumber: mockData.panNumber,
          bankName: mockData.bankDetails.bankName,
          accountNumber: mockData.bankDetails.accountNumber,
          ifscCode: mockData.bankDetails.ifscCode
        });
        setProfileImagePreview(mockData.profileImage);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setLoading(false);
      }
    };
    
    fetchProfileData();
  }, [currentUser]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle profile image change
  const handleImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setProfileImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // In a real implementation, this would send the data to an API
    console.log('Form submitted:', formData);
    console.log('Profile image:', profileImage);
    
    // Update profile data
    setProfileData({
      ...profileData,
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      company: formData.company,
      designation: formData.designation,
      investorType: formData.investorType,
      investmentFocus: formData.investmentFocus,
      bio: formData.bio,
      address: formData.address,
      panNumber: formData.panNumber,
      bankDetails: {
        bankName: formData.bankName,
        accountNumber: formData.accountNumber,
        ifscCode: formData.ifscCode
      },
      profileImage: profileImagePreview
    });
    
    setIsEditing(false);
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Investor Profile</div>
          <h1 className="welcome-title">
            Your <span style={{ color: '#25D366' }}>Profile</span>
          </h1>
          <p className="welcome-subtitle">
            Manage your personal information and investment details.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading profile data...</p>
          </div>
        ) : (
          <>
            <div className="flex justify-end mb-6">
              {!isEditing ? (
                <button
                  className="btn btn-primary"
                  onClick={() => setIsEditing(true)}
                >
                  <PencilIcon className="h-5 w-5 mr-2" />
                  Edit Profile
                </button>
              ) : (
                <div className="flex space-x-3">
                  <button
                    className="btn btn-outline"
                    onClick={() => {
                      setIsEditing(false);
                      setFormData({
                        name: profileData.name,
                        email: profileData.email,
                        phone: profileData.phone,
                        company: profileData.company,
                        designation: profileData.designation,
                        investorType: profileData.investorType,
                        investmentFocus: profileData.investmentFocus,
                        bio: profileData.bio,
                        address: profileData.address,
                        panNumber: profileData.panNumber,
                        bankName: profileData.bankDetails.bankName,
                        accountNumber: profileData.bankDetails.accountNumber,
                        ifscCode: profileData.bankDetails.ifscCode
                      });
                      setProfileImagePreview(profileData.profileImage);
                      setProfileImage(null);
                    }}
                  >
                    <XMarkIcon className="h-5 w-5 mr-2" />
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleSubmit}
                  >
                    <CheckIcon className="h-5 w-5 mr-2" />
                    Save Changes
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Profile Image and Basic Info */}
              <div className="card" style={{ padding: '24px' }}>
                <div className="flex flex-col items-center">
                  <div className="relative mb-4">
                    <div className="w-32 h-32 rounded-full overflow-hidden">
                      <img 
                        src={profileImagePreview || profileData.profileImage} 
                        alt={profileData.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {isEditing && (
                      <div className="absolute bottom-0 right-0">
                        <label htmlFor="profile-image" className="cursor-pointer">
                          <div className="bg-green-500 text-white rounded-full p-2 shadow-md">
                            <CameraIcon className="h-5 w-5" />
                          </div>
                          <input 
                            type="file" 
                            id="profile-image" 
                            className="hidden" 
                            accept="image/*"
                            onChange={handleImageChange}
                          />
                        </label>
                      </div>
                    )}
                  </div>
                  
                  <h2 className="text-xl font-semibold text-gray-900 mb-1">{profileData.name}</h2>
                  <p className="text-sm text-gray-500 mb-4">{profileData.investorType}</p>
                  
                  <div className="w-full space-y-3">
                    <div className="flex items-start">
                      <EnvelopeIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{profileData.email}</p>
                        <p className="text-xs text-gray-500">Email</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <PhoneIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{profileData.phone}</p>
                        <p className="text-xs text-gray-500">Phone</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{profileData.company}</p>
                        <p className="text-xs text-gray-500">Company</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <IdentificationIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{profileData.designation}</p>
                        <p className="text-xs text-gray-500">Designation</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Profile Details */}
              <div className="md:col-span-2">
                {isEditing ? (
                  <div className="card" style={{ padding: '24px' }}>
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Edit Profile</h2>
                    <form onSubmit={handleSubmit}>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border ${formErrors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500`}
                            required
                          />
                          {formErrors.name && (
                            <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Email *
                          </label>
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border ${formErrors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500`}
                            required
                          />
                          {formErrors.email && (
                            <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Phone *
                          </label>
                          <input
                            type="text"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border ${formErrors.phone ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500`}
                            required
                          />
                          {formErrors.phone && (
                            <p className="mt-1 text-sm text-red-500">{formErrors.phone}</p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Company
                          </label>
                          <input
                            type="text"
                            name="company"
                            value={formData.company}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Designation
                          </label>
                          <input
                            type="text"
                            name="designation"
                            value={formData.designation}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Investor Type
                          </label>
                          <select
                            name="investorType"
                            value={formData.investorType}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          >
                            <option value="Angel Investor">Angel Investor</option>
                            <option value="Venture Capitalist">Venture Capitalist</option>
                            <option value="Private Equity">Private Equity</option>
                            <option value="Corporate Investor">Corporate Investor</option>
                            <option value="Family Office">Family Office</option>
                            <option value="Individual Investor">Individual Investor</option>
                          </select>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Investment Focus
                          </label>
                          <input
                            type="text"
                            name="investmentFocus"
                            value={formData.investmentFocus}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Bio
                          </label>
                          <textarea
                            name="bio"
                            value={formData.bio}
                            onChange={handleInputChange}
                            rows={4}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          ></textarea>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Address
                          </label>
                          <textarea
                            name="address"
                            value={formData.address}
                            onChange={handleInputChange}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          ></textarea>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            PAN Number
                          </label>
                          <input
                            type="text"
                            name="panNumber"
                            value={formData.panNumber}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                      </div>

                      <h3 className="text-lg font-medium text-gray-900 mt-6 mb-4">Bank Details</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Bank Name
                          </label>
                          <input
                            type="text"
                            name="bankName"
                            value={formData.bankName}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Account Number
                          </label>
                          <input
                            type="text"
                            name="accountNumber"
                            value={formData.accountNumber}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            IFSC Code
                          </label>
                          <input
                            type="text"
                            name="ifscCode"
                            value={formData.ifscCode}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                      </div>
                    </form>
                  </div>
                ) : (
                  <>
                    <div className="card mb-6" style={{ padding: '24px' }}>
                      <h2 className="text-xl font-semibold text-gray-900 mb-4">Profile Information</h2>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Investment Focus</h3>
                          <p className="text-base text-gray-900">{profileData.investmentFocus}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Bio</h3>
                          <p className="text-base text-gray-900">{profileData.bio}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Address</h3>
                          <p className="text-base text-gray-900">{profileData.address}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">PAN Number</h3>
                          <p className="text-base text-gray-900">{profileData.panNumber}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="card" style={{ padding: '24px' }}>
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Bank Details</h2>
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Bank Name</h3>
                            <p className="text-base text-gray-900">{profileData.bankDetails.bankName}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Account Number</h3>
                            <p className="text-base text-gray-900">{profileData.bankDetails.accountNumber}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">IFSC Code</h3>
                            <p className="text-base text-gray-900">{profileData.bankDetails.ifscCode}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="card" style={{ padding: '24px' }}>
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">Investment Details</h2>
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Initial Investment</h3>
                            <p className="text-base text-gray-900">{formatCurrency(profileData.investmentDetails.initialInvestment)}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Investment Date</h3>
                            <p className="text-base text-gray-900">{formatDate(profileData.investmentDetails.investmentDate)}</p>
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Equity Percentage</h3>
                            <p className="text-base text-gray-900">{profileData.investmentDetails.equityPercentage}%</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
