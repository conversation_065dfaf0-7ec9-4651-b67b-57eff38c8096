import React, { useState, useEffect } from 'react';
import { Card, CardContent, Typography, Grid, Box, Paper, Tabs, Tab, Button, IconButton } from '@mui/material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import { ChevronLeftIcon, ChevronRightIcon, PlusCircleIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, FormControl, InputLabel, Select, MenuItem } from '@mui/material';

const AdminInvestment = () => {
  // Sample data - replace with actual API calls later
  const [investmentStats, setInvestmentStats] = useState({
    totalInvestors: 57,
    activeInvestors: 42,
    totalCapitalRaised: 2500000,
    currentValuation: 10000000,
    equityDiluted: 25,
    remainingEquity: 75,
    avgInvestment: 43859.65
  });

  const [capitalData, setCapitalData] = useState([
    { name: 'Seed Round', value: 500000, date: '2022-01-15' },
    { name: 'Angel Round', value: 750000, date: '2022-06-20' },
    { name: 'Series A', value: 1250000, date: '2023-03-10' },
  ]);

  const [investorData, setInvestorData] = useState([
    { id: 1, name: 'John Smith', amount: 200000, equity: 5, date: '2022-01-15', type: 'Angel Investor', status: 'Active' },
    { id: 2, name: 'Acme Ventures', amount: 500000, equity: 10, date: '2022-06-20', type: 'Venture Capital', status: 'Active' },
    { id: 3, name: 'Tech Growth Fund', amount: 750000, equity: 7.5, date: '2023-03-10', type: 'Private Equity', status: 'Active' },
    { id: 4, name: 'Sarah Johnson', amount: 50000, equity: 1, date: '2022-02-28', type: 'Angel Investor', status: 'Active' },
    { id: 5, name: 'Future Investments LLC', amount: 100000, equity: 1.5, date: '2022-07-15', type: 'Institutional', status: 'Inactive' }
  ]);

  const [managementTeam, setManagementTeam] = useState([
    { id: 1, name: 'Jane Doe', position: 'CEO', equity: 15, joined: '2021-05-01', bio: 'Experienced executive with 15 years in tech' },
    { id: 2, name: 'Michael Chen', position: 'CTO', equity: 12, joined: '2021-05-01', bio: 'Former lead engineer at Google and Amazon' },
    { id: 3, name: 'Priya Sharma', position: 'COO', equity: 10, joined: '2021-07-15', bio: 'Operations expert with MBA from Stanford' },
    { id: 4, name: 'David Wilson', position: 'CFO', equity: 8, joined: '2022-01-10', bio: 'Financial specialist with CPA certification' }
  ]);

  const [tabValue, setTabValue] = useState(0);
  const [investorDialogOpen, setInvestorDialogOpen] = useState(false);
  const [teamDialogOpen, setTeamDialogOpen] = useState(false);
  const [currentInvestor, setCurrentInvestor] = useState(null);
  const [currentTeamMember, setCurrentTeamMember] = useState(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState({ type: '', id: null });

  // Monthly investment data for chart
  const monthlyInvestmentData = [
    { month: 'Jan', amount: 150000 },
    { month: 'Feb', amount: 50000 },
    { month: 'Mar', amount: 300000 },
    { month: 'Apr', amount: 200000 },
    { month: 'May', amount: 100000 },
    { month: 'Jun', amount: 450000 },
    { month: 'Jul', amount: 375000 },
    { month: 'Aug', amount: 225000 },
    { month: 'Sep', amount: 550000 },
    { month: 'Oct', amount: 100000 },
    { month: 'Nov', amount: 0 },
    { month: 'Dec', amount: 0 },
  ];

  // Distribution of investor types
  const investorTypeData = [
    { name: 'Angel Investors', value: 25 },
    { name: 'Venture Capital', value: 40 },
    { name: 'Private Equity', value: 30 },
    { name: 'Institutional', value: 5 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleOpenInvestorDialog = (investor = null) => {
    setCurrentInvestor(investor);
    setInvestorDialogOpen(true);
  };

  const handleOpenTeamDialog = (member = null) => {
    setCurrentTeamMember(member);
    setTeamDialogOpen(true);
  };

  const handleCloseInvestorDialog = () => {
    setInvestorDialogOpen(false);
    setCurrentInvestor(null);
  };

  const handleCloseTeamDialog = () => {
    setTeamDialogOpen(false);
    setCurrentTeamMember(null);
  };

  const handleSaveInvestor = (event) => {
    event.preventDefault();
    // Save logic would go here
    handleCloseInvestorDialog();
  };

  const handleSaveTeamMember = (event) => {
    event.preventDefault();
    // Save logic would go here
    handleCloseTeamDialog();
  };

  const handleDeleteConfirm = (type, id) => {
    setItemToDelete({ type, id });
    setDeleteConfirmOpen(true);
  };

  const handleDelete = () => {
    if (itemToDelete.type === 'investor') {
      // Delete investor logic
      setInvestorData(investorData.filter(investor => investor.id !== itemToDelete.id));
    } else if (itemToDelete.type === 'team') {
      // Delete team member logic
      setManagementTeam(managementTeam.filter(member => member.id !== itemToDelete.id));
    }
    setDeleteConfirmOpen(false);
  };

  return (
    <div className="container mx-auto p-4">
      <Typography variant="h4" component="h1" gutterBottom className="mb-6">
        Investment Dashboard
      </Typography>

      <Grid container spacing={4}>
        {/* Overview Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card className="h-full">
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Investors
              </Typography>
              <Typography variant="h4" component="div">
                {investmentStats.totalInvestors}
              </Typography>
              <Typography color="textSecondary" className="mt-2">
                {investmentStats.activeInvestors} Active
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card className="h-full">
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Capital Raised
              </Typography>
              <Typography variant="h4" component="div">
                ${(investmentStats.totalCapitalRaised).toLocaleString()}
              </Typography>
              <Typography color="textSecondary" className="mt-2">
                Avg: ${Math.round(investmentStats.avgInvestment).toLocaleString()} per investor
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card className="h-full">
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Current Valuation
              </Typography>
              <Typography variant="h4" component="div">
                ${(investmentStats.currentValuation).toLocaleString()}
              </Typography>
              <Typography color="textSecondary" className="mt-2">
                Based on latest funding round
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card className="h-full">
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Equity Diluted
              </Typography>
              <Typography variant="h4" component="div">
                {investmentStats.equityDiluted}%
              </Typography>
              <Typography color="textSecondary" className="mt-2">
                {investmentStats.remainingEquity}% Remaining
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Charts Section */}
        <Grid item xs={12} md={6}>
          <Card className="h-full">
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Capital Raised Over Time
              </Typography>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyInvestmentData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']} />
                    <Legend />
                    <Line type="monotone" dataKey="amount" stroke="#8884d8" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card className="h-full">
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                Investor Types Distribution
              </Typography>
              <div style={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={investorTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {investorTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Tabs Section */}
        <Grid item xs={12}>
          <Paper sx={{ width: '100%' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="Investors" />
              <Tab label="Management Team" />
              <Tab label="Funding Rounds" />
            </Tabs>
            
            {/* Investors Tab */}
            {tabValue === 0 && (
              <Box p={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Investors List</Typography>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    startIcon={<PlusCircleIcon style={{ width: 20 }} />}
                    onClick={() => handleOpenInvestorDialog()}
                  >
                    Add Investor
                  </Button>
                </Box>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead>
                      <tr>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Equity
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {investorData.map((investor) => (
                        <tr key={investor.id}>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {investor.name}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {investor.type}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            ${investor.amount.toLocaleString()}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {investor.equity}%
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {new Date(investor.date).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${investor.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {investor.status}
                            </span>
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            <div className="flex space-x-2">
                              <IconButton size="small" onClick={() => handleOpenInvestorDialog(investor)}>
                                <PencilIcon style={{ width: 16 }} />
                              </IconButton>
                              <IconButton size="small" onClick={() => handleDeleteConfirm('investor', investor.id)}>
                                <TrashIcon style={{ width: 16 }} />
                              </IconButton>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Box>
            )}
            
            {/* Management Team Tab */}
            {tabValue === 1 && (
              <Box p={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Management Team</Typography>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    startIcon={<PlusCircleIcon style={{ width: 20 }} />}
                    onClick={() => handleOpenTeamDialog()}
                  >
                    Add Team Member
                  </Button>
                </Box>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead>
                      <tr>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Equity
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Joined
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Bio
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {managementTeam.map((member) => (
                        <tr key={member.id}>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {member.name}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {member.position}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {member.equity}%
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {new Date(member.joined).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200 max-w-xs truncate">
                            {member.bio}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            <div className="flex space-x-2">
                              <IconButton size="small" onClick={() => handleOpenTeamDialog(member)}>
                                <PencilIcon style={{ width: 16 }} />
                              </IconButton>
                              <IconButton size="small" onClick={() => handleDeleteConfirm('team', member.id)}>
                                <TrashIcon style={{ width: 16 }} />
                              </IconButton>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Box>
            )}
            
            {/* Funding Rounds Tab */}
            {tabValue === 2 && (
              <Box p={3}>
                <Typography variant="h6" gutterBottom>
                  Funding Rounds
                </Typography>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead>
                      <tr>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Round
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {capitalData.map((round, index) => (
                        <tr key={index}>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {round.name}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            ${round.value.toLocaleString()}
                          </td>
                          <td className="py-3 px-4 border-b border-gray-200">
                            {new Date(round.date).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <div style={{ height: 300, marginTop: 20 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={capitalData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']} />
                      <Legend />
                      <Bar dataKey="value" name="Amount" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
      
      {/* Investor Dialog */}
      <Dialog open={investorDialogOpen} onClose={handleCloseInvestorDialog} maxWidth="sm" fullWidth>
        <form onSubmit={handleSaveInvestor}>
          <DialogTitle>
            {currentInvestor ? 'Edit Investor' : 'Add New Investor'}
          </DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              id="name"
              label="Investor Name"
              type="text"
              fullWidth
              variant="outlined"
              defaultValue={currentInvestor?.name || ''}
              required
              className="mb-4 mt-2"
            />
            <FormControl fullWidth variant="outlined" className="mb-4">
              <InputLabel id="type-label">Investor Type</InputLabel>
              <Select
                labelId="type-label"
                id="type"
                label="Investor Type"
                defaultValue={currentInvestor?.type || 'Angel Investor'}
                required
              >
                <MenuItem value="Angel Investor">Angel Investor</MenuItem>
                <MenuItem value="Venture Capital">Venture Capital</MenuItem>
                <MenuItem value="Private Equity">Private Equity</MenuItem>
                <MenuItem value="Institutional">Institutional</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="dense"
              id="amount"
              label="Investment Amount"
              type="number"
              fullWidth
              variant="outlined"
              defaultValue={currentInvestor?.amount || ''}
              required
              className="mb-4"
            />
            <TextField
              margin="dense"
              id="equity"
              label="Equity Percentage"
              type="number"
              fullWidth
              variant="outlined"
              defaultValue={currentInvestor?.equity || ''}
              required
              inputProps={{ step: 0.1, min: 0, max: 100 }}
              className="mb-4"
            />
            <TextField
              margin="dense"
              id="date"
              label="Investment Date"
              type="date"
              fullWidth
              variant="outlined"
              defaultValue={currentInvestor?.date || new Date().toISOString().split('T')[0]}
              required
              className="mb-4"
              InputLabelProps={{ shrink: true }}
            />
            <FormControl fullWidth variant="outlined">
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                id="status"
                label="Status"
                defaultValue={currentInvestor?.status || 'Active'}
                required
              >
                <MenuItem value="Active">Active</MenuItem>
                <MenuItem value="Inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseInvestorDialog}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">Save</Button>
          </DialogActions>
        </form>
      </Dialog>
      
      {/* Team Member Dialog */}
      <Dialog open={teamDialogOpen} onClose={handleCloseTeamDialog} maxWidth="sm" fullWidth>
        <form onSubmit={handleSaveTeamMember}>
          <DialogTitle>
            {currentTeamMember ? 'Edit Team Member' : 'Add New Team Member'}
          </DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              id="name"
              label="Name"
              type="text"
              fullWidth
              variant="outlined"
              defaultValue={currentTeamMember?.name || ''}
              required
              className="mb-4 mt-2"
            />
            <TextField
              margin="dense"
              id="position"
              label="Position"
              type="text"
              fullWidth
              variant="outlined"
              defaultValue={currentTeamMember?.position || ''}
              required
              className="mb-4"
            />
            <TextField
              margin="dense"
              id="equity"
              label="Equity Percentage"
              type="number"
              fullWidth
              variant="outlined"
              defaultValue={currentTeamMember?.equity || ''}
              required
              inputProps={{ step: 0.1, min: 0, max: 100 }}
              className="mb-4"
            />
            <TextField
              margin="dense"
              id="joined"
              label="Joined Date"
              type="date"
              fullWidth
              variant="outlined"
              defaultValue={currentTeamMember?.joined || new Date().toISOString().split('T')[0]}
              required
              className="mb-4"
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              margin="dense"
              id="bio"
              label="Bio"
              multiline
              rows={4}
              fullWidth
              variant="outlined"
              defaultValue={currentTeamMember?.bio || ''}
              className="mb-4"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseTeamDialog}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">Save</Button>
          </DialogActions>
        </form>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this {itemToDelete.type}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="secondary" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default AdminInvestment;