import {
  ShoppingBagIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

export default function Overview() {
  // Mock data for the dashboard
  const stats = [
    {
      name: 'Total Products',
      stat: '48',
      icon: ShoppingBagIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Products in your catalog'
    },
    {
      name: 'Active Chats',
      stat: '24',
      icon: ChatBubbleLeftRightIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Ongoing customer conversations'
    },
    {
      name: 'Total Revenue',
      stat: '$2,430',
      icon: CurrencyDollarIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Monthly earnings'
    },
    {
      name: 'Total Customers',
      stat: '156',
      icon: UserGroupIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Unique customers'
    },
  ];

  return (
    <div className="dashboard-section">
      <div className="dashboard-section-header">
        <h2 className="dashboard-subheading">Store Performance</h2>
      </div>
      <div className="dashboard-stats-grid">
        {stats.map((item) => (
          <div
            key={item.name}
            className="dashboard-stat-card"
          >
            <div className="dashboard-stat-icon" style={{ backgroundColor: item.color }}>
              <item.icon style={{ color: item.iconColor, width: '28px', height: '28px' }} aria-hidden="true" />
            </div>
            <h4 className="dashboard-stat-label">{item.name}</h4>
            <p className="dashboard-stat-value">{item.stat}</p>
            <p className="dashboard-text mt-2 text-sm">{item.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
