import React, { useState, useEffect } from 'react';
import { CheckIcon, ArrowTopRightOnSquareIcon, PhoneIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import ProductCatalogTemplate from './ProductCatalogTemplate';
import { ensureValidImageUrl } from '../../utils/imageUtils';

/**
 * ChatMessage component
 * Displays a single message in the chat
 * Supports template messages with CTA buttons
 */
const ChatMessage = ({ message }) => {
  const { content, sender, timestamp, status, buttons } = message;
  const isIncoming = sender === 'store';

  // Format time as HH:MM AM/PM
  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Render message status icon (for outgoing messages)
  const renderStatusIcon = () => {
    if (isIncoming) return null;

    switch (status) {
      case 'sent':
        return <CheckIcon className="w-3 h-3 status-icon" />;
      case 'delivered':
        return (
          <div className="double-check">
            <CheckIcon className="w-3 h-3 status-icon first-check" />
            <CheckIcon className="w-3 h-3 status-icon second-check" />
          </div>
        );
      case 'read':
        return (
          <div className="double-check read">
            <CheckIcon className="w-3 h-3 status-icon first-check" />
            <CheckIcon className="w-3 h-3 status-icon second-check" />
          </div>
        );
      default:
        return null;
    }
  };

  // Get button icon based on type
  const getButtonIcon = (type) => {
    switch (type) {
      case 'quick_reply':
        return <ArrowPathIcon className="w-4 h-4 mr-2" />;
      case 'url':
        return <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />;
      case 'call':
        return <PhoneIcon className="w-4 h-4 mr-2" />;
      default:
        return null;
    }
  };

  // Handle button click
  const handleButtonClick = (button) => {
    switch (button.type) {
      case 'quick_reply':
        // Get the parent component's sendMessage function from props
        if (message.onQuickReply) {
          // Pass the button object as the third parameter
          message.onQuickReply(button.text, message, button);
        }
        break;
      case 'url':
        // Open URL in new tab
        window.open(button.url || 'https://example.com', '_blank');
        break;
      case 'call':
        // Open phone dialer
        window.open(`tel:${button.phoneNumber || '+911234567890'}`);
        break;
      default:
        break;
    }
  };

  // Render CTA buttons if they exist
  const renderButtons = () => {
    if (!buttons || buttons.length === 0) return null;

    return (
      <>
        {buttons.map((button, index) => (
          <React.Fragment key={index}>
            <div className="message-divider" style={{ height: '0.5px', backgroundColor: 'rgba(0, 0, 0, 0.08)' }}></div>
            <div className="message-button" onClick={() => handleButtonClick(button)} style={{ textAlign: 'center' }}>
              <div className="button-content" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {getButtonIcon(button.type)}
                <span>{button.text}</span>
              </div>
            </div>
          </React.Fragment>
        ))}
      </>
    );
  };

  // Handle product selection
  const handleProductSelect = (product) => {
    if (message.onProductSelect) {
      message.onProductSelect(product);
    }
  };

  // State to track screen width
  const [bubbleWidth, setBubbleWidth] = useState(window.innerWidth <= 767 ? '85%' : '75%');

  // Update bubble width when window is resized
  useEffect(() => {
    const handleResize = () => {
      setBubbleWidth(window.innerWidth <= 767 ? '85%' : '75%');
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`chat-message ${isIncoming ? 'incoming' : 'outgoing'}`}>
      <div className={`message-bubble ${buttons && buttons.length > 0 ? 'with-buttons' : ''}`} style={{ width: bubbleWidth }}>
        {/* Reply quote */}
        {message.replyTo && (
          <div className="message-reply-container">
            <div className="message-reply">
              <div className="reply-sender">{message.replyTo.sender === 'store' ? 'Store' : 'You'}</div>
              <div className="reply-content">
                {message.replyTo.content.length > 60
                  ? `${message.replyTo.content.substring(0, 60)}...`
                  : message.replyTo.content}
              </div>
            </div>
          </div>
        )}

        {message.header && <div className="message-header">{message.header}</div>}
        {message.image && (
          <div className="message-image">
            <img
              src={(() => {
//                 // Removed console.log

                // Direct string handling
                if (typeof message.image === 'string') {
                  if (message.image.startsWith('data:image/')) {
//                     // Removed console.log
                    return message.image;
                  }
                  return ensureValidImageUrl(message.image);
                }

                // Object handling
                if (typeof message.image === 'object') {
//                   // Removed console.log

                  // Try to find any property that might contain a data URL
                  for (const key of Object.keys(message.image)) {
                    if (typeof message.image[key] === 'string' && message.image[key].startsWith('data:image/')) {
//                       // Removed console.log
                      return message.image[key];
                    }
                  }

                  // Try to extract from JSON string
                  try {
                    const jsonString = JSON.stringify(message.image);
//                     // Removed console.log

                    // Look for data:image pattern in the JSON string
                    const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                    if (dataUrlMatch && dataUrlMatch[1]) {
//                       // Removed console.log
                      return dataUrlMatch[1];
                    }

                    // Try to find any data URL in the string
                    const allDataUrlMatches = jsonString.match(/data:image\/[^"]+/g);
                    if (allDataUrlMatches && allDataUrlMatches.length > 0) {
//                       // Removed console.log
                      return allDataUrlMatches[0];
                    }
                  } catch (err) {
//                     // Removed console.error
                  }
                }

                // Fall back to the utility function
                return ensureValidImageUrl(message.image);
              })()}
              alt="Message attachment"
              className="message-img"
              onError={(e) => {
//                 // Removed console.error
                e.target.onerror = null;

                // Last resort: try to directly extract the image data
                if (typeof message.image === 'object') {
                  try {
                    // Try all possible properties that might contain the image data
                    const props = Object.keys(message.image);
//                     // Removed console.log

                    for (const prop of props) {
                      const value = message.image[prop];
                      if (typeof value === 'string' && value.startsWith('data:image/')) {
//                         // Removed console.log
                        e.target.src = value;
                        return;
                      }
                    }

                    // Try to extract from JSON string with a more flexible approach
                    const jsonString = JSON.stringify(message.image);
                    const dataUrlRegex = /data:image\/[^"',}\]]+/g;
                    const matches = jsonString.match(dataUrlRegex);

                    if (matches && matches.length > 0) {
//                       // Removed console.log
                      e.target.src = matches[0];
                      return;
                    }
                  } catch (err) {
//                     // Removed console.error
                  }
                }

                // Use embedded SVG as fallback instead of external placeholder service
                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4=';
              }}
              onLoad={() => {}}
            />
          </div>
        )}

        {/* Regular content or product catalog */}
        {message.productCatalog ? (
          <ProductCatalogTemplate
            categories={message.productCatalog}
            onSelectProduct={handleProductSelect}
            storeId={message.storeId}
          />
        ) : (
          <div className="message-content">{content}</div>
        )}

        {message.footer && <div className="message-footer">{message.footer}</div>}
        {renderButtons()}
        <div className="message-metadata">
          <span className="message-time">{formatTime(timestamp)}</span>
          <span className="message-status">{renderStatusIcon()}</span>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
