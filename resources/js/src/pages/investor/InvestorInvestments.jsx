import { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  BanknotesIcon,
  DocumentTextIcon,
  CalendarIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  PlusIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorInvestments() {
  const [loading, setLoading] = useState(true);
  const [investmentsData, setInvestmentsData] = useState({
    investments: [],
    totalInvested: 0,
    currentValue: 0,
    totalReturn: 0,
    equityOwned: 0,
    nextMilestones: [],
    investmentOpportunities: []
  });
  const [showAddInvestmentModal, setShowAddInvestmentModal] = useState(false);
  const [formData, setFormData] = useState({
    amount: '',
    date: '',
    notes: ''
  });
  const [formError, setFormError] = useState('');
  const [formSuccess, setFormSuccess] = useState(false);

  // Fetch investments data
  useEffect(() => {
    const fetchInvestmentsData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          investments: [
            {
              id: 1,
              amount: 300000,
              date: '2023-01-01',
              equityPercentage: 5,
              status: 'completed',
              notes: 'Initial investment',
              documents: [
                { id: 1, name: 'Investment Agreement.pdf', url: '#' },
                { id: 2, name: 'Term Sheet.pdf', url: '#' }
              ]
            }
          ],
          totalInvested: 300000,
          currentValue: 345000,
          totalReturn: 15,
          equityOwned: 5,
          nextMilestones: [
            {
              id: 1,
              name: 'Series A Funding Round',
              targetDate: '2024-06-30',
              targetValuation: 15000000,
              description: 'Planned Series A funding round to accelerate growth and expand to 20 more cities.'
            },
            {
              id: 2,
              name: 'Break-even Point',
              targetDate: '2024-12-31',
              description: 'Projected date to reach operational break-even based on current growth trajectory.'
            }
          ],
          investmentOpportunities: [
            {
              id: 1,
              name: 'Growth Capital',
              minAmount: 500000,
              equityOffered: 7.5,
              description: 'Additional investment opportunity to fund market expansion and product development.',
              deadline: '2023-12-31'
            },
            {
              id: 2,
              name: 'Strategic Partnership',
              minAmount: 1000000,
              equityOffered: 15,
              description: 'Strategic investment with management rights and board seat.',
              deadline: '2023-12-31'
            }
          ]
        };

        setInvestmentsData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching investments data:', error);
        setLoading(false);
      }
    };

    fetchInvestmentsData();
  }, []);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.amount || !formData.date) {
      setFormError('Please fill in all required fields');
      return;
    }

    if (isNaN(formData.amount) || parseFloat(formData.amount) <= 0) {
      setFormError('Please enter a valid amount');
      return;
    }

    // In a real implementation, this would send the data to an API
    console.log('Form submitted:', formData);

    // Show success message
    setFormSuccess(true);
    setFormError('');

    // Reset form after 2 seconds
    setTimeout(() => {
      setFormSuccess(false);
      setShowAddInvestmentModal(false);
      setFormData({
        amount: '',
        date: '',
        notes: ''
      });
    }, 2000);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Investment Management</div>
          <h1 className="welcome-title">
            Your <span style={{ color: '#25D366' }}>Investments</span>
          </h1>
          <p className="welcome-subtitle">
            Track your investments, view current valuation, and explore new investment opportunities.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Actions */}
        <div className="flex justify-end mb-6">
          <button
            className="btn btn-primary"
            onClick={() => setShowAddInvestmentModal(true)}
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Investment
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading investments data...</p>
          </div>
        ) : (
          <>
            {/* Investment Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="card" style={{ padding: '24px' }}>
                <h2 className="text-xl font-semibold mb-4">Investment Summary</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Invested</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(investmentsData.totalInvested)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Current Value</p>
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(investmentsData.currentValue)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Total Return</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold text-gray-900 mr-2">{formatPercentage(investmentsData.totalReturn)}</p>
                      <ArrowUpIcon className="h-5 w-5 text-green-500" />
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Equity Owned</p>
                    <p className="text-2xl font-bold text-gray-900">{formatPercentage(investmentsData.equityOwned)}</p>
                  </div>
                </div>
              </div>

              <div className="card" style={{ padding: '24px' }}>
                <h2 className="text-xl font-semibold mb-4">Next Milestones</h2>
                <div className="space-y-4">
                  {investmentsData.nextMilestones.map((milestone) => (
                    <div key={milestone.id} className="border-l-4 border-green-500 pl-4">
                      <h3 className="text-lg font-medium text-gray-900">{milestone.name}</h3>
                      <p className="text-sm text-gray-500 mb-1">
                        <CalendarIcon className="h-4 w-4 inline mr-1" />
                        Target: {formatDate(milestone.targetDate)}
                      </p>
                      {milestone.targetValuation && (
                        <p className="text-sm text-gray-500 mb-1">
                          <BanknotesIcon className="h-4 w-4 inline mr-1" />
                          Target Valuation: {formatCurrency(milestone.targetValuation)}
                        </p>
                      )}
                      <p className="text-sm text-gray-600">{milestone.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Investment History */}
            <div className="mb-8">
              <div className="section-header">
                <h2 className="section-title">Investment History</h2>
                <button className="btn btn-outline btn-sm">
                  <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                  Export
                </button>
              </div>
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Equity %
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Notes
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Documents
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {investmentsData.investments.map((investment) => (
                        <tr key={investment.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(investment.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {formatCurrency(investment.amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatPercentage(investment.equityPercentage)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              {investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {investment.notes}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            <div className="flex flex-col space-y-1">
                              {investment.documents.map((doc) => (
                                <a
                                  key={doc.id}
                                  href={doc.url}
                                  className="text-green-600 hover:text-green-800 flex items-center"
                                >
                                  <DocumentTextIcon className="h-4 w-4 mr-1" />
                                  {doc.name}
                                </a>
                              ))}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Investment Opportunities */}
            <div>
              <div className="section-header">
                <h2 className="section-title">Investment Opportunities</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {investmentsData.investmentOpportunities.map((opportunity) => (
                  <div key={opportunity.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{opportunity.name}</h3>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Minimum Investment</p>
                        <p className="text-lg font-bold text-gray-900">{formatCurrency(opportunity.minAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Equity Offered</p>
                        <p className="text-lg font-bold text-green-600">{formatPercentage(opportunity.equityOffered)}</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{opportunity.description}</p>
                    <div className="flex justify-between items-center">
                      <p className="text-xs text-gray-500">
                        <CalendarIcon className="h-4 w-4 inline mr-1" />
                        Deadline: {formatDate(opportunity.deadline)}
                      </p>
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={() => {
                          setFormData({
                            ...formData,
                            amount: opportunity.minAmount.toString()
                          });
                          setShowAddInvestmentModal(true);
                        }}
                      >
                        Invest Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Add Investment Modal */}
      {showAddInvestmentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium">Add New Investment</h3>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => {
                  setShowAddInvestmentModal(false);
                  setFormError('');
                  setFormSuccess(false);
                }}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4">
              {formSuccess ? (
                <div className="success-message mb-4">
                  <CheckIcon className="h-5 w-5 mr-2" />
                  Investment added successfully!
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  {formError && (
                    <div className="error-message mb-4">
                      {formError}
                    </div>
                  )}

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Investment Amount *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span className="text-gray-500">₹</span>
                        </div>
                        <input
                          type="number"
                          name="amount"
                          value={formData.amount}
                          onChange={handleInputChange}
                          className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="500000"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Investment Date *
                      </label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Any additional notes about this investment..."
                      ></textarea>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      className="btn btn-outline mr-3"
                      onClick={() => {
                        setShowAddInvestmentModal(false);
                        setFormError('');
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      <CheckIcon className="h-5 w-5 mr-2" />
                      Submit Investment
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
