import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { productAPI } from '../../../services/api';

export default function AddProduct() {
  const navigate = useNavigate();

  // Simple function to replace the BusinessTypeContext
  const getTermFor = (term) => {
    return term; // Just return the term as is for now
  };
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Product form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
    sku: '',
    weight: '',
    dimensions: '',
    isFeatured: false,
    discountPrice: '',
  });

  // Image state
  const [productImage, setProductImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // Validate file is an image
        if (!file.type.startsWith('image/')) {
//           // Removed console.error
          setError('Please select a valid image file');
          return;
        }

        setProductImage(file);
        const objectUrl = URL.createObjectURL(file);
        setImagePreview(objectUrl);
//         // Removed console.log
      } catch (error) {
//         // Removed console.error
        setError('Failed to preview image. Please try again.');
      }
    }
  };

  // Remove image
  const handleRemoveImage = () => {
    if (imagePreview) {
      try {
        URL.revokeObjectURL(imagePreview);
//         // Removed console.log
      } catch (error) {
//         // Removed console.error
      }
    }
    setProductImage(null);
    setImagePreview(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Create FormData object for file upload
      const productData = new FormData();

      // Add all form fields to FormData
      Object.keys(formData).forEach(key => {
        if (formData[key] !== '') {
          productData.append(key, formData[key]);
        }
      });

      // Add image if exists
      if (productImage) {
        productData.append('productImage', productImage);
      }

      // Send data to backend
      const response = await productAPI.createProduct(productData);

      // Show success message
      setSuccess(true);

      // Reset form after 2 seconds and redirect
      setTimeout(() => {
        navigate('/vendor/products');
      }, 2000);

    } catch (err) {
//       // Removed console.error
      setError(err.response?.data?.message || 'Failed to create product. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Categories for dropdown
  const categories = [
    'Clothing',
    'Electronics',
    'Accessories',
    'Home & Kitchen',
    'Beauty & Personal Care',
    'Books',
    'Toys & Games',
    'Sports & Outdoors',
    'Food & Beverages',
    'Other'
  ];

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">{getTermFor('Add New Product')}</div>
          <h1 className="welcome-title">
            Create a <span style={{ color: '#25D366' }}>{getTermFor('New Product')}</span>
          </h1>
          <p className="welcome-subtitle">
            {getTermFor('Add a new product')} to your store catalog. Fill in the details below to create your {getTermFor('product')}.
          </p>
        </div>
      </div>

      {/* Back Button */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => navigate('/vendor/products')}
          className="btn btn-outline"
          style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
        >
          <ArrowLeftIcon style={{ width: '16px', height: '16px' }} />
          {getTermFor('Back to Products')}
        </button>
      </div>

      {/* Success Message */}
      {success && (
        <div className="alert alert-success" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(37, 211, 102, 0.1)', color: '#25D366', border: '1px solid #25D366' }}>
          {getTermFor('Product')} created successfully! Redirecting...
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="alert alert-danger" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(220, 53, 69, 0.1)', color: '#dc3545', border: '1px solid #dc3545' }}>
          {error}
        </div>
      )}

      {/* Product Form */}
      <div className="card" style={{ marginBottom: '30px' }}>
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '24px' }}>
            <h2 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '20px' }}>Product Information</h2>

            {/* Basic Info Section */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Product Name */}
              <div className="form-group">
                <label htmlFor="name" className="form-label">Product Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter product name"
                  required
                />
              </div>

              {/* Category */}
              <div className="form-group">
                <label htmlFor="category" className="form-label">Category *</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Price and Stock Section */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Price */}
              <div className="form-group">
                <label htmlFor="price" className="form-label">Price (₹) *</label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              {/* Discount Price */}
              <div className="form-group">
                <label htmlFor="discountPrice" className="form-label">Discount Price (₹)</label>
                <input
                  type="number"
                  id="discountPrice"
                  name="discountPrice"
                  value={formData.discountPrice}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>

              {/* Stock */}
              <div className="form-group">
                <label htmlFor="stock" className="form-label">Stock Quantity *</label>
                <input
                  type="number"
                  id="stock"
                  name="stock"
                  value={formData.stock}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0"
                  min="0"
                  required
                />
              </div>

              {/* SKU */}
              <div className="form-group">
                <label htmlFor="sku" className="form-label">SKU</label>
                <input
                  type="text"
                  id="sku"
                  name="sku"
                  value={formData.sku}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="SKU-12345"
                />
              </div>
            </div>

            {/* Description */}
            <div className="form-group" style={{ marginBottom: '24px' }}>
              <label htmlFor="description" className="form-label">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="form-textarea"
                placeholder="Enter product description"
                rows="4"
              ></textarea>
            </div>

            {/* Additional Details */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Weight */}
              <div className="form-group">
                <label htmlFor="weight" className="form-label">Weight (kg)</label>
                <input
                  type="number"
                  id="weight"
                  name="weight"
                  value={formData.weight}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.0"
                  min="0"
                  step="0.01"
                />
              </div>

              {/* Dimensions */}
              <div className="form-group">
                <label htmlFor="dimensions" className="form-label">Dimensions (LxWxH)</label>
                <input
                  type="text"
                  id="dimensions"
                  name="dimensions"
                  value={formData.dimensions}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="10x10x10 cm"
                />
              </div>

              {/* Featured Product */}
              <div className="form-group" style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  id="isFeatured"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={handleInputChange}
                  style={{ marginRight: '8px' }}
                />
                <label htmlFor="isFeatured" className="form-label" style={{ margin: 0 }}>
                  Featured Product
                </label>
              </div>
            </div>

            {/* Product Image */}
            <div className="form-group">
              <label className="form-label">Product Image</label>
              <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                {imagePreview ? (
                  <div style={{ position: 'relative', marginBottom: '16px' }}>
                    <img
                      src={imagePreview}
                      alt="Product preview"
                      style={{ width: '200px', height: '200px', objectFit: 'cover', borderRadius: '8px' }}
                    />
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      style={{
                        position: 'absolute',
                        top: '-8px',
                        right: '-8px',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px'
                      }}
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <div
                    style={{
                      width: '200px',
                      height: '200px',
                      border: '2px dashed #ddd',
                      borderRadius: '8px',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '16px',
                      cursor: 'pointer'
                    }}
                    onClick={() => document.getElementById('productImage').click()}
                  >
                    <PhotoIcon style={{ width: '48px', height: '48px', color: '#aaa' }} />
                    <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>Click to upload image</p>
                  </div>
                )}
                <input
                  type="file"
                  id="productImage"
                  name="productImage"
                  onChange={handleImageUpload}
                  accept="image/*"
                  style={{ display: 'none' }}
                />
                <button
                  type="button"
                  onClick={() => document.getElementById('productImage').click()}
                  className="btn btn-outline"
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <PhotoIcon style={{ width: '16px', height: '16px' }} />
                  {imagePreview ? 'Change Image' : 'Upload Image'}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <div style={{ marginTop: '24px', display: 'flex', justifyContent: 'flex-end' }}>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
                style={{ minWidth: '120px' }}
              >
                {loading ? 'Creating...' : 'Create Product'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
