.store-theme {
  background-image: url('/chat-background.jpg') !important;
  background-repeat: repeat !important;
  background-size: 300px auto !important;
  position: relative !important;
}

/* Overlay for the chat background */
.store-theme::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(230, 222, 178, 0.15) !important; /* #E6DEB2 with 15% opacity */
  pointer-events: none;
  z-index: 1;
}

/* Keep chat-body styles for compatibility */
.chat-body {
  background-color: transparent !important;
  position: relative !important;
}
