import { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  PencilIcon,
  ClipboardIcon,
  ShareIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerContent() {
  const [loading, setLoading] = useState(false);
  const [contentTemplates, setContentTemplates] = useState([]);
  const [userContent, setUserContent] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isCreatingContent, setIsCreatingContent] = useState(false);
  const [isEditingContent, setIsEditingContent] = useState(false);
  const [selectedContent, setSelectedContent] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: '',
    platform: '',
    notes: ''
  });
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('templates');

  // Fetch content templates and user content
  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockTemplates = [
          {
            id: 1,
            title: 'WhatsApp Store Introduction',
            content: '🛍️ Exciting news! I have partnered with WhaMart to bring you a seamless shopping experience right on WhatsApp! Browse products, place orders, and get support - all in one chat. Check out my store: [Your Referral Link]',
            category: 'announcement',
            platform: 'WhatsApp',
            isTemplate: true
          },
          {
            id: 2,
            title: 'Product Showcase',
            content: '✨ Just discovered these amazing products on WhaMart! Shop directly through WhatsApp with no apps to download. Fast delivery and secure payments. Use my link to get started: [Your Referral Link] #WhaMart #OnlineShopping',
            category: 'product',
            platform: 'Instagram',
            isTemplate: true
          },
          {
            id: 3,
            title: 'Special Offer Announcement',
            content: 'SPECIAL OFFER ALERT! 🔥 My friends at WhaMart are offering exclusive discounts for first-time shoppers! Click my link to browse thousands of products and shop directly through WhatsApp: [Your Referral Link]',
            category: 'promotion',
            platform: 'Facebook',
            isTemplate: true
          },
          {
            id: 4,
            title: 'WhaMart Benefits',
            content: 'Shopping made simple with WhaMart:\n✅ No app downloads needed\n✅ Shop directly in WhatsApp\n✅ Secure payments\n✅ Fast delivery\n✅ 24/7 customer support\n\nTry it now: [Your Referral Link]',
            category: 'informational',
            platform: 'Twitter',
            isTemplate: true
          },
          {
            id: 5,
            title: 'Video Script: WhaMart Tutorial',
            content: 'Hey everyone! Today I am going to show you how to shop on WhatsApp using WhaMart. It is super easy and convenient - no apps to download, just chat and shop! [Show screen recording of browsing a store] Use my link in the description to get started: [Your Referral Link]',
            category: 'tutorial',
            platform: 'YouTube',
            isTemplate: true
          }
        ];

        const mockUserContent = [
          {
            id: 101,
            title: 'My WhaMart Announcement',
            content: 'Excited to share that I have partnered with WhaMart! Now you can shop my recommended products directly through WhatsApp. No more complicated apps or websites - just chat and shop! Check it out here: whamart.com/ref/priya123',
            category: 'announcement',
            platform: 'Instagram',
            createdAt: '2023-10-20',
            lastUsed: '2023-10-21',
            useCount: 3,
            isTemplate: false
          },
          {
            id: 102,
            title: 'Weekly Deals Post',
            content: 'This weeks top deals on WhaMart! 🛍️\n\n1. Premium headphones - 20% off\n2. Smartphone accessories - Buy 1 Get 1\n3. Home decor items - Up to 30% off\n\nShop now through WhatsApp: whamart.com/ref/priya-deals',
            category: 'promotion',
            platform: 'Facebook',
            createdAt: '2023-10-18',
            lastUsed: '2023-10-22',
            useCount: 2,
            isTemplate: false
          }
        ];

        setContentTemplates(mockTemplates);
        setUserContent(mockUserContent);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching content:', error);
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle create content button click
  const handleCreateContent = () => {
    setIsCreatingContent(true);
    setFormData({
      title: '',
      content: '',
      category: '',
      platform: '',
      notes: ''
    });
    setError('');
  };

  // Handle edit content button click
  const handleEditContent = (content) => {
    setIsEditingContent(true);
    setSelectedContent(content);
    setFormData({
      title: content.title,
      content: content.content,
      category: content.category,
      platform: content.platform,
      notes: ''
    });
    setError('');
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.title || !formData.content || !formData.category || !formData.platform) {
      setError('Please fill in all required fields');
      return;
    }

    if (isEditingContent) {
      // Update existing content
      const updatedContent = userContent.map(content =>
        content.id === selectedContent.id
          ? {
              ...content,
              title: formData.title,
              content: formData.content,
              category: formData.category,
              platform: formData.platform
            }
          : content
      );

      setUserContent(updatedContent);
      setIsEditingContent(false);
      setSelectedContent(null);
    } else {
      // Create new content
      const newContent = {
        id: Date.now(),
        title: formData.title,
        content: formData.content,
        category: formData.category,
        platform: formData.platform,
        createdAt: new Date().toISOString().split('T')[0],
        lastUsed: null,
        useCount: 0,
        isTemplate: false
      };

      setUserContent([...userContent, newContent]);
      setIsCreatingContent(false);
    }

    // Reset form
    setFormData({
      title: '',
      content: '',
      category: '',
      platform: '',
      notes: ''
    });
    setError('');
  };

  // Handle cancel button click
  const handleCancel = () => {
    setIsCreatingContent(false);
    setIsEditingContent(false);
    setSelectedContent(null);
    setFormData({
      title: '',
      content: '',
      category: '',
      platform: '',
      notes: ''
    });
    setError('');
  };

  // Handle delete content
  const handleDeleteContent = (id) => {
    if (window.confirm('Are you sure you want to delete this content?')) {
      const updatedContent = userContent.filter(content => content.id !== id);
      setUserContent(updatedContent);
    }
  };

  // Handle copy content to clipboard
  const handleCopyContent = (content) => {
    navigator.clipboard.writeText(content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle use template
  const handleUseTemplate = (template) => {
    setIsCreatingContent(true);
    setFormData({
      title: `My ${template.title}`,
      content: template.content,
      category: template.category,
      platform: template.platform,
      notes: ''
    });
    setError('');
  };

  // Filter content based on search term and category
  const getFilteredContent = (contentList) => {
    return contentList.filter(content => {
      const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           content.content.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filterCategory === 'all' || content.category === filterCategory;

      return matchesSearch && matchesCategory;
    });
  };

  const filteredTemplates = getFilteredContent(contentTemplates);
  const filteredUserContent = getFilteredContent(userContent);

  // Get unique categories for filter
  const categories = ['all', ...new Set([...contentTemplates, ...userContent].map(content => content.category))];

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Content Management</div>
          <h1 className="welcome-title">
            Content <span style={{ color: '#25D366' }}>Sharing</span>
          </h1>
          <p className="welcome-subtitle">
            Create, manage, and share promotional content across your social media platforms.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading content...</p>
          </div>
        ) : (
          <>
            {/* Content Creation/Editing Form */}
            {(isCreatingContent || isEditingContent) && (
              <div className="card" style={{ padding: '24px', marginBottom: '24px' }}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">
                    {isEditingContent ? 'Edit Content' : 'Create New Content'}
                  </h2>
                  <button
                    className="text-gray-500 hover:text-gray-700"
                    onClick={handleCancel}
                  >
                    <XMarkIcon style={{ width: '20px', height: '20px' }} />
                  </button>
                </div>

                {error && (
                  <div className="error-message mb-4">
                    {error}
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Title *
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="e.g. WhatsApp Store Announcement"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Category *
                        </label>
                        <select
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          required
                        >
                          <option value="">Select Category</option>
                          <option value="announcement">Announcement</option>
                          <option value="product">Product</option>
                          <option value="promotion">Promotion</option>
                          <option value="informational">Informational</option>
                          <option value="tutorial">Tutorial</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Platform *
                        </label>
                        <select
                          name="platform"
                          value={formData.platform}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          required
                        >
                          <option value="">Select Platform</option>
                          <option value="WhatsApp">WhatsApp</option>
                          <option value="Instagram">Instagram</option>
                          <option value="Facebook">Facebook</option>
                          <option value="Twitter">Twitter</option>
                          <option value="YouTube">YouTube</option>
                          <option value="LinkedIn">LinkedIn</option>
                          <option value="TikTok">TikTok</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Content *
                      </label>
                      <textarea
                        name="content"
                        value={formData.content}
                        onChange={handleInputChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Enter your content here..."
                        required
                      ></textarea>
                      <p className="text-xs text-gray-500 mt-1">
                        Use [Your Referral Link] as a placeholder for your referral link.
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes (Optional)
                      </label>
                      <textarea
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Add any notes or reminders about this content..."
                      ></textarea>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      className="btn btn-outline mr-3"
                      onClick={handleCancel}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      {isEditingContent ? (
                        <>
                          <CheckIcon style={{ width: '16px', height: '16px' }} />
                          Save Changes
                        </>
                      ) : (
                        <>
                          <PlusIcon style={{ width: '16px', height: '16px' }} />
                          Create Content
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Tabs */}
            <div className="flex border-b mb-6">
              <button
                className={`py-2 px-4 font-medium text-sm ${activeTab === 'templates' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('templates')}
              >
                Content Templates
              </button>
              <button
                className={`py-2 px-4 font-medium text-sm ${activeTab === 'mycontent' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('mycontent')}
              >
                My Content
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
              <div className="flex-1 w-full md:w-auto">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search content..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="flex gap-4 w-full md:w-auto">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>

                {activeTab === 'mycontent' && (
                  <button
                    className="btn btn-primary"
                    onClick={handleCreateContent}
                  >
                    <PlusIcon style={{ width: '16px', height: '16px' }} />
                    Create Content
                  </button>
                )}
              </div>
            </div>

            {/* Content Templates Tab */}
            {activeTab === 'templates' && (
              <>
                {filteredTemplates.length === 0 ? (
                  <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                    <p>No content templates found matching your criteria.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {filteredTemplates.map((template) => (
                      <div key={template.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{template.title}</h3>
                            <div className="flex items-center mt-1">
                              <span className="text-xs font-medium px-2 py-1 rounded-full mr-2" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)', color: '#25D366' }}>
                                {template.category.charAt(0).toUpperCase() + template.category.slice(1)}
                              </span>
                              <span className="text-xs text-gray-500">
                                For {template.platform}
                              </span>
                            </div>
                          </div>

                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => handleUseTemplate(template)}
                          >
                            <PlusIcon style={{ width: '14px', height: '14px' }} />
                            Use Template
                          </button>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md mb-3">
                          <p className="text-gray-700 whitespace-pre-line">{template.content}</p>
                        </div>

                        <div className="flex justify-end">
                          <button
                            className="text-gray-500 hover:text-gray-700 p-2"
                            onClick={() => handleCopyContent(template.content)}
                            title="Copy content"
                          >
                            <ClipboardIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}

            {/* My Content Tab */}
            {activeTab === 'mycontent' && (
              <>
                {filteredUserContent.length === 0 ? (
                  <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                    <p>You haven't created any content yet.</p>
                    <button
                      className="btn btn-primary mt-4"
                      onClick={handleCreateContent}
                    >
                      <PlusIcon style={{ width: '16px', height: '16px' }} />
                      Create Your First Content
                    </button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {filteredUserContent.map((content) => (
                      <div key={content.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{content.title}</h3>
                            <div className="flex items-center mt-1">
                              <span className="text-xs font-medium px-2 py-1 rounded-full mr-2" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)', color: '#25D366' }}>
                                {content.category.charAt(0).toUpperCase() + content.category.slice(1)}
                              </span>
                              <span className="text-xs text-gray-500">
                                For {content.platform}
                              </span>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <button
                              className="p-2 rounded-md hover:bg-gray-100"
                              onClick={() => handleEditContent(content)}
                              title="Edit content"
                            >
                              <PencilIcon style={{ width: '16px', height: '16px', color: '#6C757D' }} />
                            </button>
                            <button
                              className="p-2 rounded-md hover:bg-gray-100"
                              onClick={() => handleDeleteContent(content.id)}
                              title="Delete content"
                            >
                              <TrashIcon style={{ width: '16px', height: '16px', color: '#DC3545' }} />
                            </button>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md mb-3">
                          <p className="text-gray-700 whitespace-pre-line">{content.content}</p>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-500">
                            {content.lastUsed ? (
                              <span>Last used: {content.lastUsed} • Used {content.useCount} times</span>
                            ) : (
                              <span>Created: {content.createdAt} • Never used</span>
                            )}
                          </div>

                          <div className="flex">
                            <button
                              className="text-gray-500 hover:text-gray-700 p-2"
                              onClick={() => handleCopyContent(content.content)}
                              title="Copy content"
                            >
                              <ClipboardIcon style={{ width: '16px', height: '16px' }} />
                            </button>
                            <button
                              className="text-gray-500 hover:text-gray-700 p-2"
                              title="Share content"
                            >
                              <ShareIcon style={{ width: '16px', height: '16px' }} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>

      {/* Copy Success Toast */}
      {copied && (
        <div className="fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
          <CheckIcon style={{ width: '16px', height: '16px', marginRight: '8px' }} />
          Content copied to clipboard!
        </div>
      )}
    </div>
  );
}
