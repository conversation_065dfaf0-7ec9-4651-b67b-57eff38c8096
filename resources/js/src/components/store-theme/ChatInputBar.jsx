import React, { useState } from 'react';
import { PaperClipIcon, PaperAirplaneIcon, MicrophoneIcon, PhotoIcon } from '@heroicons/react/24/outline';
import EmojiIcon from './icons/EmojiIcon';

/**
 * ChatInputBar component
 * Displays the WhatsApp-style input bar for typing and sending messages
 */
const ChatInputBar = ({ onSendMessage }) => {
  const [message, setMessage] = useState('');

  // Handle sending a message
  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '6px 8px',
      backgroundColor: 'transparent',
      position: 'relative',
      zIndex: 2
    }}>
      {/* Input container with attachment and camera buttons */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '6px 10px',
        flex: 1,
        marginRight: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Emoji button (placeholder) */}
        <button style={{
          background: 'none',
          border: 'none',
          color: '#8696A0',
          cursor: 'pointer',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '6px',
          width: '22px',
          height: '22px'
        }}>
          <EmojiIcon width="20" height="20" />
        </button>

        {/* Message input */}
        <input
          type="text"
          style={{
            flex: 1,
            border: 'none',
            outline: 'none',
            backgroundColor: 'transparent',
            fontSize: '14px',
            padding: '0 6px',
            height: '28px'
          }}
          placeholder="Message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
        />

        {/* Attachment button */}
        <button style={{
          background: 'none',
          border: 'none',
          color: '#8696A0',
          cursor: 'pointer',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginLeft: '6px',
          width: '22px',
          height: '22px'
        }}>
          <PaperClipIcon style={{ width: '18px', height: '18px' }} />
        </button>

        {/* Camera button */}
        <button style={{
          background: 'none',
          border: 'none',
          color: '#8696A0',
          cursor: 'pointer',
          padding: '0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginLeft: '6px',
          width: '22px',
          height: '22px'
        }}>
          <PhotoIcon style={{ width: '18px', height: '18px' }} />
        </button>
      </div>

      {/* Send or microphone button */}
      <button
        style={{
          width: '36px',
          height: '36px',
          borderRadius: '50%',
          backgroundColor: message.trim() ? '#00A884' : '#00A884',
          color: 'white',
          border: 'none',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
          flexShrink: 0
        }}
        onClick={message.trim() ? handleSend : undefined}
      >
        {message.trim() ? (
          <PaperAirplaneIcon style={{ width: '18px', height: '18px', transform: 'rotate(90deg)' }} />
        ) : (
          <MicrophoneIcon style={{ width: '18px', height: '18px' }} />
        )}
      </button>
    </div>
  );
};

export default ChatInputBar;
