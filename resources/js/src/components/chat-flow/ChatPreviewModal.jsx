import React, { useState, useEffect, useRef } from 'react';
import { XMarkIcon, PaperAirplaneIcon, PaperClipIcon, MicrophoneIcon } from '@heroicons/react/24/outline';
import { UserCircleIcon } from '@heroicons/react/24/solid';

/**
 * ChatPreviewModal component for the Chat Flow Builder
 * Displays a WhatsApp-style chat interface to preview the flow
 */
const ChatPreviewModal = ({ isOpen, onClose, nodes, edges, storeName = "Your Store" }) => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentNodeId, setCurrentNodeId] = useState(null);
  const messagesEndRef = useRef(null);

  // Find the first node (entry point of the flow)
  useEffect(() => {
    if (isOpen && nodes.length > 0) {
      // Reset messages when modal opens
      setMessages([]);

      // Find the first node (usually marked with isFirst flag)
      const firstNode = nodes.find(node => node.data.isFirst) || nodes[0];

      if (firstNode) {
        setCurrentNodeId(firstNode.id);

        // Add the first message from the bot
        const firstMessage = {
          id: 1,
          text: firstNode.data.content,
          sender: 'bot',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          buttons: firstNode.data.buttons || [],
          header: firstNode.data.header || '',
          footer: firstNode.data.footer || '',
          image: firstNode.data.image || null,
          isProductCatalog: firstNode.type === 'product_catalog' || firstNode.data.isProductCatalog,
          categoryFilter: firstNode.data.categoryFilter
        };

        setMessages([firstMessage]);
      }
    }
  }, [isOpen, nodes]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle user selecting a quick reply option
  const handleQuickReply = (buttonIndex, buttonText, buttonValue) => {
    if (!currentNodeId) return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      text: buttonText,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);

    // Find the edge that connects from the current node using this button
    const edge = edges.find(edge =>
      edge.source === currentNodeId &&
      edge.data?.buttonIndex === buttonIndex
    );

    if (edge) {
      // Find the target node
      const targetNode = nodes.find(node => node.id === edge.target);

      if (targetNode) {
        // Set the new current node
        setCurrentNodeId(targetNode.id);

        // Add the bot response
        setTimeout(() => {
          const botResponse = {
            id: messages.length + 2,
            text: targetNode.data.content,
            sender: 'bot',
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            buttons: targetNode.data.buttons || [],
            header: targetNode.data.header || '',
            footer: targetNode.data.footer || '',
            image: targetNode.data.image || null,
            isProductCatalog: targetNode.type === 'product_catalog' || targetNode.data.isProductCatalog,
            categoryFilter: targetNode.data.categoryFilter
          };

          setMessages(prev => [...prev, botResponse]);
        }, 1000);
      }
    }
  };

  // Handle sending a custom message
  const handleSendMessage = () => {
    if (inputMessage.trim() === '') return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    // For custom messages, we'll just respond with a default message
    // In a real implementation, you might want to use NLP to determine the response
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: "I'm sorry, I didn't understand that. Please use one of the options below.",
        sender: 'bot',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-start justify-center pt-4">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      ></div>

      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-2 right-4 z-20 bg-white rounded-full p-2 shadow-lg"
      >
        <XMarkIcon className="h-5 w-5 text-gray-600" />
      </button>

      {/* Mobile Phone Frame */}
      <div className="relative z-10 transform transition-all">
        {/* Phone Frame */}
        <div className="mobile-frame">
          {/* Status Bar */}
          <div className="status-bar">
            <div className="status-bar-time">8:30</div>
            <div className="status-bar-icons">
              <div className="battery-icon">61%</div>
              <div className="signal-icon"></div>
              <div className="wifi-icon"></div>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="phone-content flex flex-col h-full">
            {/* WhatsApp Header */}
            <div className="bg-whatsapp-teal text-white px-2 py-2 flex items-center">
              <button
                onClick={onClose}
                className="text-white mr-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>

              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-white mr-2 overflow-hidden">
                {storeName.charAt(0)}
              </div>

              <div className="flex-1">
                <div className="flex items-center">
                  <h2 className="font-medium text-xs">{storeName}</h2>
                  {/* Verified badge */}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 ml-1 text-blue-400">
                    <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-[9px] opacity-80">Online</p>
              </div>

              <button className="text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
              </button>
            </div>

            {/* Chat messages */}
            <div className="flex-1 overflow-y-auto p-2 bg-[#e5ddd5] bg-opacity-70 bg-[url('https://web.whatsapp.com/img/bg-chat-tile-light_04fcacde539c58cca6745483d4858c52.png')]">
              {/* Day marker */}
              <div className="text-center my-1">
                <span className="inline-block bg-white text-gray-500 text-[9px] px-2 py-0.5 rounded-md">Sunday</span>
              </div>

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-2`}
                >
                  <div className={`rounded-md px-2 py-1.5 max-w-[85%] shadow-sm ${
                    message.sender === 'user' ? 'bg-[#dcf8c6]' : 'bg-white'
                  }`}>
                    {message.header && (
                      <div className="text-[9px] font-semibold text-gray-500 mb-0.5">{message.header}</div>
                    )}

                    {message.image && (
                      <div className="mb-1 relative">
                        <img
                          src={(() => {
                            // Direct string handling
                            if (typeof message.image === 'string') {
                              if (message.image.startsWith('data:image/')) {
                                return message.image;
                              }
                              return message.image;
                            }

                            // Handle File or Blob
                            if (message.image instanceof Blob || message.image instanceof File) {
                              return URL.createObjectURL(message.image);
                            }

                            // Object handling
                            if (typeof message.image === 'object') {
                              // Try to find any property that might contain a data URL
                              for (const key of Object.keys(message.image)) {
                                if (typeof message.image[key] === 'string' && message.image[key].startsWith('data:image/')) {
                                  return message.image[key];
                                }
                              }

                              // Check common properties
                              if (message.image.data && typeof message.image.data === 'string') {
                                return message.image.data;
                              }

                              if (message.image.url && typeof message.image.url === 'string') {
                                return message.image.url;
                              }

                              if (message.image.src && typeof message.image.src === 'string') {
                                return message.image.src;
                              }

                              // Try to extract from JSON string
                              try {
                                const jsonString = JSON.stringify(message.image);

                                // Look for data:image pattern in the JSON string
                                const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                                if (dataUrlMatch && dataUrlMatch[1]) {
                                  return dataUrlMatch[1];
                                }

                                // Try to find any data URL in the string
                                const allDataUrlMatches = jsonString.match(/data:image\/[^"]+/g);
                                if (allDataUrlMatches && allDataUrlMatches.length > 0) {
                                  return allDataUrlMatches[0];
                                }
                              } catch (err) {
                                // Silent error handling
                              }
                            }

                            // Fallback to placeholder
                            return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkludmFsaWQgSW1hZ2U8L3RleHQ+PC9zdmc+';
                          })()}
                          alt="Message image"
                          className="w-full h-auto max-h-24 object-contain rounded-md"
                          onError={(e) => {
                            e.target.onerror = null;

                            // Last resort: try to directly extract the image data
                            if (typeof message.image === 'object') {
                              try {
                                // Try all possible properties that might contain the image data
                                const props = Object.keys(message.image);

                                for (const prop of props) {
                                  const value = message.image[prop];
                                  if (typeof value === 'string' && value.startsWith('data:image/')) {
                                    e.target.src = value;
                                    return;
                                  }
                                }

                                // Try to extract from JSON string with a more flexible approach
                                const jsonString = JSON.stringify(message.image);
                                const dataUrlRegex = /data:image\/[^"',}\]]+/g;
                                const matches = jsonString.match(dataUrlRegex);

                                if (matches && matches.length > 0) {
                                  e.target.src = matches[0];
                                  return;
                                }
                              } catch (err) {
                                // Silent error handling
                              }
                            }

                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPkltYWdlIEVycm9yPC90ZXh0Pjwvc3ZnPg==';
                          }}
                        />
                        {/* File size indicator */}
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white text-[8px] px-1.5 py-0.5 rounded-full flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                          66 KB
                        </div>
                      </div>
                    )}

                    <p className="text-[11px] whitespace-pre-line">{message.text}</p>

                    {/* Product Catalog Preview (if applicable) */}
                    {message.isProductCatalog && (
                      <div className="product-catalog-preview mt-2">
                        <div className="flex items-center justify-center mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-green-600 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                          </svg>
                          <span className="text-[10px] font-medium">Product Catalog</span>
                        </div>

                        <div className="bg-gray-50 p-2 rounded-lg border border-gray-200 mb-2">
                          <div className="text-[9px] text-blue-600 mb-1 font-medium">
                            Category: {message.categoryFilter === 'all' ? 'All Products' : message.categoryFilter}
                          </div>
                          <div className="text-[9px] text-green-600 mb-1">
                            Products found: 4
                          </div>
                          <div className="text-[9px] text-center text-gray-500">
                            Your products will be displayed here in the actual chat.
                          </div>
                        </div>
                      </div>
                    )}

                    {message.footer && (
                      <div className="text-[9px] italic text-gray-500 mt-0.5">{message.footer}</div>
                    )}

                    {/* Read more link */}
                    {message.sender === 'bot' && message.text.length > 50 && (
                      <div className="mt-0.5">
                        <a href="#" className="text-[9px] text-green-600 font-medium">Read more</a>
                      </div>
                    )}

                    <p className="text-right text-[8px] text-gray-500 mt-0.5">{message.timestamp}</p>

                    {/* Reply with STOP text */}
                    {message.sender === 'bot' && (
                      <div className="mt-0.5 text-[8px] text-gray-500">
                        Reply with 'STOP' to unsubscribe
                      </div>
                    )}

                    {/* Quick reply buttons */}
                    {message.sender === 'bot' && message.buttons && message.buttons.length > 0 && (
                      <div className="mt-1">
                        {message.buttons.map((button, index) => (
                          <button
                            key={index}
                            onClick={() => button.type === 'quick_reply' && handleQuickReply(index, button.text, button.value)}
                            className={`text-[9px] px-2 py-1 my-0.5 ${
                              button.type === 'quick_reply'
                                ? 'bg-white text-green-600 border border-green-200 flex items-center w-full rounded-md'
                                : button.type === 'url'
                                  ? 'bg-white text-green-600 border border-green-200 flex items-center w-full rounded-md'
                                  : 'bg-white text-green-600 border border-green-200 flex items-center w-full rounded-md'
                            }`}
                          >
                            {button.type === 'quick_reply' ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            ) : button.type === 'url' ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            )}
                            <span>{button.text}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Chat input */}
            <div className="bg-[#f0f2f5] p-1 border-t border-gray-200">
              <div className="flex items-center">
                <button className="text-gray-500 p-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </button>

                <button className="text-gray-500 p-1">
                  <PaperClipIcon className="h-4 w-4" />
                </button>

                <div className="flex-1 bg-white rounded-full px-2 py-1 mx-1 flex items-center">
                  <input
                    type="text"
                    placeholder="Message"
                    className="flex-1 border-none outline-none bg-transparent text-[10px]"
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                  />
                </div>

                <button className="text-gray-500 p-1">
                  {inputMessage ? (
                    <PaperAirplaneIcon className="h-4 w-4 text-[#00a884]" />
                  ) : (
                    <MicrophoneIcon className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>

            {/* Home indicator for iPhone-style frame */}
            <div className="home-indicator"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPreviewModal;
