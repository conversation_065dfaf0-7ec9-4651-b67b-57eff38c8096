import { CheckIcon } from '@heroicons/react/24/solid';
import Button from '../ui/Button';

export default function Pricing() {
  const tiers = [
    {
      name: 'Basic',
      id: 'basic',
      price: '$9',
      description: 'Perfect for small businesses just getting started.',
      features: [
        'Up to 100 products',
        'Basic chat automation',
        'Standard payment processing',
        'Email support',
        '1 admin user',
      ],
      cta: 'Start Basic',
      mostPopular: false,
    },
    {
      name: 'Pro',
      id: 'pro',
      price: '$29',
      description: 'For growing businesses with more advanced needs.',
      features: [
        'Up to 500 products',
        'Advanced chat automation',
        'Multiple payment methods',
        'Priority support',
        'Up to 3 admin users',
        'Analytics dashboard',
        'Influencer collaborations',
      ],
      cta: 'Start Pro',
      mostPopular: true,
    },
    {
      name: 'Enterprise',
      id: 'enterprise',
      price: '$79',
      description: 'For larger businesses with high volume sales.',
      features: [
        'Unlimited products',
        'Custom chat flows',
        'Advanced analytics',
        'Dedicated account manager',
        'Unlimited admin users',
        'API access',
        'White-label option',
        'Custom integrations',
      ],
      cta: 'Start Enterprise',
      mostPopular: false,
    },
  ];

  return (
    <div id="pricing" className="py-24 bg-gray-50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-base font-semibold leading-7 text-whatsapp-default">Pricing</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Plans for businesses of all sizes
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Choose the perfect plan for your business needs. All plans include a 14-day free trial.
          </p>
        </div>
        
        <div className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              className={`flex flex-col justify-between rounded-3xl bg-white p-8 ring-1 ring-gray-200 xl:p-10 ${
                tier.mostPopular ? 'ring-2 ring-whatsapp-default' : ''
              }`}
            >
              <div>
                <div className="flex items-center justify-between gap-x-4">
                  <h3 className="text-lg font-semibold leading-8 text-gray-900">{tier.name}</h3>
                  {tier.mostPopular ? (
                    <p className="rounded-full bg-whatsapp-default/10 px-2.5 py-1 text-xs font-semibold leading-5 text-whatsapp-default">
                      Most popular
                    </p>
                  ) : null}
                </div>
                <p className="mt-4 text-sm leading-6 text-gray-600">{tier.description}</p>
                <p className="mt-6 flex items-baseline gap-x-1">
                  <span className="text-4xl font-bold tracking-tight text-gray-900">{tier.price}</span>
                  <span className="text-sm font-semibold leading-6 text-gray-600">/month</span>
                </p>
                <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <CheckIcon className="h-6 w-5 flex-none text-whatsapp-default" aria-hidden="true" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <Button
                to="/register"
                variant={tier.mostPopular ? 'primary' : 'outline'}
                className="mt-8"
                fullWidth
              >
                {tier.cta}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
