import React, { useState, useEffect } from 'react';
import { ChevronRightIcon, ChevronLeftIcon } from '@heroicons/react/24/outline';
import { ensureValidImageUrl } from '../../utils/imageUtils';

/**
 * ProductCatalogTemplate component
 * Displays a catalog of products organized by category
 */
const ProductCatalogTemplate = ({ categories, onSelectProduct, storeId }) => {
  // Initialize with the first category if available
  const [activeCategory, setActiveCategory] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const productsPerPage = 4; // Show 4 products at a time

  // Initialize active category when categories are loaded
  useEffect(() => {
    // Set loading state based on categories
    if (!categories || !Array.isArray(categories)) {
      setIsLoading(true);
      return;
    } else {
      // Set loading to false once we have categories
      setIsLoading(false);
    }

    if (categories.length === 0) {
      return;
    }

    // First try to find the "all" category which should contain all products
    const allCategory = categories.find(cat =>
      cat.id === 'all' || cat.name === 'All Products'
    );

    if (allCategory && allCategory.products && allCategory.products.length > 0) {
      setActiveCategory(allCategory.id);
      return;
    }

    // If no "all" category with products, find any category that has products
    const categoryWithProducts = categories.find(cat =>
      cat.products && Array.isArray(cat.products) && cat.products.length > 0
    );

    if (categoryWithProducts) {
      setActiveCategory(categoryWithProducts.id);
    } else {
      setActiveCategory(categories[0].id);
    }
  }, [categories]);

  // Get active category data
  const activeCategoryData = categories.find(cat => cat.id === activeCategory) ||
                            (categories.length > 0 ? categories[0] : null);

  // Calculate total pages for pagination
  const totalPages = activeCategoryData && activeCategoryData.products
    ? Math.ceil(activeCategoryData.products.length / productsPerPage)
    : 1;

  // Get current products for the active page with valid images
  const currentProducts = activeCategoryData && activeCategoryData.products
    ? activeCategoryData.products
        .map(product => {
          // Process image data more carefully
          const imageSource = product.imageUrl || product.image;
          let processedImage = imageSource;

          // Handle object image data
          if (typeof imageSource === 'object') {
            // Try to find any property that might contain a data URL
            for (const key of Object.keys(imageSource)) {
              if (typeof imageSource[key] === 'string' && imageSource[key].startsWith('data:image/')) {
                processedImage = imageSource[key];
                break;
              }
            }

            // If we couldn't find a direct data URL, try to extract from JSON
            if (typeof processedImage === 'object') {
              try {
                const jsonString = JSON.stringify(imageSource);
                const dataUrlMatch = jsonString.match(/data:image\/[^"',}\]]+/g);
                if (dataUrlMatch && dataUrlMatch.length > 0) {
                  processedImage = dataUrlMatch[0];
                }
              } catch (err) {
                // Silent error handling
              }
            }
          }

          return {
            ...product,
            // Keep the original image/imageUrl for reference
            originalImage: imageSource,
            // Set processed versions
            image: processedImage,
            imageUrl: processedImage
          };
        })
        .slice(
          (currentPage - 1) * productsPerPage,
          currentPage * productsPerPage
        )
    : [];

  // Handle category change
  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
    setCurrentPage(1); // Reset to first page when changing category
  };

  // Handle pagination
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Check if we have products in any category
  const hasAnyProducts = categories && categories.some(cat =>
    cat.products && Array.isArray(cat.products) && cat.products.length > 0
  );

  // Show loading state
  if (isLoading) {
    // If loading takes too long, force a refresh
    setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
      }
    }, 3000);

    return (
      <div className="product-catalog">
        <div className="text-center py-4 text-gray-500">
          <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
            Loading products...
          </div>
          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            Please wait while we fetch the products.
          </div>
        </div>
      </div>
    );
  }

  // Handle the case when there are no categories or products
  if (!categories || categories.length === 0 || !activeCategoryData || !hasAnyProducts) {
    // Try to refresh the data after a delay
    setTimeout(() => {
      // This will trigger a re-render and potentially fetch new data
      setIsLoading(true);

      // Force reload the page if this is the second attempt
      if (sessionStorage.getItem('refreshAttempt')) {
        window.location.reload();
      } else {
        // Mark this as the first attempt
        sessionStorage.setItem('refreshAttempt', 'true');
        setTimeout(() => setIsLoading(false), 1000);
      }
    }, 2000);

    return (
      <div className="product-catalog">
        <div className="text-center py-4 text-gray-500">
          <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
            No products available in this store yet.
          </div>
          <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '8px' }}>
            Add products in the vendor dashboard to display them here.
          </div>
          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            <button
              onClick={() => window.location.reload()}
              style={{
                color: '#10b981',
                fontWeight: 'bold',
                border: 'none',
                background: 'none',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
            >
              Click here to refresh
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check if the active category has any products
  const hasProducts = activeCategoryData.products &&
                     Array.isArray(activeCategoryData.products) &&
                     activeCategoryData.products.length > 0;

  return (
    <div className="product-catalog">
      {/* Category tabs - only show if there are multiple categories */}
      {categories.length > 1 && (
        <div className="catalog-categories">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-tab ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => handleCategoryChange(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      )}

      {/* Products grid */}
      <div className="products-grid" style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '8px',
        margin: '8px 0'
      }}>
        {hasProducts ? (
          currentProducts.length > 0 ? (
            currentProducts.map(product => (
              <div
                key={product.id || `product-${Math.random()}`}
                className="product-card"
                style={{
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  overflow: 'hidden',
                  backgroundColor: '#f9fafb',
                  cursor: 'pointer'
                }}
                onClick={() => onSelectProduct && onSelectProduct(product)}
              >
                <div className="product-image" style={{ height: '100px', overflow: 'hidden' }}>
                  <img
                    src={(() => {
                      // Direct string handling
                      const imageSource = product.image || product.imageUrl;

                      if (typeof imageSource === 'string') {
                        if (imageSource.startsWith('data:image/')) {
                          return imageSource;
                        }
                        return ensureValidImageUrl(imageSource);
                      }

                      // Object handling
                      if (typeof imageSource === 'object') {
                        // Try to find any property that might contain a data URL
                        for (const key of Object.keys(imageSource)) {
                          if (typeof imageSource[key] === 'string' && imageSource[key].startsWith('data:image/')) {
                            return imageSource[key];
                          }
                        }

                        // Try to extract from JSON string
                        try {
                          const jsonString = JSON.stringify(imageSource);

                          // Look for data:image pattern in the JSON string
                          const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                          if (dataUrlMatch && dataUrlMatch[1]) {
                            return dataUrlMatch[1];
                          }

                          // Try to find any data URL in the string
                          const allDataUrlMatches = jsonString.match(/data:image\/[^"]+/g);
                          if (allDataUrlMatches && allDataUrlMatches.length > 0) {
                            return allDataUrlMatches[0];
                          }
                        } catch (err) {
                          // Silent error handling
                        }
                      }

                      // Fall back to the utility function
                      return ensureValidImageUrl(imageSource);
                    })()}
                    alt={product.name}
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    onError={(e) => {
                      e.target.onerror = null;

                      // Last resort: try to directly extract the image data
                      const imageSource = product.image || product.imageUrl;
                      if (typeof imageSource === 'object') {
                        try {
                          // Try all possible properties that might contain the image data
                          const props = Object.keys(imageSource);

                          for (const prop of props) {
                            const value = imageSource[prop];
                            if (typeof value === 'string' && value.startsWith('data:image/')) {
                              e.target.src = value;
                              return;
                            }
                          }

                          // Try to extract from JSON string with a more flexible approach
                          const jsonString = JSON.stringify(imageSource);
                          const dataUrlRegex = /data:image\/[^"',}\]]+/g;
                          const matches = jsonString.match(dataUrlRegex);

                          if (matches && matches.length > 0) {
                            e.target.src = matches[0];
                            return;
                          }
                        } catch (err) {
                          // Silent error handling
                        }
                      }

                      // Use embedded SVG as fallback
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
                    }}
                  />
                </div>
                <div className="product-info" style={{ padding: '8px', textAlign: 'center' }}>
                  <h3 className="product-name" style={{
                    fontSize: '12px',
                    fontWeight: 'bold',
                    margin: '0 0 4px 0',
                    textTransform: 'uppercase'
                  }}>
                    {product.name}
                  </h3>
                  <p className="product-price" style={{
                    fontSize: '12px',
                    color: '#10b981',
                    fontWeight: 'bold',
                    margin: 0
                  }}>
                    ₹{typeof product.price === 'number'
                      ? product.price.toFixed(2)
                      : (parseFloat(product.price) || 0).toFixed(2)}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-500" style={{ gridColumn: 'span 2' }}>
              No products available on this page.
            </div>
          )
        ) : (
          <div className="text-center py-4 text-gray-500" style={{ gridColumn: 'span 2' }}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>No products available in this store yet.</div>
            <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '8px' }}>
              Add products in the vendor dashboard to display them here.
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              Make sure products are assigned to the correct category.
            </div>
          </div>
        )}
      </div>

      {/* Pagination with arrow buttons */}
      {totalPages > 1 && (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '16px',
          margin: '8px 0'
        }}>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              border: 'none',
              backgroundColor: currentPage === 1 ? '#e5e7eb' : '#10b981',
              color: currentPage === 1 ? '#9ca3af' : 'white',
              cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
            }}
            onClick={goToPrevPage}
            disabled={currentPage === 1}
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              border: 'none',
              backgroundColor: currentPage === totalPages ? '#e5e7eb' : '#10b981',
              color: currentPage === totalPages ? '#9ca3af' : 'white',
              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
            }}
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductCatalogTemplate;
