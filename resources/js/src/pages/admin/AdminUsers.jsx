import { useState, useEffect } from 'react';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  EyeIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  UserPlusIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminUsers() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUser, setSelectedUser] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isEditingUser, setIsEditingUser] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    userType: 'vendor',
    isActive: true
  });

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call with pagination
        // For now, we'll use mock data
        const mockUsers = [
          {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            userType: 'admin',
            isActive: true,
            registeredDate: '2023-01-15',
            lastLogin: '2023-10-25'
          },
          {
            id: 2,
            name: 'Rahul Sharma',
            email: '<EMAIL>',
            userType: 'vendor',
            isActive: true,
            registeredDate: '2023-05-15',
            lastLogin: '2023-10-24'
          },
          {
            id: 3,
            name: 'Priya Patel',
            email: '<EMAIL>',
            userType: 'vendor',
            isActive: true,
            registeredDate: '2023-06-20',
            lastLogin: '2023-10-23'
          },
          {
            id: 4,
            name: 'Amit Kumar',
            email: '<EMAIL>',
            userType: 'vendor',
            isActive: true,
            registeredDate: '2023-07-10',
            lastLogin: '2023-10-22'
          },
          {
            id: 5,
            name: 'Neha Singh',
            email: '<EMAIL>',
            userType: 'vendor',
            isActive: false,
            registeredDate: '2023-08-05',
            lastLogin: '2023-09-15'
          },
          {
            id: 6,
            name: 'Vikram Joshi',
            email: '<EMAIL>',
            userType: 'vendor',
            isActive: true,
            registeredDate: '2023-09-15',
            lastLogin: '2023-10-20'
          },
          {
            id: 7,
            name: 'Influencer One',
            email: '<EMAIL>',
            userType: 'influencer',
            isActive: true,
            registeredDate: '2023-08-10',
            lastLogin: '2023-10-18'
          },
          {
            id: 8,
            name: 'Influencer Two',
            email: '<EMAIL>',
            userType: 'influencer',
            isActive: true,
            registeredDate: '2023-09-05',
            lastLogin: '2023-10-15'
          }
        ];
        
        // Filter users based on search term and status
        let filteredUsers = mockUsers;
        
        if (searchTerm) {
          filteredUsers = filteredUsers.filter(user => 
            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (filterStatus !== 'all') {
          filteredUsers = filteredUsers.filter(user => {
            if (filterStatus === 'active') return user.isActive;
            if (filterStatus === 'inactive') return !user.isActive;
            if (filterStatus === 'admin') return user.userType === 'admin';
            if (filterStatus === 'vendor') return user.userType === 'vendor';
            if (filterStatus === 'influencer') return user.userType === 'influencer';
            return true;
          });
        }
        
        setUsers(filteredUsers);
        setTotalPages(Math.ceil(filteredUsers.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [searchTerm, filterStatus]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle toggle user status (active/inactive)
  const handleToggleUserStatus = (userId) => {
    const user = users.find(u => u.id === userId);
    const action = user.isActive ? 'deactivate' : 'activate';
    
    if (window.confirm(`Are you sure you want to ${action} this user?`)) {
      // In a real implementation, this would be an API call
      const updatedUsers = users.map(user => 
        user.id === userId ? { ...user, isActive: !user.isActive } : user
      );
      setUsers(updatedUsers);
    }
  };

  // Handle delete user
  const handleDeleteUser = (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      // In a real implementation, this would be an API call
      const updatedUsers = users.filter(user => user.id !== userId);
      setUsers(updatedUsers);
      setSelectedUser(null);
    }
  };

  // Handle view user details
  const handleViewUserDetails = (user) => {
    setSelectedUser(user);
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle add user
  const handleAddUser = () => {
    setIsAddingUser(true);
    setFormData({
      name: '',
      email: '',
      password: '',
      userType: 'vendor',
      isActive: true
    });
  };

  // Handle edit user
  const handleEditUser = (user) => {
    setIsEditingUser(true);
    setFormData({
      id: user.id,
      name: user.name,
      email: user.email,
      password: '',
      userType: user.userType,
      isActive: user.isActive
    });
  };

  // Handle save user
  const handleSaveUser = () => {
    // Validate form
    if (!formData.name || !formData.email || (isAddingUser && !formData.password)) {
      alert('Please fill in all required fields');
      return;
    }

    if (isAddingUser) {
      // Add new user
      const newUser = {
        id: Date.now(),
        ...formData,
        registeredDate: new Date().toISOString().split('T')[0],
        lastLogin: '-'
      };
      setUsers([...users, newUser]);
    } else {
      // Update existing user
      const updatedUsers = users.map(user => 
        user.id === formData.id ? { ...user, ...formData } : user
      );
      setUsers(updatedUsers);
    }

    // Reset form
    setIsAddingUser(false);
    setIsEditingUser(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setIsAddingUser(false);
    setIsEditingUser(false);
  };

  // Get paginated users
  const paginatedUsers = users.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">User Management</div>
          <h1 className="welcome-title">
            Manage <span style={{ color: '#25D366' }}>Users</span>
          </h1>
          <p className="welcome-subtitle">
            View, edit, and manage user accounts. Control access to the platform and monitor user activity.
          </p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">User Accounts</h2>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <button
              className="btn btn-primary"
              onClick={handleAddUser}
            >
              <UserPlusIcon style={{ width: '16px', height: '16px' }} />
              Add User
            </button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-6">
          <button
            className={`btn ${filterStatus === 'all' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('all')}
          >
            All Users
          </button>
          <button
            className={`btn ${filterStatus === 'admin' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('admin')}
          >
            Admins
          </button>
          <button
            className={`btn ${filterStatus === 'vendor' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('vendor')}
          >
            Vendors
          </button>
          <button
            className={`btn ${filterStatus === 'influencer' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('influencer')}
          >
            Influencers
          </button>
          <button
            className={`btn ${filterStatus === 'active' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('active')}
          >
            Active
          </button>
          <button
            className={`btn ${filterStatus === 'inactive' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleFilterChange('inactive')}
          >
            Inactive
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading users...</p>
          </div>
        ) : (
          <div className="card" style={{ padding: '0' }}>
            {users.length === 0 ? (
              <div style={{ padding: '24px', textAlign: 'center' }}>
                <p>No users found matching your criteria.</p>
              </div>
            ) : (
              <>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Email</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Role</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Registered</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedUsers.map((user) => (
                      <tr key={user.id} className="border-b hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="font-medium text-gray-900">{user.name}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            ${user.userType === 'admin' ? 'bg-purple-100 text-purple-800' : 
                              user.userType === 'vendor' ? 'bg-blue-100 text-blue-800' : 
                              'bg-yellow-100 text-yellow-800'}`}
                          >
                            {user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500">{user.registeredDate}</div>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewUserDetails(user)}
                              className="text-blue-600 hover:text-blue-900"
                              title="View Details"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleEditUser(user)}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Edit User"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleToggleUserStatus(user.id)}
                              className={`${user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                              title={user.isActive ? 'Deactivate User' : 'Activate User'}
                            >
                              {user.isActive ? <XCircleIcon className="h-5 w-5" /> : <CheckCircleIcon className="h-5 w-5" />}
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete User"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">{Math.min(currentPage * 10, users.length)}</span> of{' '}
                        <span className="font-medium">{users.length}</span> users
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Add/Edit User Form */}
      {(isAddingUser || isEditingUser) && (
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">{isAddingUser ? 'Add New User' : 'Edit User'}</h2>
          </div>
          <div className="card" style={{ padding: '24px' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter user name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter email address"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password {!isAddingUser && '(Leave blank to keep current)'}
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder={isAddingUser ? "Enter password" : "Enter new password (optional)"}
                  required={isAddingUser}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  User Role
                </label>
                <select
                  name="userType"
                  value={formData.userType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="vendor">Vendor</option>
                  <option value="admin">Admin</option>
                  <option value="influencer">Influencer</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                  Active Account
                </label>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSaveUser}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {isAddingUser ? 'Add User' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User Details Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">User Details</h3>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Basic Information</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Name:</span> {selectedUser.name}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Email:</span> {selectedUser.email}</p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Role:</span>{' '}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${selectedUser.userType === 'admin' ? 'bg-purple-100 text-purple-800' : 
                        selectedUser.userType === 'vendor' ? 'bg-blue-100 text-blue-800' : 
                        'bg-yellow-100 text-yellow-800'}`}
                    >
                      {selectedUser.userType.charAt(0).toUpperCase() + selectedUser.userType.slice(1)}
                    </span>
                  </p>
                  <p className="text-sm mb-2">
                    <span className="font-medium">Status:</span>{' '}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${selectedUser.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {selectedUser.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Account Information</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Registered:</span> {selectedUser.registeredDate}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Last Login:</span> {selectedUser.lastLogin}</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setSelectedUser(null)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
              <button
                type="button"
                onClick={() => {
                  setSelectedUser(null);
                  handleEditUser(selectedUser);
                }}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Edit User
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
