import api from './api';

// Subscription API calls
export const subscriptionAPI = {
  // Get all subscription plans
  getAllPlans: () => api.get('/subscriptions/plans'),
  
  // Get subscription plan by ID
  getPlanById: (id) => api.get(`/subscriptions/plans/${id}`),
  
  // Get current user's subscription
  getCurrentSubscription: () => api.get('/subscriptions/current'),
  
  // Create a new subscription
  createSubscription: (subscriptionData) => api.post('/subscriptions', subscriptionData),
  
  // Update subscription status
  updateSubscriptionStatus: (id, status) => api.put(`/subscriptions/${id}/status`, { status })
};

export default subscriptionAPI;
