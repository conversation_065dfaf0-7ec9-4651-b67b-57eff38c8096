const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * Utility function to upload files
 * @param {Object} file - The file object from express-fileupload
 * @param {String} uploadPath - The path where the file should be uploaded (relative to server root)
 * @returns {Promise<Object>} - Object containing the file path and other info
 */
exports.uploadFile = async (file, uploadPath = 'uploads') => {
  return new Promise((resolve, reject) => {
    try {
      // Create directory if it doesn't exist
      const fullUploadPath = path.join(__dirname, '..', '..', uploadPath);
      if (!fs.existsSync(fullUploadPath)) {
        fs.mkdirSync(fullUploadPath, { recursive: true });
      }

      // Generate a unique filename
      const fileExtension = path.extname(file.name);
      const fileName = `${uuidv4()}${fileExtension}`;
      const filePath = path.join(uploadPath, fileName);
      const fullFilePath = path.join(__dirname, '..', '..', filePath);

      // Move the file to the upload directory
      file.mv(fullFilePath, (err) => {
        if (err) {
//           // Removed console.error
          return reject(err);
        }

        // Return file information
        resolve({
          fileName,
          filePath,
          fileType: file.mimetype,
          fileSize: file.size
        });
      });
    } catch (error) {
//       // Removed console.error
      reject(error);
    }
  });
};

/**
 * Utility function to delete a file
 * @param {String} filePath - The path of the file to delete (relative to server root)
 * @returns {Promise<Boolean>} - True if file was deleted, false otherwise
 */
exports.deleteFile = async (filePath) => {
  return new Promise((resolve) => {
    try {
      const fullFilePath = path.join(__dirname, '..', '..', filePath);
      
      // Check if file exists
      if (fs.existsSync(fullFilePath)) {
        // Delete the file
        fs.unlinkSync(fullFilePath);
        resolve(true);
      } else {
        console.warn(`File not found: ${fullFilePath}`);
        resolve(false);
      }
    } catch (error) {
//       // Removed console.error
      resolve(false);
    }
  });
};
