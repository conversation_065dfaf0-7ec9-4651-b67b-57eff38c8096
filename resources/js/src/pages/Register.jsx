import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

export default function Register() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  // Default user role is vendor, no selection needed
  const userType = 'vendor';
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const { register } = useAuth();
  const navigate = useNavigate();

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529',
    error: '#DC3545',
    errorLight: '#F8D7DA'
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      return setError('Passwords do not match');
    }

    if (password.length < 6) {
      return setError('Password must be at least 6 characters long');
    }

    try {
      setError('');
      setLoading(true);
      // Register with fixed vendor role
      const user = await register(email, password, name, userType);

      // Redirect based on user type
      if (user.userType === 'admin') {
        navigate('/admin');
      } else if (user.userType === 'vendor') {
        navigate('/vendor');
      } else if (user.userType === 'influencer') {
        navigate('/influencer');
      } else {
        navigate('/vendor'); // Default fallback
      }
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to create an account');
//       // Removed console.error
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.secondary,
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '500px',
        width: '100%',
        backgroundColor: colors.white,
        borderRadius: '16px',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
        padding: '40px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          right: '-50px',
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.primary}15 0%, rgba(255,255,255,0) 70%)`,
          zIndex: 0
        }}></div>

        <div style={{
          position: 'absolute',
          bottom: '-30px',
          left: '-30px',
          width: '120px',
          height: '120px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.primary}15 0%, rgba(255,255,255,0) 70%)`,
          zIndex: 0
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <h1 style={{
              fontSize: '28px',
              fontWeight: 700,
              color: colors.dark,
              marginBottom: '12px'
            }}>
              Create Your Account
            </h1>
            <p style={{
              fontSize: '15px',
              color: colors.gray,
              marginBottom: '0'
            }}>
              Join Whamart and start selling today
            </p>
          </div>

          {error && (
            <div style={{
              backgroundColor: colors.errorLight,
              color: colors.error,
              padding: '12px 16px',
              borderRadius: '8px',
              marginBottom: '24px',
              fontSize: '14px',
              textAlign: 'center',
              maxWidth: '350px',
              margin: '0 auto 24px auto'
            }}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%',
              maxWidth: '350px',
              margin: '0 auto'
            }}>
            <div style={{ marginBottom: '24px', width: '100%' }}>
              <label
                htmlFor="name"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: colors.dark,
                  textAlign: 'center'
                }}
              >
                Full Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                autoComplete="name"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: '1px solid #E2E8F0',
                  fontSize: '15px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  backgroundColor: colors.lightGray,
                  textAlign: 'center'
                }}
                placeholder="Enter your full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>

            <div style={{ marginBottom: '24px', width: '100%' }}>
              <label
                htmlFor="email-address"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: colors.dark,
                  textAlign: 'center'
                }}
              >
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: '1px solid #E2E8F0',
                  fontSize: '15px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  backgroundColor: colors.lightGray,
                  textAlign: 'center'
                }}
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div style={{ marginBottom: '24px', width: '100%' }}>
              <label
                htmlFor="password"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: colors.dark,
                  textAlign: 'center'
                }}
              >
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: '1px solid #E2E8F0',
                  fontSize: '15px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  backgroundColor: colors.lightGray,
                  textAlign: 'center'
                }}
                placeholder="Create a password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div style={{ marginBottom: '24px', width: '100%' }}>
              <label
                htmlFor="confirm-password"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: colors.dark,
                  textAlign: 'center'
                }}
              >
                Confirm Password
              </label>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                autoComplete="new-password"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: '1px solid #E2E8F0',
                  fontSize: '15px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  backgroundColor: colors.lightGray,
                  textAlign: 'center'
                }}
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>

            {/* User role selection removed - default is vendor */}

            <button
              type="submit"
              disabled={loading}
              style={{
                width: '100%',
                padding: '14px',
                backgroundColor: colors.primary,
                color: colors.white,
                border: 'none',
                borderRadius: '50px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1,
                transition: 'all 0.3s ease',
                marginBottom: '20px',
                boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)'
              }}
            >
              {loading ? 'Creating account...' : 'Create account'}
            </button>

            <div style={{ textAlign: 'center', width: '100%' }}>
              <p style={{
                fontSize: '14px',
                color: colors.gray,
                margin: 0
              }}>
                Already have an account?{' '}
                <Link
                  to="/login"
                  style={{
                    color: colors.primary,
                    fontWeight: '600',
                    textDecoration: 'none'
                  }}
                >
                  Sign in
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
