import React from 'react';
import ChatHeader from './ChatHeader';

/**
 * HeaderTest component
 * Used to test the ChatHeader with different store names
 */
const HeaderTest = () => {
  // Test with different store names
  const testStoreNames = [
    "Anita Kirana Store",
    "Anita Kirana Store & General Merchandise",
    "Short Name"
  ];

  return (
    <div style={{ 
      width: '100%', 
      maxWidth: '500px', 
      margin: '20px auto',
      border: '1px solid #ccc',
      borderRadius: '8px',
      overflow: 'hidden'
    }}>
      <h2 style={{ padding: '10px', textAlign: 'center' }}>Header Test</h2>
      
      {testStoreNames.map((name, index) => (
        <div key={index} style={{ marginBottom: '20px' }}>
          <p style={{ padding: '5px 10px', backgroundColor: '#f5f5f5', margin: 0 }}>
            Testing: "{name}"
          </p>
          <ChatHeader 
            storeName={name} 
            storeLogoUrl={null} 
            isVerified={true} 
          />
        </div>
      ))}
    </div>
  );
};

export default HeaderTest;
