import React, { useState, useEffect } from 'react';
import { XMarkIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import { productAPI } from '../../services/api';

/**
 * ProductCatalogEditor component for the Chat Flow Builder
 * Provides a simplified side panel for editing product catalog properties
 */
const ProductCatalogEditor = ({ message, isOpen, onClose, onSave }) => {
  // State for message properties
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);

  // Initialize form with message data when opened
  useEffect(() => {
    if (message && isOpen) {
      setCategoryFilter(message.categoryFilter || 'all');
    }
  }, [message, isOpen]);

  // Fetch product categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        // Fetch all products to extract unique categories
        const response = await productAPI.getAllProducts();

        // Extract unique categories
        const uniqueCategories = [...new Set(
          response.data.products
            .map(product => product.category)
            .filter(category => category) // Filter out null/undefined
        )];

        setCategories(uniqueCategories);
      } catch (err) {
//         // Removed console.error
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      fetchCategories();
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Save changes - preserve other properties from the original message
    onSave({
      ...message,
      categoryFilter,
      type: 'product_catalog' // Ensure the node type is set
    });

    // Close the panel immediately after saving
    onClose();
  };

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-y-0 right-0 z-[9999] flex">
      {/* Overlay for mobile - only visible on small screens */}
      <div
        className="fixed inset-0 bg-black bg-opacity-30 lg:hidden"
        onClick={onClose}
      ></div>

      {/* Side panel */}
      <div className="bg-white shadow-xl w-full max-w-md h-full overflow-y-auto relative animate-slide-in-right">
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between z-10">
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <ShoppingCartIcon className="h-5 w-5 mr-2 text-green-600" />
            Edit Product Catalog
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* Category Filter - The only option available */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Category
            </label>
            {loading ? (
              <div className="text-center py-4 text-gray-500">Loading categories...</div>
            ) : (
              <>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Products</option>
                  {categories.map((category, index) => (
                    <option key={index} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                <p className="mt-2 text-sm text-gray-500">
                  Select which product category to display in this catalog message.
                </p>

                {/* Product preview message */}
                <div className="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100 text-sm text-blue-700">
                  <p className="font-medium mb-1">Preview:</p>
                  <p>
                    {categoryFilter === 'all'
                      ? 'All products will be displayed in this catalog.'
                      : `Only products from the "${categoryFilter}" category will be displayed.`}
                  </p>
                </div>
              </>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700"
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductCatalogEditor;
