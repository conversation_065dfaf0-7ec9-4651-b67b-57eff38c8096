import React, { useState } from 'react';
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline';

/**
 * TemplateMarketplace component for the Chat Flow Builder
 * Provides a modal for selecting message templates or creating from scratch
 */
const TemplateMarketplace = ({ isOpen, onClose, onSelectTemplate, templateType = 'message' }) => {
  const [activeCategory, setActiveCategory] = useState('all');

  // Mock template categories
  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'retail', name: 'Retail & E-commerce' },
    { id: 'food', name: 'Food & Restaurants' },
    { id: 'services', name: 'Services' },
    { id: 'education', name: 'Education' }
  ];

  // Mock templates
  const templates = [
    {
      id: 1,
      category: 'retail',
      name: 'Welcome Message',
      description: 'Initial greeting for new customers',
      content: 'Welcome to our store! How can I help you today?',
      buttons: [
        { type: 'quick_reply', text: 'View Products', value: 'products' },
        { type: 'quick_reply', text: 'Check Order', value: 'order' },
        { type: 'call', text: 'Call Support', phoneNumber: '+91123456789' }
      ],
      header: 'Welcome',
      footer: 'Reply to continue'
    },
    {
      id: 9,
      category: 'retail',
      name: 'Item Details Handler',
      description: 'REQUIRED for catalog: This node handles item selection and displays details when a customer clicks on an item',
      content: 'This item is currently in stock and ready to ship. Would you like to place an order?',
      buttons: [
        { type: 'quick_reply', text: 'Buy Now', value: 'buy_now' },
        { type: 'quick_reply', text: 'Back to Catalog', value: 'back_to_catalog' }
      ],
      header: 'ITEM DETAILS',
      image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80',
      handleProductSelection: true
    },
    {
      id: 2,
      category: 'retail',
      name: 'Product Catalog',
      description: 'Show product categories',
      content: 'Here are our product categories. Please select one to browse:',
      buttons: [
        { type: 'quick_reply', text: 'Clothing', value: 'clothing' },
        { type: 'quick_reply', text: 'Electronics', value: 'electronics' },
        { type: 'quick_reply', text: 'Home Goods', value: 'home' }
      ]
    },
    {
      id: 3,
      category: 'retail',
      name: 'Product Launch',
      description: 'Announce a new product with image',
      header: 'NEW PRODUCT LAUNCH',
      image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80',
      content: 'Introducing our latest product with amazing features. Limited time offer with special discounts!',
      footer: 'Offer valid until stocks last',
      buttons: [
        { type: 'url', text: 'Shop Now', url: 'https://example.com/shop' },
        { type: 'quick_reply', text: 'Tell Me More', value: 'more_info' }
      ]
    },
    {
      id: 4,
      category: 'food',
      name: 'Restaurant Menu',
      description: 'Show restaurant menu categories',
      content: "Here's our menu. What would you like to order today?",
      buttons: [
        { type: 'quick_reply', text: 'Starters', value: 'starters' },
        { type: 'quick_reply', text: 'Main Course', value: 'main' },
        { type: 'quick_reply', text: 'Desserts', value: 'desserts' }
      ],
      header: 'Our Menu'
    },
    {
      id: 5,
      category: 'services',
      name: 'Appointment Booking',
      description: 'Help customers book appointments',
      content: 'Would you like to book an appointment with us?',
      buttons: [
        { type: 'quick_reply', text: 'Book Now', value: 'book' },
        { type: 'quick_reply', text: 'View Availability', value: 'availability' },
        { type: 'url', text: 'Visit Website', url: 'https://example.com/booking' }
      ]
    },
    {
      id: 7,
      category: 'food',
      name: 'Special Dish',
      description: 'Promote a special dish with image',
      header: 'TODAY\'S SPECIAL',
      image: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80',
      content: 'Try our chef\'s special dish of the day! Made with fresh ingredients and served with complimentary sides.',
      footer: 'Available only today',
      buttons: [
        { type: 'quick_reply', text: 'Order Now', value: 'order_special' },
        { type: 'call', text: 'Call Restaurant', phoneNumber: '+911234567890' }
      ]
    },
    {
      id: 6,
      category: 'education',
      name: 'Course Inquiry',
      description: 'Handle course inquiries',
      content: 'Thank you for your interest in our courses. What would you like to know?',
      buttons: [
        { type: 'quick_reply', text: 'Course List', value: 'courses' },
        { type: 'quick_reply', text: 'Fees', value: 'fees' },
        { type: 'quick_reply', text: 'Schedule', value: 'schedule' }
      ],
      header: 'Course Information'
    },
    {
      id: 8,
      category: 'services',
      name: 'Service Promotion',
      description: 'Promote a service with image',
      header: 'PREMIUM SERVICE',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80',
      content: 'Our premium service package includes everything you need for a complete solution. Book now and get 20% off!',
      footer: 'Limited time offer',
      buttons: [
        { type: 'url', text: 'Learn More', url: 'https://example.com/services' },
        { type: 'quick_reply', text: 'Get Quote', value: 'quote' }
      ]
    }
  ];

  // Filter templates by category
  const filteredTemplates = activeCategory === 'all'
    ? templates
    : templates.filter(template => template.category === activeCategory);

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>

      {/* Modal */}
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl overflow-hidden relative z-10 transform transition-all">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h2 className="text-xl font-medium text-gray-900">
              {templateType === 'product_catalog' ? 'Add Product Catalog' : 'Add Message'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Create from scratch card */}
            <div
              className="border-2 border-dashed border-green-300 rounded-lg p-6 mb-6 hover:bg-green-50 cursor-pointer transition-colors flex flex-col items-center justify-center"
              onClick={() => onSelectTemplate()}
            >
              <div className="bg-green-100 rounded-full p-3 mb-3">
                <PlusIcon className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">Create from Scratch</h3>
              <p className="text-sm text-gray-500 text-center">
                {templateType === 'product_catalog'
                  ? 'Start with a blank product catalog and customize it'
                  : 'Start with a blank message and customize it'
                }
              </p>
            </div>

            {/* Category tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex space-x-8 overflow-x-auto" aria-label="Tabs">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`
                      whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm
                      ${activeCategory === category.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                    `}
                  >
                    {category.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Templates grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTemplates.map((template) => (
                <div
                  key={template.id}
                  className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onSelectTemplate(template)}
                >
                  <div className="p-4">
                    <h3 className="text-md font-medium text-gray-900 mb-1">{template.name}</h3>
                    <p className="text-xs text-gray-500 mb-3">{template.description}</p>

                    {/* Template preview */}
                    <div className="bg-gray-50 p-3 rounded-md text-xs border border-gray-200">
                      {/* Header */}
                      {template.header && (
                        <div className="text-xs font-semibold text-green-600 mb-1 p-1 bg-green-50 rounded">
                          {template.header}
                        </div>
                      )}

                      {/* Image */}
                      {template.image && (
                        <div className="mb-2">
                          <img
                            src={template.image}
                            alt="Template preview"
                            className="w-full h-auto max-h-20 object-cover rounded-md"
                          />
                        </div>
                      )}

                      {/* Content */}
                      <div className="text-gray-700 mb-1">
                        {template.content.length > 80
                          ? `${template.content.substring(0, 80)}...`
                          : template.content}
                      </div>

                      {/* Footer */}
                      {template.footer && (
                        <div className="text-xs italic text-gray-500 mb-1 p-1 bg-gray-100 rounded">
                          {template.footer}
                        </div>
                      )}
                    </div>

                    {/* Buttons */}
                    {template.buttons && template.buttons.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {template.buttons.map((button, idx) => (
                          <span
                            key={idx}
                            className={`
                              text-xs px-2 py-0.5 rounded-full
                              ${button.type === 'quick_reply' ? 'bg-green-100 text-green-800' :
                                button.type === 'url' ? 'bg-blue-100 text-blue-800' :
                                'bg-purple-100 text-purple-800'}
                            `}
                          >
                            {button.text}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateMarketplace;
