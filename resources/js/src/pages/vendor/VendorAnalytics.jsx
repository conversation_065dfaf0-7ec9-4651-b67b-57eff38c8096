import { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarDaysIcon,
  ArrowDownTrayIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import '../../../src/styles/vendor-dashboard.css';
import { analyticsAPI } from '../../services/api';

export default function VendorAnalytics() {
  const [timeRange, setTimeRange] = useState('month');
  const [currentPage, setCurrentPage] = useState(1);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const itemsPerPage = 5;

  // State for analytics data
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalRevenue: { value: '₹0', change: '0%', trend: 'neutral' },
      totalOrders: { value: '0', change: '0%', trend: 'neutral' },
      averageOrderValue: { value: '₹0', change: '0%', trend: 'neutral' },
      conversionRate: { value: '0%', change: '0%', trend: 'neutral' }
    },
    topProducts: [],
    topCustomers: [],
    monthlySales: [],
    customerAcquisition: { organic: 0, direct: 0, referral: 0, social: 0 }
  });

  // Loading states
  const [loading, setLoading] = useState({
    overview: true,
    monthlySales: true,
    topProducts: true,
    customerAcquisition: true
  });

  // Error states
  const [error, setError] = useState({
    overview: null,
    monthlySales: null,
    topProducts: null,
    customerAcquisition: null
  });

  // Fetch analytics overview data
  useEffect(() => {
    const fetchAnalyticsOverview = async () => {
      try {
        setLoading(prev => ({ ...prev, overview: true }));
        const response = await analyticsAPI.getAnalyticsOverview(timeRange);
        setAnalyticsData(prev => ({ ...prev, overview: response.data.overview }));
        setError(prev => ({ ...prev, overview: null }));
      } catch (err) {
        setError(prev => ({ ...prev, overview: 'Failed to load analytics overview' }));
      } finally {
        setLoading(prev => ({ ...prev, overview: false }));
      }
    };

    fetchAnalyticsOverview();
  }, [timeRange]);

  // Fetch monthly sales data
  useEffect(() => {
    const fetchMonthlySales = async () => {
      try {
        setLoading(prev => ({ ...prev, monthlySales: true }));
        const response = await analyticsAPI.getMonthlySales(currentYear);
        setAnalyticsData(prev => ({ ...prev, monthlySales: response.data.monthlySales }));
        setError(prev => ({ ...prev, monthlySales: null }));
      } catch (err) {
        setError(prev => ({ ...prev, monthlySales: 'Failed to load monthly sales data' }));
      } finally {
        setLoading(prev => ({ ...prev, monthlySales: false }));
      }
    };

    fetchMonthlySales();
  }, [currentYear]);

  // Fetch top products data
  useEffect(() => {
    const fetchTopProducts = async () => {
      try {
        setLoading(prev => ({ ...prev, topProducts: true }));
        const response = await analyticsAPI.getTopProducts(timeRange);
        setAnalyticsData(prev => ({ ...prev, topProducts: response.data.topProducts }));
        setError(prev => ({ ...prev, topProducts: null }));
      } catch (err) {
        setError(prev => ({ ...prev, topProducts: 'Failed to load top products data' }));
      } finally {
        setLoading(prev => ({ ...prev, topProducts: false }));
      }
    };

    fetchTopProducts();
  }, [timeRange]);

  // Fetch customer acquisition data
  useEffect(() => {
    const fetchCustomerAcquisition = async () => {
      try {
        setLoading(prev => ({ ...prev, customerAcquisition: true }));
        const response = await analyticsAPI.getCustomerAcquisition();
        setAnalyticsData(prev => ({ ...prev, customerAcquisition: response.data.customerAcquisition }));
        setError(prev => ({ ...prev, customerAcquisition: null }));
      } catch (err) {
        setError(prev => ({ ...prev, customerAcquisition: 'Failed to load customer acquisition data' }));
      } finally {
        setLoading(prev => ({ ...prev, customerAcquisition: false }));
      }
    };

    fetchCustomerAcquisition();
  }, []);

  // Helper function to render trend indicator
  const renderTrendIndicator = (trend, change) => {
    if (trend === 'up') {
      return (
        <span style={{ display: 'inline-flex', alignItems: 'center', color: 'var(--success)' }}>
          <ArrowTrendingUpIcon style={{ width: '12px', height: '12px', marginRight: '2px' }} />
          <span style={{ fontSize: '12px' }}>{change}</span>
        </span>
      );
    } else if (trend === 'down') {
      return (
        <span style={{ display: 'inline-flex', alignItems: 'center', color: 'var(--error)' }}>
          <ArrowTrendingDownIcon style={{ width: '12px', height: '12px', marginRight: '2px' }} />
          <span style={{ fontSize: '12px' }}>{change}</span>
        </span>
      );
    } else {
      // Neutral trend
      return (
        <span style={{ display: 'inline-flex', alignItems: 'center', color: 'var(--gray)' }}>
          <span style={{ fontSize: '12px' }}>{change}</span>
        </span>
      );
    }
  };

  // Pagination helpers
  const indexOfLastProduct = currentPage * itemsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - itemsPerPage;
  const currentProducts = analyticsData.topProducts.slice(indexOfFirstProduct, indexOfLastProduct);

  // For now, we'll use the same top products data for customers since we don't have a separate API for top customers
  const currentCustomers = analyticsData.topProducts.map(product => ({
    id: product.id,
    name: `Customer ${product.id}`,
    orders: product.sales > 10 ? 10 : product.sales,
    spent: product.revenue
  })).slice(indexOfFirstProduct, indexOfLastProduct);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const totalPages = Math.ceil(analyticsData.topProducts.length / itemsPerPage);

  // Handle time range change
  const handleTimeRangeChange = (newTimeRange) => {
    setTimeRange(newTimeRange);
    setCurrentPage(1); // Reset to first page when changing time range
  };

  return (
    <div className="vendor-dashboard" style={{ maxWidth: '100%', overflowX: 'hidden' }}>
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Analytics Dashboard</div>
          <h1 className="welcome-title">
            Store <span style={{ color: '#25D366' }}>Analytics</span>
          </h1>
          <p className="welcome-subtitle">
            Track your store's performance metrics, analyze sales trends, and gain insights to grow your business.
          </p>
        </div>
      </div>

      {/* Time Range Filter */}
      <div className="stats-section" style={{ marginBottom: '24px' }}>
        <div className="section-header" style={{ marginBottom: '12px' }}>
          <h2 className="section-title">Performance Overview</h2>
          <div className="section-header-actions">
            <div className="flex items-center gap-4">
              <select
                value={timeRange}
                onChange={(e) => handleTimeRangeChange(e.target.value)}
                className="analytics-filter-select"
                style={{
                  padding: '8px 12px',
                  borderRadius: 'var(--border-radius-md)',
                  border: '1px solid rgba(0,0,0,0.1)',
                  fontSize: '14px',
                  backgroundColor: 'white',
                  minWidth: '120px'
                }}
              >
                <option value="week">Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="year">Last 12 months</option>
              </select>
              <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
                <span>Export Data</span>
              </button>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="stats-grid" style={{ marginBottom: '0' }}>
          {loading.overview ? (
            // Loading state
            Array(4).fill().map((_, index) => (
              <div key={index} className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto' }}>
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(0, 0, 0, 0.05)', width: '48px', height: '48px' }}></div>
                <div className="stat-content">
                  <div style={{ height: '14px', width: '80px', backgroundColor: 'rgba(0, 0, 0, 0.05)', borderRadius: '4px', margin: '0 0 8px 0' }}></div>
                  <div style={{ height: '22px', width: '60px', backgroundColor: 'rgba(0, 0, 0, 0.05)', borderRadius: '4px', margin: '0 0 8px 0' }}></div>
                  <div style={{ height: '12px', width: '120px', backgroundColor: 'rgba(0, 0, 0, 0.05)', borderRadius: '4px', margin: '0' }}></div>
                </div>
              </div>
            ))
          ) : error.overview ? (
            // Error state
            <div className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto', gridColumn: '1 / -1' }}>
              <p style={{ color: 'var(--error)', margin: '0' }}>{error.overview}</p>
              <button
                className="btn btn-sm btn-outline"
                style={{ marginTop: '8px' }}
                onClick={() => {
                  setError(prev => ({ ...prev, overview: null }));
                  analyticsAPI.getAnalyticsOverview(timeRange)
                    .then(response => setAnalyticsData(prev => ({ ...prev, overview: response.data.overview })))
                    .catch(err => setError(prev => ({ ...prev, overview: 'Failed to load analytics overview' })));
                }}
              >
                Retry
              </button>
            </div>
          ) : (
            // Data loaded successfully
            <>
              <div className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto' }}>
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)', width: '48px', height: '48px' }}>
                  <CurrencyDollarIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label" style={{ margin: '0 0 4px 0', fontSize: '14px' }}>Total Revenue</h3>
                  <p className="stat-value" style={{ margin: '0 0 4px 0', fontSize: '22px' }}>{analyticsData.overview.totalRevenue.value}</p>
                  <p className="stat-description" style={{ margin: '0', fontSize: '12px' }}>
                    {renderTrendIndicator(analyticsData.overview.totalRevenue.trend, analyticsData.overview.totalRevenue.change)}
                    <span style={{ marginLeft: '4px' }}>vs. previous period</span>
                  </p>
                </div>
              </div>

              <div className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto' }}>
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)', width: '48px', height: '48px' }}>
                  <ShoppingBagIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label" style={{ margin: '0 0 4px 0', fontSize: '14px' }}>Total Orders</h3>
                  <p className="stat-value" style={{ margin: '0 0 4px 0', fontSize: '22px' }}>{analyticsData.overview.totalOrders.value}</p>
                  <p className="stat-description" style={{ margin: '0', fontSize: '12px' }}>
                    {renderTrendIndicator(analyticsData.overview.totalOrders.trend, analyticsData.overview.totalOrders.change)}
                    <span style={{ marginLeft: '4px' }}>vs. previous period</span>
                  </p>
                </div>
              </div>

              <div className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto' }}>
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)', width: '48px', height: '48px' }}>
                  <ChartBarIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label" style={{ margin: '0 0 4px 0', fontSize: '14px' }}>Avg Order Value</h3>
                  <p className="stat-value" style={{ margin: '0 0 4px 0', fontSize: '22px' }}>{analyticsData.overview.averageOrderValue.value}</p>
                  <p className="stat-description" style={{ margin: '0', fontSize: '12px' }}>
                    {renderTrendIndicator(analyticsData.overview.averageOrderValue.trend, analyticsData.overview.averageOrderValue.change)}
                    <span style={{ marginLeft: '4px' }}>vs. previous period</span>
                  </p>
                </div>
              </div>

              <div className="stat-card" style={{ padding: '16px', margin: '0', height: 'auto' }}>
                <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)', width: '48px', height: '48px' }}>
                  <UserGroupIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
                </div>
                <div className="stat-content">
                  <h3 className="stat-label" style={{ margin: '0 0 4px 0', fontSize: '14px' }}>Conversion Rate</h3>
                  <p className="stat-value" style={{ margin: '0 0 4px 0', fontSize: '22px' }}>{analyticsData.overview.conversionRate.value}</p>
                  <p className="stat-description" style={{ margin: '0', fontSize: '12px' }}>
                    {renderTrendIndicator(analyticsData.overview.conversionRate.trend, analyticsData.overview.conversionRate.change)}
                    <span style={{ marginLeft: '4px' }}>vs. previous period</span>
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Sales Chart Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Sales Trends</h2>
          <div className="section-header-actions"></div>
        </div>
        <div className="card">
          <div style={{ padding: 'var(--spacing-lg)' }}>
            <div className="flex justify-between items-center mb-4">
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: '0' }}>Monthly Revenue</h3>
              <div className="flex items-center gap-2">
                <CalendarDaysIcon style={{ width: '16px', height: '16px', color: 'var(--gray)' }} />
                <select
                  value={currentYear}
                  onChange={(e) => setCurrentYear(parseInt(e.target.value))}
                  style={{
                    fontSize: '14px',
                    color: 'var(--gray)',
                    border: 'none',
                    background: 'transparent',
                    cursor: 'pointer'
                  }}
                >
                  {[...Array(5)].map((_, i) => {
                    const year = new Date().getFullYear() - i;
                    return <option key={year} value={year}>{year}</option>;
                  })}
                </select>
              </div>
            </div>

            {loading.monthlySales ? (
              // Loading state for chart
              <div className="relative bg-light-gray rounded-md flex items-center justify-center overflow-hidden" style={{ height: '250px' }}>
                <div style={{ fontSize: '14px', color: 'var(--gray)' }}>Loading chart data...</div>
              </div>
            ) : error.monthlySales ? (
              // Error state for chart
              <div className="relative bg-light-gray rounded-md flex items-center justify-center overflow-hidden" style={{ height: '250px' }}>
                <div style={{ textAlign: 'center' }}>
                  <p style={{ color: 'var(--error)', margin: '0 0 8px 0' }}>{error.monthlySales}</p>
                  <button
                    className="btn btn-sm btn-outline"
                    onClick={() => {
                      setError(prev => ({ ...prev, monthlySales: null }));
                      analyticsAPI.getMonthlySales(currentYear)
                        .then(response => setAnalyticsData(prev => ({ ...prev, monthlySales: response.data.monthlySales })))
                        .catch(err => setError(prev => ({ ...prev, monthlySales: 'Failed to load monthly sales data' })));
                    }}
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              // Chart with data
              <div className="relative bg-light-gray rounded-md flex items-center justify-center overflow-hidden" style={{ height: '250px' }}>
                <div className="flex h-[70%] w-full items-end justify-between px-1 overflow-x-auto">
                  {analyticsData.monthlySales.map((month, index) => (
                    <div key={index} className="flex flex-col items-center" style={{ minWidth: '16px', flex: '1', maxWidth: '30px' }}>
                      <div style={{
                        height: `${month.revenue > 0 ? Math.max(5, (month.revenue / Math.max(...analyticsData.monthlySales.map(m => m.revenue)) * 100)) : 0}%`,
                        width: '12px',
                        backgroundColor: 'var(--primary)',
                        borderRadius: '3px 3px 0 0'
                      }}></div>
                      <span style={{ fontSize: '9px', marginTop: '3px' }}>{month.month}</span>
                    </div>
                  ))}
                </div>
                <div className="absolute top-2 right-2 text-xs text-gray">
                  Revenue in INR
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Top Products and Customers */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Top Performers</h2>
          <div className="section-header-actions"></div>
        </div>
        <div className="analytics-grid">
          {/* Top Products */}
          <div className="card">
            <div style={{ padding: 'var(--spacing-lg)', borderBottom: '1px solid rgba(0,0,0,0.05)' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: '0' }}>Top Selling Products</h3>
            </div>
            <div className="table-container">
              {loading.topProducts ? (
                // Loading state for top products
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <div style={{ fontSize: '14px', color: 'var(--gray)' }}>Loading top products...</div>
                </div>
              ) : error.topProducts ? (
                // Error state for top products
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <p style={{ color: 'var(--error)', margin: '0 0 8px 0' }}>{error.topProducts}</p>
                  <button
                    className="btn btn-sm btn-outline"
                    onClick={() => {
                      setError(prev => ({ ...prev, topProducts: null }));
                      analyticsAPI.getTopProducts(timeRange)
                        .then(response => setAnalyticsData(prev => ({ ...prev, topProducts: response.data.topProducts })))
                        .catch(err => setError(prev => ({ ...prev, topProducts: 'Failed to load top products data' })));
                    }}
                  >
                    Retry
                  </button>
                </div>
              ) : analyticsData.topProducts.length === 0 ? (
                // No data state
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <p style={{ color: 'var(--gray)', margin: '0' }}>No product sales data available for this period.</p>
                </div>
              ) : (
                // Data loaded successfully
                <table className="products-table">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Units Sold</th>
                      <th>Revenue</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentProducts.map((product) => (
                      <tr key={product.id}>
                        <td style={{ fontWeight: '500' }}>{product.name}</td>
                        <td>{product.sales}</td>
                        <td style={{ fontWeight: '600', color: 'var(--primary)' }}>{product.revenue}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            <div className="pagination-container">
              <div className="pagination-info">
                Showing {indexOfFirstProduct + 1} to {Math.min(indexOfLastProduct, analyticsData.topProducts.length)} of {analyticsData.topProducts.length} products
              </div>
              <div className="pagination-controls">
                <button
                  className="pagination-button"
                  onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                  Previous
                </button>
                <button
                  className="pagination-button"
                  onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
                </button>
              </div>
            </div>
          </div>

          {/* Top Customers */}
          <div className="card">
            <div style={{ padding: 'var(--spacing-lg)', borderBottom: '1px solid rgba(0,0,0,0.05)' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: '0' }}>Top Customers</h3>
            </div>
            <div className="table-container">
              <table className="products-table">
                <thead>
                  <tr>
                    <th>Customer</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                  </tr>
                </thead>
                <tbody>
                  {currentCustomers.map((customer) => (
                    <tr key={customer.id}>
                      <td>
                        <div className="flex items-center gap-3">
                          <div style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '50%',
                            backgroundColor: 'var(--primary-light)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'var(--primary)',
                            fontWeight: '600',
                            fontSize: '14px'
                          }}>
                            {customer.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <span style={{ fontWeight: '500' }}>{customer.name}</span>
                        </div>
                      </td>
                      <td>{customer.orders}</td>
                      <td style={{ fontWeight: '600', color: 'var(--primary)' }}>{customer.spent}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="pagination-container">
              <div className="pagination-info">
                Showing {indexOfFirstProduct + 1} to {Math.min(indexOfLastProduct, analyticsData.topCustomers.length)} of {analyticsData.topCustomers.length} customers
              </div>
              <div className="pagination-controls">
                <button
                  className="pagination-button"
                  onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                  Previous
                </button>
                <button
                  className="pagination-button"
                  onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Acquisition */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Customer Acquisition</h2>
          <div className="section-header-actions"></div>
        </div>
        <div className="card">
          <div style={{ padding: 'var(--spacing-lg)' }}>
            <div className="flex justify-between items-center mb-4">
              <h3 style={{ fontSize: '16px', fontWeight: '600', margin: '0' }}>Acquisition Sources</h3>
              <span style={{ fontSize: '14px', color: 'var(--gray)' }}>Based on {analyticsData.overview.totalOrders.value} orders</span>
            </div>

            {/* Acquisition Chart */}
            {loading.customerAcquisition ? (
              // Loading state for customer acquisition
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <div style={{ fontSize: '14px', color: 'var(--gray)' }}>Loading customer acquisition data...</div>
              </div>
            ) : error.customerAcquisition ? (
              // Error state for customer acquisition
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <p style={{ color: 'var(--error)', margin: '0 0 8px 0' }}>{error.customerAcquisition}</p>
                <button
                  className="btn btn-sm btn-outline"
                  onClick={() => {
                    setError(prev => ({ ...prev, customerAcquisition: null }));
                    analyticsAPI.getCustomerAcquisition()
                      .then(response => setAnalyticsData(prev => ({ ...prev, customerAcquisition: response.data.customerAcquisition })))
                      .catch(err => setError(prev => ({ ...prev, customerAcquisition: 'Failed to load customer acquisition data' })));
                  }}
                >
                  Retry
                </button>
              </div>
            ) : (
              // Data loaded successfully
              <div className="flex flex-col gap-4">
                {/* Organic */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span style={{ fontSize: '14px', fontWeight: '500' }}>Organic Search</span>
                    <span style={{ fontSize: '14px', fontWeight: '600' }}>{analyticsData.customerAcquisition.organic}%</span>
                  </div>
                  <div style={{
                    height: '8px',
                    width: '100%',
                    backgroundColor: 'var(--light-gray)',
                    borderRadius: '4px',
                    overflow: 'hidden'
                  }}>
                    <div
                      style={{
                        height: '100%',
                        width: `${analyticsData.customerAcquisition.organic}%`,
                        backgroundColor: 'var(--primary)',
                        borderRadius: '4px'
                      }}
                    ></div>
                  </div>
                </div>

                {/* Direct */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span style={{ fontSize: '14px', fontWeight: '500' }}>Direct</span>
                    <span style={{ fontSize: '14px', fontWeight: '600' }}>{analyticsData.customerAcquisition.direct}%</span>
                  </div>
                  <div style={{
                    height: '8px',
                    width: '100%',
                    backgroundColor: 'var(--light-gray)',
                    borderRadius: '4px',
                    overflow: 'hidden'
                  }}>
                    <div
                      style={{
                        height: '100%',
                        width: `${analyticsData.customerAcquisition.direct}%`,
                        backgroundColor: '#075E54',
                        borderRadius: '4px'
                      }}
                    ></div>
                  </div>
                </div>

                {/* Referral */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span style={{ fontSize: '14px', fontWeight: '500' }}>Referrals</span>
                    <span style={{ fontSize: '14px', fontWeight: '600' }}>{analyticsData.customerAcquisition.referral}%</span>
                  </div>
                  <div style={{
                    height: '8px',
                    width: '100%',
                    backgroundColor: 'var(--light-gray)',
                    borderRadius: '4px',
                    overflow: 'hidden'
                  }}>
                    <div
                      style={{
                        height: '100%',
                        width: `${analyticsData.customerAcquisition.referral}%`,
                        backgroundColor: 'var(--accent)',
                        borderRadius: '4px'
                      }}
                    ></div>
                  </div>
                </div>

                {/* Social */}
                <div>
                  <div className="flex justify-between mb-1">
                    <span style={{ fontSize: '14px', fontWeight: '500' }}>Social Media</span>
                    <span style={{ fontSize: '14px', fontWeight: '600' }}>{analyticsData.customerAcquisition.social}%</span>
                  </div>
                  <div style={{
                    height: '8px',
                    width: '100%',
                    backgroundColor: 'var(--light-gray)',
                    borderRadius: '4px',
                    overflow: 'hidden'
                  }}>
                    <div
                      style={{
                        height: '100%',
                        width: `${analyticsData.customerAcquisition.social}%`,
                        backgroundColor: '#9333ea',
                        borderRadius: '4px'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
