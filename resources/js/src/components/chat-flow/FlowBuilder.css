/* FlowBuilder.css */

/* Animation for node connections */
@keyframes pulse-success {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

@keyframes pulse-error {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Connection feedback styles */
.connection-success {
  animation: pulse-success 0.5s ease-out;
}

.connection-error {
  animation: pulse-error 0.5s ease-out;
}

/* Handle styles */
.react-flow__handle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #25D366;
  border: 2px solid white;
  transition: all 0.2s ease;
  z-index: 10;
}

.react-flow__handle:hover {
  transform: scale(1.3);
  background-color: #1ea855;
  box-shadow: 0 0 0 4px rgba(37, 211, 102, 0.3);
}

/* Custom socket handle styles */
.socket-handle {
  cursor: crosshair;
  opacity: 0.9;
}

.socket-handle:hover {
  opacity: 1;
}

/* Add extended connection areas */
.react-flow__node .react-flow__handle-right::after {
  content: '';
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background-color: transparent;
  cursor: crosshair;
}

.react-flow__node .react-flow__handle-left::after {
  content: '';
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background-color: transparent;
  cursor: crosshair;
}

/* Pulse animation for handles on hover */
@keyframes pulse-handle {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.react-flow__node:hover .react-flow__handle {
  animation: pulse-handle 1.5s infinite;
}

/* Edge styles */
.react-flow__edge-path {
  stroke-width: 2;
  stroke: #25D366;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #1ea855;
  stroke-width: 3;
}

/* Node styles */
.react-flow__node {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.react-flow__node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #25D366;
}

/* Add visual indicators for connection points */
.react-flow__node::before,
.react-flow__node::after {
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* Right side indicator (for source connections) */
.react-flow__node::after {
  content: 'Drag from here →';
  position: absolute;
  right: -110px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Left side indicator (for target connections) */
.react-flow__node::before {
  content: '← Connect here';
  position: absolute;
  left: -110px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Show indicators on hover */
.react-flow__node:hover::before,
.react-flow__node:hover::after {
  opacity: 1;
}

/* Controls styles */
.react-flow__controls {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.react-flow__controls-button {
  background-color: white;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px;
  color: #666;
}

.react-flow__controls-button:hover {
  background-color: #f9f9f9;
}

/* Background styles */
.react-flow__background {
  background-color: #f9fafb;
}

/* Animation for edge path */
@keyframes dash {
  from {
    stroke-dashoffset: 24;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.react-flow__edge-path.animated {
  stroke-dasharray: 5, 5;
  animation: dash 1s linear infinite;
}

/* Sidebar animation */
@keyframes slide-in-right {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

/* Chat Preview Styles */
.chat-bubble {
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
  max-width: 80%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-bubble-sent {
  background-color: #dcf8c6;
  margin-left: auto;
  border-top-right-radius: 0;
}

.chat-bubble-received {
  background-color: white;
  margin-right: auto;
  border-top-left-radius: 0;
}

.whatsapp-header {
  background-color: #075E54;
  color: white;
  padding: 10px 16px;
  display: flex;
  align-items: center;
}

.whatsapp-input {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 24px;
  padding: 8px 12px;
}

/* Mobile Phone Frame Styles */
.mobile-frame {
  width: 280px;
  height: 560px;
  background-color: white;
  border-radius: 28px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 8px solid #111827;
  margin: 0 auto;

  /* Phone notch */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 20px;
    background-color: #111827;
    border-bottom-left-radius: 14px;
    border-bottom-right-radius: 14px;
    z-index: 10;
  }
}

.phone-content {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Status Bar */
.status-bar {
  height: 24px;
  background-color: #075E54;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  color: white;
  font-size: 10px;
}

.status-bar-time {
  font-weight: 500;
}

.status-bar-icons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery-icon::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 6px;
  background-color: white;
  border-radius: 1px;
  margin-left: 2px;
  position: relative;
  top: 1px;
}

.signal-icon::before {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17 4h3a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1zm-6 4h3a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1zM5 12h3a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6a1 1 0 0 1 1-1z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.wifi-icon::before {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 18c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1zm-4.07-1.59l.83.84c.39.39.9.59 1.41.59h3.66c.51 0 1.02-.2 1.41-.59l.83-.84c.2-.2.2-.51 0-.71-.2-.2-.51-.2-.71 0l-.83.83c-.2.2-.46.3-.71.3h-3.66c-.25 0-.51-.1-.71-.3l-.83-.83c-.2-.2-.51-.2-.71 0-.19.2-.19.52.02.71zm2.83-4.24l1.24 1.24 1.24-1.24c.2-.2.2-.51 0-.71-.2-.2-.51-.2-.71 0L12 12.25l-.53-.53c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1 .98zm4.12-3.54l.2.2c.2.2.51.2.71 0 .2-.2.2-.51 0-.71l-.2-.2c-.98-.98-2.56-.98-3.54 0l-.2.2c-.2.2-.2.51 0 .71.2.2.51.2.71 0l.2-.2c.58-.58 1.54-.58 2.12 0zm1.41-1.41l.2.2c.2.2.51.2.71 0 .2-.2.2-.51 0-.71l-.2-.2c-.98-.98-2.27-1.52-3.66-1.52-1.39 0-2.68.54-3.66 1.52l-.2.2c-.2.2-.2.51 0 .71.2.2.51.2.71 0l.2-.2c.78-.78 1.81-1.21 2.95-1.21 1.14 0 2.17.43 2.95 1.21z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

/* Home Indicator */
.home-indicator {
  height: 4px;
  width: 80px;
  background-color: #000;
  border-radius: 2px;
  margin: 6px auto;
  opacity: 0.2;
}

/* WhatsApp specific styles */
.bg-whatsapp-teal {
  background-color: #075E54;
}

.text-whatsapp-default {
  color: #00a884;
}

/* Adjust button styles to match WhatsApp */
.whatsapp-button {
  background-color: #dcf8c6;
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  margin-right: 8px;
}
