import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  UsersIcon,
  BuildingStorefrontIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  CreditCardIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminDashboard() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          stats: {
            vendorCount: 120,
            userCount: 580,
            chatFlowCount: 924,
            totalRevenue: '₹12,430',
          },
          recentActivities: [
            { id: 1, type: 'vendor_registration', user: '<PERSON><PERSON>', time: '1 hour ago' },
            { id: 2, type: 'subscription_purchase', user: '<PERSON><PERSON> <PERSON>', plan: 'Gold', time: '3 hours ago' },
            { id: 3, type: 'store_verification', user: 'Amit Kumar', time: '5 hours ago' },
            { id: 4, type: 'account_blocked', user: 'Vijay Singh', time: '1 day ago' },
            { id: 5, type: 'template_added', template: 'Food Menu', time: '2 days ago' },
          ]
        };

        setDashboardData(mockData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Define stats with real data or fallbacks
  const stats = [
    {
      name: 'Total Vendors',
      stat: dashboardData?.stats?.vendorCount || '0',
      icon: BuildingStorefrontIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Active vendors on platform'
    },
    {
      name: 'Total Users',
      stat: dashboardData?.stats?.userCount || '0',
      icon: UsersIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Registered users'
    },
    {
      name: 'Active Chat Flows',
      stat: dashboardData?.stats?.chatFlowCount || '0',
      icon: ChatBubbleLeftRightIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Total chat flows created'
    },
    {
      name: 'Total Revenue',
      stat: dashboardData?.stats?.totalRevenue || '₹0.00',
      icon: CreditCardIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Platform earnings'
    },
  ];

  // Get recent activities
  const recentActivities = dashboardData?.recentActivities || [];

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Admin Dashboard</div>
          <h1 className="welcome-title">
            Welcome back, <span style={{ color: '#25D366' }}>{currentUser?.name || 'Admin'}</span>
          </h1>
          <p className="welcome-subtitle">
            Manage your platform, vendors, and content from this central dashboard. Monitor performance and make important administrative decisions.
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Platform Overview</h2>
        </div>
        <div className="stats-grid">
          {stats.map((item) => (
            <div key={item.name} className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
              </div>
              <div className="stat-content">
                <h3 className="stat-label">{item.name}</h3>
                <p className="stat-value">{item.stat}</p>
                <p className="stat-description">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="orders-section">
        <div className="section-header">
          <h2 className="section-title">Recent Activity</h2>
          <Link to="/admin/activity" className="btn btn-outline">
            View all activity
            <ArrowRightIcon style={{ width: '16px', height: '16px' }} />
          </Link>
        </div>
        <div className="card">
          {recentActivities.length > 0 ? (
            <ul className="orders-list">
              {recentActivities.map((activity) => (
                <li key={activity.id} className="order-item">
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div>
                      <p style={{ fontSize: '16px', fontWeight: '600', color: '#212529', margin: '0 0 4px 0' }}>
                        {activity.type === 'vendor_registration' && 'New Vendor Registration'}
                        {activity.type === 'subscription_purchase' && 'Subscription Purchase'}
                        {activity.type === 'store_verification' && 'Store Verification'}
                        {activity.type === 'account_blocked' && 'Account Blocked'}
                        {activity.type === 'template_added' && 'Template Added'}
                      </p>
                      <p style={{ fontSize: '14px', color: '#6C757D', margin: '0' }}>
                        {activity.user && `${activity.user}`}
                        {activity.plan && ` - ${activity.plan} Plan`}
                        {activity.template && ` - ${activity.template}`}
                      </p>
                    </div>
                    <div>
                      <span style={{ fontSize: '14px', color: '#6C757D' }}>{activity.time}</span>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div style={{ padding: '24px', textAlign: 'center' }}>
              <p>No recent activity found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Actions</h2>
        </div>
        <div className="stats-grid">
          <Link to="/admin/vendors" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <BuildingStorefrontIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Manage Vendors</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                View, verify, or block vendor accounts
              </p>
            </div>
          </Link>

          <Link to="/admin/subscriptions" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <CreditCardIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Manage Subscriptions</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Edit subscription plans and pricing
              </p>
            </div>
          </Link>

          <Link to="/admin/message-templates" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <ChatBubbleLeftRightIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Message Templates</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Add or edit message templates for vendors
              </p>
            </div>
          </Link>

          <Link to="/admin/academy" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <AcademicCapIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Academy Content</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Manage tutorial videos and guides
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
