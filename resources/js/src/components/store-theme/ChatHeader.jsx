import React, { useState, useEffect } from 'react';
import { ArrowLeftIcon, EllipsisVerticalIcon, PhoneIcon } from '@heroicons/react/24/outline';
import VerificationBadge from './VerificationBadge';
import InlineLogo from './InlineLogo';

// Default logo URL
const DEFAULT_LOGO_URL = '/store-logo.png';

/**
 * ChatHeader component
 * Displays the store header with back button, logo, store name, verification badge, and menu
 */
const ChatHeader = ({ storeName, storeLogoUrl, isVerified }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [logoUrl, setLogoUrl] = useState(null);
  const [useInlineLogo, setUseInlineLogo] = useState(true);
  const menuRef = React.useRef(null);

  // Process props

  // Handle logo URL
  useEffect(() => {
    if (!storeLogoUrl) {
      setUseInlineLogo(true);
      return;
    }

    // If the URL already includes the server prefix, use it directly
    if (storeLogoUrl.includes('https://api.whamart.shop')) {
      setLogoUrl(storeLogoUrl);
      setUseInlineLogo(false);
      return;
    }

    // For relative paths, always add the server URL prefix
    // The server stores logo URLs in format: /uploads/logos/filename.jpg
    if (storeLogoUrl.startsWith('/')) {
      const fullUrl = `https://api.whamart.shop${storeLogoUrl}`;
      setLogoUrl(fullUrl);
      setUseInlineLogo(false);
      return;
    }

    // For paths that don't start with '/' but should be relative to the server
    if (!storeLogoUrl.startsWith('http') && !storeLogoUrl.startsWith('/')) {
      // Check if it's an uploads path that's missing the leading slash
      if (storeLogoUrl.includes('uploads/')) {
        const fullUrl = `https://api.whamart.shop/${storeLogoUrl}`;
        setLogoUrl(fullUrl);
        setUseInlineLogo(false);
        return;
      }

      // For other paths, try with the uploads/logos prefix
      const fullUrl = `https://api.whamart.shop/uploads/logos/${storeLogoUrl}`;
      setLogoUrl(fullUrl);
      setUseInlineLogo(false);
      return;
    }

    // For any other URL format, use as is
    setLogoUrl(storeLogoUrl);
    setUseInlineLogo(false);
  }, [storeLogoUrl]);

  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };

  // Close menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuRef]);

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      padding: '12px 12px',
      backgroundColor: 'white',
      color: 'black',
      height: '64px',
      zIndex: 10,
      position: 'relative',
      overflow: 'visible', // Changed from 'hidden' to allow dropdown to be visible
      borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }}>
      {/* Back button */}
      <button style={{
        background: 'none',
        border: 'none',
        color: 'black',
        cursor: 'pointer',
        padding: '2px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
        width: '28px',
        height: '28px',
        marginRight: '4px'
      }}>
        <ArrowLeftIcon className="w-5 h-5" />
      </button>

      {/* Store info */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        flex: 1,
        marginLeft: '2px',
        height: '100%'
      }}>
        <div style={{
          width: '30px',
          height: '30px',
          borderRadius: '50%',
          overflow: 'hidden',
          backgroundColor: '#f0f0f0',
          flexShrink: 0,
          marginRight: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {useInlineLogo ? (
            <InlineLogo size={30} color="#25D366" />
          ) : (
            <img
              src={logoUrl}
              alt={storeName}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
              onError={(e) => {
                // Try different URL formats as a last resort
                if (logoUrl) {
                  // Already tried with https://api.whamart.shop prefix
                  if (logoUrl.includes('https://api.whamart.shop')) {
                    // Try without the prefix
                    const pathOnly = logoUrl.replace('https://api.whamart.shop', '');
                    if (pathOnly && pathOnly !== logoUrl) {
                      e.target.src = pathOnly;
                      return;
                    }
                  }
                  // Try with the server prefix if not already tried
                  else if (!logoUrl.includes('https://api.whamart.shop')) {
                    // For paths with leading slash
                    if (logoUrl.startsWith('/')) {
                      const fullUrl = `https://api.whamart.shop${logoUrl}`;
                      e.target.src = fullUrl;
                      return;
                    }
                    // For paths without leading slash
                    else if (!logoUrl.startsWith('http')) {
                      // Try with direct uploads/logos path
                      const directLogoUrl = `https://api.whamart.shop/uploads/logos/${logoUrl.split('/').pop()}`;
                      e.target.src = directLogoUrl;
                      return;
                    }
                  }

                  // Try one more time with just the filename
                  const filename = logoUrl.split('/').pop();
                  if (filename) {
                    const lastAttemptUrl = `https://api.whamart.shop/uploads/logos/${filename}`;
                    e.target.src = lastAttemptUrl;

                    // Set a timeout to check if this final attempt worked
                    setTimeout(() => {
                      if (e.target.naturalWidth === 0) {
                        setUseInlineLogo(true);
                      }
                    }, 1000);
                    return;
                  }
                }

                // If all attempts fail, use the inline logo
                setUseInlineLogo(true);
              }}
            />
          )}
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginLeft: '0'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            height: '32px'
          }}>
            <span style={{
              fontWeight: 600,
              fontSize: '16px',
              lineHeight: '30px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '240px',
              color: '#000000',
              display: 'inline-block',
              verticalAlign: 'middle'
            }}>{storeName}</span>
            {isVerified && <VerificationBadge />}
          </div>
        </div>
      </div>

      {/* Call button */}
      <button style={{
        background: 'none',
        border: 'none',
        color: 'black',
        cursor: 'pointer',
        padding: '2px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
        width: '28px',
        height: '28px',
        marginLeft: '4px'
      }}>
        <PhoneIcon className="w-5 h-5" />
      </button>

      {/* Menu button with dropdown */}
      <div ref={menuRef} style={{ position: 'relative' }}>
        <button
          onClick={toggleMenu}
          style={{
            background: 'none',
            border: 'none',
            color: 'black',
            cursor: 'pointer',
            padding: '2px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0,
            width: '28px',
            height: '28px',
            marginLeft: '4px'
          }}
        >
          <EllipsisVerticalIcon className="w-5 h-5" />
        </button>

        {/* Dropdown menu */}
        {showMenu && (
          <div style={{
            position: 'absolute',
            top: '40px',
            right: '0',
            backgroundColor: 'white',
            borderRadius: '4px',
            boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
            width: '180px',
            zIndex: 20,
            overflow: 'hidden'
          }}>
            <ul style={{
              listStyle: 'none',
              margin: 0,
              padding: 0
            }}>
              <li>
                <button style={{
                  display: 'block',
                  width: '100%',
                  textAlign: 'left',
                  padding: '12px 16px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#000',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  Store Info
                </button>
              </li>
              <li>
                <button style={{
                  display: 'block',
                  width: '100%',
                  textAlign: 'left',
                  padding: '12px 16px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#000',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  Visit Store
                </button>
              </li>
              <li>
                <button style={{
                  display: 'block',
                  width: '100%',
                  textAlign: 'left',
                  padding: '12px 16px',
                  backgroundColor: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#000'
                }}>
                  Create Your Store
                </button>
              </li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatHeader;
