<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vendor1 = \App\Models\User::where('email', '<EMAIL>')->first();
        $vendor2 = \App\Models\User::where('email', '<EMAIL>')->first();

        if ($vendor1) {
            \App\Models\Store::create([
                'user_id' => $vendor1->id,
                'name' => 'Vendor One\'s Gadget Store',
                'description' => 'The best place for all your gadget needs.',
            ]);
        }

        if ($vendor2) {
            \App\Models\Store::create([
                'user_id' => $vendor2->id,
                'name' => 'Vendor Two\'s Fashion Hub',
                'description' => 'Latest trends in fashion and apparel.',
            ]);
        }
    }
}
