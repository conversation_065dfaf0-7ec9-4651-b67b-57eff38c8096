<?php

namespace App\Http\Controllers;

use App\Models\Store;
use Illuminate\Http\Request;

class PublicStoreController extends Controller
{
    /**
     * Get a store by its public URL.
     */
    public function getStoreByUrl(string $storeUrl)
    {
        $store = Store::where('storeUrl', $storeUrl)->where('isVerified', true)->with('products')->first();

        if (!$store) {
            return response()->json(['message' => 'Store not found or not verified.'], 404);
        }

        // Optionally, you can also load products or other relationships here
        // $store->load('products');

        return response()->json($store);
    }
}
