<?php

namespace App\Helpers;

use Carbon\Carbon;

class OrderHelper
{
    /**
     * Generate a unique order number in the format WM-YYYYMMDD-XXXX.
     *
     * @return string
     */
    public static function generateOrderNumber(): string
    {
        $date = Carbon::now();
        $year = $date->year;
        $month = str_pad($date->month, 2, '0', STR_PAD_LEFT);
        $day = str_pad($date->day, 2, '0', STR_PAD_LEFT);
        $random = mt_rand(1000, 9999); // 4-digit random number

        return "WM-{$year}{$month}{$day}-{$random}";
    }
}
