import { useState, useEffect } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  PlusIcon,
  CheckIcon,
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  PhoneIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminMessageTemplates() {
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [isAddingTemplate, setIsAddingTemplate] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    content: '',
    header: '',
    footer: '',
    image: '',
    buttons: []
  });
  const [newButton, setNewButton] = useState({
    type: 'quick_reply',
    text: '',
    value: '',
    url: '',
    phoneNumber: ''
  });

  // Fetch templates and categories
  useEffect(() => {
    const fetchTemplatesAndCategories = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockCategories = [
          { id: 'retail', name: 'Retail & E-commerce' },
          { id: 'food', name: 'Food & Restaurants' },
          { id: 'services', name: 'Services' },
          { id: 'education', name: 'Education' }
        ];
        
        const mockTemplates = [
          {
            id: 1,
            category: 'retail',
            name: 'Welcome Message',
            description: 'Initial greeting for new customers',
            content: 'Welcome to our store! How can I help you today?',
            buttons: [
              { type: 'quick_reply', text: 'View Products', value: 'products' },
              { type: 'quick_reply', text: 'Check Order', value: 'order' },
              { type: 'call', text: 'Call Support', phoneNumber: '+91123456789' }
            ],
            header: 'Welcome',
            footer: 'Reply to continue'
          },
          {
            id: 2,
            category: 'retail',
            name: 'Product Catalog',
            description: 'Show product categories',
            content: 'Here are our product categories. Please select one to browse:',
            buttons: [
              { type: 'quick_reply', text: 'Clothing', value: 'clothing' },
              { type: 'quick_reply', text: 'Electronics', value: 'electronics' },
              { type: 'quick_reply', text: 'Home Goods', value: 'home' }
            ]
          },
          {
            id: 3,
            category: 'food',
            name: 'Restaurant Menu',
            description: 'Show restaurant menu categories',
            content: "Here's our menu. What would you like to order today?",
            buttons: [
              { type: 'quick_reply', text: 'Starters', value: 'starters' },
              { type: 'quick_reply', text: 'Main Course', value: 'main' },
              { type: 'quick_reply', text: 'Desserts', value: 'desserts' }
            ],
            header: 'Our Menu'
          },
          {
            id: 4,
            category: 'services',
            name: 'Appointment Booking',
            description: 'Help customers book appointments',
            content: 'Would you like to book an appointment with us?',
            buttons: [
              { type: 'quick_reply', text: 'Book Now', value: 'book' },
              { type: 'quick_reply', text: 'View Availability', value: 'availability' },
              { type: 'url', text: 'Visit Website', url: 'https://example.com/booking' }
            ]
          }
        ];
        
        setCategories(mockCategories);
        setTemplates(mockTemplates);
      } catch (error) {
        console.error('Error fetching templates:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplatesAndCategories();
  }, []);

  // Filter templates based on selected category
  const filteredTemplates = activeCategory === 'all'
    ? templates
    : templates.filter(template => template.category === activeCategory);

  // Handle edit template
  const handleEditTemplate = (template) => {
    setEditingTemplate(template.id);
    setFormData({
      name: template.name,
      description: template.description,
      category: template.category,
      content: template.content,
      header: template.header || '',
      footer: template.footer || '',
      image: template.image || '',
      buttons: [...(template.buttons || [])]
    });
  };

  // Handle add new template
  const handleAddTemplate = () => {
    setIsAddingTemplate(true);
    setFormData({
      name: '',
      description: '',
      category: categories.length > 0 ? categories[0].id : '',
      content: '',
      header: '',
      footer: '',
      image: '',
      buttons: []
    });
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle button input change
  const handleButtonInputChange = (e) => {
    const { name, value } = e.target;
    setNewButton({
      ...newButton,
      [name]: value
    });
  };

  // Handle adding a button
  const handleAddButton = () => {
    if (newButton.text.trim()) {
      setFormData({
        ...formData,
        buttons: [...formData.buttons, { ...newButton }]
      });
      setNewButton({
        type: 'quick_reply',
        text: '',
        value: '',
        url: '',
        phoneNumber: ''
      });
    }
  };

  // Handle removing a button
  const handleRemoveButton = (index) => {
    const updatedButtons = [...formData.buttons];
    updatedButtons.splice(index, 1);
    setFormData({
      ...formData,
      buttons: updatedButtons
    });
  };

  // Handle save template
  const handleSaveTemplate = () => {
    // In a real implementation, this would be an API call
    if (isAddingTemplate) {
      // Add new template
      const newTemplate = {
        id: Date.now(), // Temporary ID for mock data
        ...formData
      };
      setTemplates([...templates, newTemplate]);
    } else {
      // Update existing template
      const updatedTemplates = templates.map(template => 
        template.id === editingTemplate ? { ...template, ...formData } : template
      );
      setTemplates(updatedTemplates);
    }
    
    // Reset form
    setEditingTemplate(null);
    setIsAddingTemplate(false);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingTemplate(null);
    setIsAddingTemplate(false);
  };

  // Handle delete template
  const handleDeleteTemplate = (templateId) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      // In a real implementation, this would be an API call
      const updatedTemplates = templates.filter(template => template.id !== templateId);
      setTemplates(updatedTemplates);
    }
  };

  // Handle preview template
  const handlePreviewTemplate = (template) => {
    setPreviewTemplate(template);
  };

  // Get button icon based on type
  const getButtonIcon = (type) => {
    switch (type) {
      case 'quick_reply':
        return <ArrowPathIcon className="w-4 h-4 mr-2" />;
      case 'url':
        return <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />;
      case 'call':
        return <PhoneIcon className="w-4 h-4 mr-2" />;
      default:
        return null;
    }
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Template Management</div>
          <h1 className="welcome-title">
            Message <span style={{ color: '#25D366' }}>Templates</span>
          </h1>
          <p className="welcome-subtitle">
            Create and manage message templates for vendors to use in their chat flows. Organize templates by category and customize them for different business types.
          </p>
        </div>
      </div>

      {/* Category Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Template Categories</h2>
        </div>
        <div className="flex flex-wrap gap-2 mb-6">
          <button
            className={`btn ${activeCategory === 'all' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => setActiveCategory('all')}
          >
            All Templates
          </button>
          {categories.map(category => (
            <button
              key={category.id}
              className={`btn ${activeCategory === category.id ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Templates Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Message Templates</h2>
          <button 
            className="btn btn-primary"
            onClick={handleAddTemplate}
            disabled={isAddingTemplate || editingTemplate !== null}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Add New Template
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading templates...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map(template => (
              <div key={template.id} className="card" style={{ padding: '0', overflow: 'hidden' }}>
                <div className="p-4 border-b">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-gray-900">{template.name}</h3>
                      <p className="text-sm text-gray-500">{template.description}</p>
                      <span className="mt-1 inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {categories.find(c => c.id === template.category)?.name || template.category}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePreviewTemplate(template)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <ArrowTopRightOnSquareIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleEditTemplate(template)}
                        className="text-indigo-600 hover:text-indigo-900"
                        disabled={editingTemplate !== null || isAddingTemplate}
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteTemplate(template.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={editingTemplate !== null || isAddingTemplate}
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-gray-50">
                  <div className="whatsapp-message-preview">
                    {template.header && (
                      <div className="message-header">{template.header}</div>
                    )}
                    <div className="message-content">{template.content}</div>
                    {template.footer && (
                      <div className="message-footer">{template.footer}</div>
                    )}
                    {template.buttons && template.buttons.length > 0 && (
                      <div className="message-buttons">
                        {template.buttons.map((button, index) => (
                          <div key={index} className="message-button">
                            <div className="flex items-center">
                              {getButtonIcon(button.type)}
                              <span>{button.text}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Edit/Add Template Form */}
      {(editingTemplate !== null || isAddingTemplate) && (
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">{isAddingTemplate ? 'Add New Template' : 'Edit Template'}</h2>
          </div>
          <div className="card" style={{ padding: '24px' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. Welcome Message"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  required
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Brief description of the template"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Header (Optional)
                </label>
                <input
                  type="text"
                  name="header"
                  value={formData.header}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Message header"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Footer (Optional)
                </label>
                <input
                  type="text"
                  name="footer"
                  value={formData.footer}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Message footer"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Content
                </label>
                <textarea
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  rows="4"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Message content"
                  required
                ></textarea>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Image URL (Optional)
                </label>
                <input
                  type="text"
                  name="image"
                  value={formData.image}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="https://example.com/image.jpg"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buttons
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Button Type
                    </label>
                    <select
                      name="type"
                      value={newButton.type}
                      onChange={handleButtonInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="quick_reply">Quick Reply</option>
                      <option value="url">URL</option>
                      <option value="call">Call</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Button Text
                    </label>
                    <input
                      type="text"
                      name="text"
                      value={newButton.text}
                      onChange={handleButtonInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="e.g. View Products"
                    />
                  </div>
                  {newButton.type === 'quick_reply' && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Value
                      </label>
                      <input
                        type="text"
                        name="value"
                        value={newButton.value}
                        onChange={handleButtonInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="e.g. products"
                      />
                    </div>
                  )}
                  {newButton.type === 'url' && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        URL
                      </label>
                      <input
                        type="text"
                        name="url"
                        value={newButton.url}
                        onChange={handleButtonInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="https://example.com"
                      />
                    </div>
                  )}
                  {newButton.type === 'call' && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <input
                        type="text"
                        name="phoneNumber"
                        value={newButton.phoneNumber}
                        onChange={handleButtonInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="+91123456789"
                      />
                    </div>
                  )}
                </div>
                <div className="flex justify-end mb-4">
                  <button
                    type="button"
                    onClick={handleAddButton}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Add Button
                  </button>
                </div>
                <div className="space-y-2">
                  {formData.buttons.map((button, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <div className="flex items-center">
                        {getButtonIcon(button.type)}
                        <span className="font-medium">{button.text}</span>
                        <span className="ml-2 text-sm text-gray-500">
                          {button.type === 'quick_reply' && `(Value: ${button.value})`}
                          {button.type === 'url' && `(URL: ${button.url})`}
                          {button.type === 'call' && `(Phone: ${button.phoneNumber})`}
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveButton(index)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSaveTemplate}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {isAddingTemplate ? 'Add Template' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Template Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">{previewTemplate.name}</h3>
              <button
                onClick={() => setPreviewTemplate(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="whatsapp-message-preview">
              {previewTemplate.header && (
                <div className="message-header">{previewTemplate.header}</div>
              )}
              {previewTemplate.image && (
                <div className="message-image">
                  <img src={previewTemplate.image} alt="Message" />
                </div>
              )}
              <div className="message-content">{previewTemplate.content}</div>
              {previewTemplate.footer && (
                <div className="message-footer">{previewTemplate.footer}</div>
              )}
              {previewTemplate.buttons && previewTemplate.buttons.length > 0 && (
                <div className="message-buttons">
                  {previewTemplate.buttons.map((button, index) => (
                    <div key={index} className="message-button">
                      <div className="flex items-center">
                        {getButtonIcon(button.type)}
                        <span>{button.text}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
