import React, { useState, useEffect } from 'react';
import { XMarkIcon, PhotoIcon, PlusIcon, EyeIcon } from '@heroicons/react/24/outline';
import WhatsAppTemplatePreview from './WhatsAppTemplatePreview';

/**
 * MessageEditor component for the Chat Flow Builder
 * Provides a side panel for editing message properties
 */
const MessageEditor = ({ message, isOpen, onClose, onSave }) => {
  // State for message properties
  const [header, setHeader] = useState('');
  const [content, setContent] = useState('');
  const [footer, setFooter] = useState('');
  const [buttons, setButtons] = useState([]);
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [handleProductSelection, setHandleProductSelection] = useState(false);

  // Initialize form with message data when opened
  useEffect(() => {
    if (message && isOpen) {
      setHeader(message.header || '');
      setContent(message.content || '');
      setFooter(message.footer || '');
      setButtons(message.buttons || []);

      // Handle image data properly
      let imageData = null;
      let imagePreviewData = '';

      if (message.image) {
        if (typeof message.image === 'string') {
          // If it's already a string (likely a data URL), use it directly
          imageData = message.image;
          imagePreviewData = message.image;
        } else if (typeof message.image === 'object') {
          // Try to extract image data from object
          try {
            if (message.image.data) {
              imageData = message.image.data;
              imagePreviewData = message.image.data;
            } else if (message.image.src) {
              imageData = message.image.src;
              imagePreviewData = message.image.src;
            } else if (message.image.url) {
              imageData = message.image.url;
              imagePreviewData = message.image.url;
            } else {
              // Try to extract from JSON string
              const jsonString = JSON.stringify(message.image);
              const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
              if (dataUrlMatch && dataUrlMatch[1]) {
                imageData = dataUrlMatch[1];
                imagePreviewData = dataUrlMatch[1];
              }
            }
          } catch (err) {
            // Silent error handling
          }
        }
      }

      setImage(imageData);
      setImagePreview(imagePreviewData);
      setHandleProductSelection(message.handleProductSelection || false);
    }
  }, [message, isOpen]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate content
    if (!content.trim()) {
      alert('Message content is required');
      return;
    }

    // Ensure image is saved as a string
    let processedImage = image;

    // If image is null or undefined, keep it that way
    if (!image) {
      processedImage = null;
    }
    // If image is already a string and starts with data:image/, it's good to go
    else if (typeof image === 'string' && image.startsWith('data:image/')) {
      processedImage = image;
    }
    // If image is an object, try to convert it
    else if (typeof image === 'object') {
      try {
        // Try to extract from common object properties
        if (image.data && typeof image.data === 'string' && image.data.startsWith('data:image/')) {
          processedImage = image.data;
        }
        else if (image.src && typeof image.src === 'string' && image.src.startsWith('data:image/')) {
          processedImage = image.src;
        }
        else if (image.url && typeof image.url === 'string' && image.url.startsWith('data:image/')) {
          processedImage = image.url;
        }
        // Try toString if it's not the default Object.toString
        else if (image.toString && image.toString() !== '[object Object]' &&
                 image.toString().startsWith('data:image/')) {
          processedImage = image.toString();
        }
        // Try to extract from JSON string
        else {
          const jsonString = JSON.stringify(image);

          const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
          if (dataUrlMatch && dataUrlMatch[1]) {
            processedImage = dataUrlMatch[1];
          }
          // Try a more flexible regex
          else {
            const flexMatch = jsonString.match(/data:image\/[^"',}\]]+/);
            if (flexMatch && flexMatch[0]) {
              processedImage = flexMatch[0];
            }
            // If all else fails, use the image preview which should be a string
            else if (imagePreview && typeof imagePreview === 'string' &&
                     imagePreview.startsWith('data:image/')) {
              processedImage = imagePreview;
            }
            // If we still don't have a valid image, set to null
            else {
              processedImage = null;
            }
          }
        }
      } catch (err) {
        // If imagePreview is valid, use it as fallback
        if (imagePreview && typeof imagePreview === 'string' &&
            imagePreview.startsWith('data:image/')) {
          processedImage = imagePreview;
        } else {
          processedImage = null;
        }
      }
    }
    // If image is a string but not a data URL, set to null
    else if (typeof image === 'string' && !image.startsWith('data:image/')) {
      processedImage = null;
    }

    // Final validation - ensure it's either null or a valid data URL string
    if (processedImage !== null &&
        (typeof processedImage !== 'string' || !processedImage.startsWith('data:image/'))) {
      processedImage = null;
    }

    // Save changes
    onSave({
      ...message,
      header,
      content,
      footer,
      buttons,
      image: processedImage,
      handleProductSelection
    });
  };

  // Handle button changes
  const handleButtonTextChange = (index, value) => {
    const updatedButtons = [...buttons];
    updatedButtons[index] = { ...updatedButtons[index], text: value };
    setButtons(updatedButtons);
  };

  // Handle button type changes
  const handleButtonTypeChange = (index, type) => {
    const updatedButtons = [...buttons];

    // Create a new button with the selected type and appropriate properties
    let newButton = { type, text: updatedButtons[index].text };

    if (type === 'quick_reply') {
      newButton.value = updatedButtons[index].value || 'reply_value';
    } else if (type === 'url') {
      newButton.url = updatedButtons[index].url || 'https://';
    } else if (type === 'call') {
      newButton.phoneNumber = updatedButtons[index].phoneNumber || '+91';
    }

    updatedButtons[index] = newButton;
    setButtons(updatedButtons);
  };

  // Handle button value changes (url, phone number, or reply value)
  const handleButtonValueChange = (index, field, value) => {
    const updatedButtons = [...buttons];
    updatedButtons[index] = { ...updatedButtons[index], [field]: value };
    setButtons(updatedButtons);
  };

  // Add a new button
  const handleAddButton = (type) => {
    if (buttons.length >= 3) {
      alert('Maximum 3 buttons allowed per message');
      return;
    }

    const newButton = type === 'quick_reply'
      ? { type, text: 'Quick Reply', value: 'reply_value' }
      : type === 'url'
        ? { type, text: 'Visit Website', url: 'https://' }
        : { type, text: 'Call Us', phoneNumber: '+91' };

    setButtons([...buttons, newButton]);
  };

  // Remove a button
  const handleRemoveButton = (index) => {
    const updatedButtons = [...buttons];
    updatedButtons.splice(index, 1);
    setButtons(updatedButtons);
  };

  // Handle image upload with compression
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.type.match('image.*')) {
      alert('Please select an image file');
      return;
    }

    // Increased max size to 2MB
    if (file.size > 2 * 1024 * 1024) {
      alert('Image size should be less than 2MB');
      return;
    }

    // Function to compress image
    const compressImage = (imageFile, maxWidth = 800, quality = 0.7) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(imageFile);
        reader.onload = (event) => {
          const img = new Image();
          img.src = event.target.result;

          img.onload = () => {
            // Create canvas for resizing
            const canvas = document.createElement('canvas');
            let width = img.width;
            let height = img.height;

            // Calculate new dimensions if needed
            if (width > maxWidth) {
              height = Math.round((height * maxWidth) / width);
              width = maxWidth;
            }

            // Set canvas dimensions and draw image
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to data URL with compression
            const dataUrl = canvas.toDataURL(imageFile.type, quality);

            resolve(dataUrl);
          };

          img.onerror = (error) => {
            reject(error);
          };
        };
        reader.onerror = (error) => {
          reject(error);
        };
      });
    };

    // Compress and process the image
    compressImage(file)
      .then(compressedDataUrl => {
        setImagePreview(compressedDataUrl);
        setImage(compressedDataUrl);

        // Verify the image data is valid
        try {
          // Test if the image can be loaded
          const img = new Image();
          img.onload = () => {
            // Image loaded successfully
          };
          img.onerror = () => {
            alert('The selected image could not be processed. Please try another image.');
            setImage(null);
            setImagePreview(null);
          };
          img.src = compressedDataUrl;
        } catch (err) {
          // Silent error handling
        }
      })
      .catch(error => {
        alert('There was an error processing the image. Please try another image.');
        setImage(null);
        setImagePreview(null);
      });
  };

  // Remove image
  const handleRemoveImage = () => {
    setImage(null);
    setImagePreview('');
  };

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-y-0 right-0 z-[9999] flex">
      {/* Overlay for mobile - only visible on small screens */}
      <div
        className="fixed inset-0 bg-black bg-opacity-30 lg:hidden"
        onClick={onClose}
      ></div>

      {/* Side panel */}
      <div className="bg-white shadow-xl w-full max-w-md h-full overflow-y-auto relative animate-slide-in-right">
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between z-10">
          <h2 className="text-lg font-medium text-gray-900">Edit Message</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* Header */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Header (Optional)
            </label>
            <input
              type="text"
              value={header}
              onChange={(e) => setHeader(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Message header"
            />
          </div>

          {/* Content */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Content <span className="text-red-500">*</span>
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Message content"
              rows={4}
              required
            />
          </div>

          {/* Footer */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Footer (Optional)
            </label>
            <input
              type="text"
              value={footer}
              onChange={(e) => setFooter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              placeholder="Message footer"
            />
          </div>

          {/* Product Selection Handler */}
          <div className="mb-4">
            <div className="flex items-center">
              <input
                id="handle-product-selection"
                type="checkbox"
                checked={handleProductSelection}
                onChange={(e) => setHandleProductSelection(e.target.checked)}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="handle-product-selection" className="ml-2 block text-sm text-gray-700">
                Handle Product Selection (This message will be shown when a product is selected)
              </label>
            </div>
            {handleProductSelection && (
              <div className="mt-2 text-xs text-gray-500 bg-yellow-50 p-2 rounded">
                <p>You can use these placeholders in your message:</p>
                <ul className="list-disc pl-5 mt-1">
                  <li>{'{product_name}'} - Will be replaced with the selected product name</li>
                  <li>{'{product_price}'} - Will be replaced with the selected product price</li>
                </ul>
              </div>
            )}
          </div>

          {/* Image Upload */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image (Optional)
            </label>

            {imagePreview ? (
              <div className="relative mb-2">
                <img
                  src={imagePreview}
                  alt="Message image"
                  className="w-full h-auto max-h-48 object-contain rounded-md border border-gray-300"
                />
                <button
                  type="button"
                  onClick={handleRemoveImage}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <PhotoIcon className="w-8 h-8 mb-3 text-gray-400" />
                    <p className="mb-1 text-sm text-gray-500">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 1MB)</p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleImageUpload}
                  />
                </label>
              </div>
            )}
          </div>

          {/* Buttons */}
          <div className="mb-4">
            <div className="mb-2">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Buttons ({buttons.length}/3)
                </label>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => handleAddButton('quick_reply')}
                    disabled={buttons.length >= 3}
                    className={`text-xs px-2 py-1 rounded flex items-center ${
                      buttons.length >= 3
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-green-50 text-green-600 hover:bg-green-100'
                    }`}
                  >
                    <PlusIcon className="h-3 w-3 mr-1" />
                    Quick Reply
                  </button>
                  <button
                    type="button"
                    onClick={() => handleAddButton('url')}
                    disabled={buttons.length >= 3}
                    className={`text-xs px-2 py-1 rounded flex items-center ${
                      buttons.length >= 3
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                    }`}
                  >
                    <PlusIcon className="h-3 w-3 mr-1" />
                    URL
                  </button>
                  <button
                    type="button"
                    onClick={() => handleAddButton('call')}
                    disabled={buttons.length >= 3}
                    className={`text-xs px-2 py-1 rounded flex items-center ${
                      buttons.length >= 3
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-purple-50 text-purple-600 hover:bg-purple-100'
                    }`}
                  >
                    <PlusIcon className="h-3 w-3 mr-1" />
                    Call
                  </button>
                </div>
              </div>

              {/* Button type information */}
              <div className="bg-gray-50 p-3 rounded-md text-xs text-gray-600 mb-3">
                <p className="font-medium mb-1">Button Types:</p>
                <ul className="space-y-1 ml-4 list-disc">
                  <li className="flex items-start">
                    <span className="font-medium text-green-600 mr-1">Quick Reply:</span>
                    <span>Can be connected to other messages to continue the conversation flow.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="font-medium text-blue-600 mr-1">URL:</span>
                    <span>Opens an external website. Cannot connect to other messages.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="font-medium text-purple-600 mr-1">Call:</span>
                    <span>Opens the phone dialer. Cannot connect to other messages.</span>
                  </li>
                </ul>
                <p className="mt-2 text-gray-500 italic">Maximum 3 buttons allowed per message.</p>
              </div>
            </div>

            {/* Button list */}
            {buttons.map((button, index) => (
              <div key={index} className="bg-gray-50 p-3 rounded-md mb-2 border border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <span className="text-xs font-medium bg-gray-200 text-gray-700 px-2 py-1 rounded mr-2">
                      Button {index + 1}
                    </span>
                    <select
                      value={button.type}
                      onChange={(e) => handleButtonTypeChange(index, e.target.value)}
                      className="text-xs border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="quick_reply">Quick Reply</option>
                      <option value="url">URL</option>
                      <option value="call">Call</option>
                    </select>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveButton(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>

                <div className="mb-2">
                  <label className="block text-xs text-gray-500 mb-1">Button Text</label>
                  <input
                    type="text"
                    value={button.text}
                    onChange={(e) => handleButtonTextChange(index, e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="Button text"
                  />
                </div>

                {button.type === 'quick_reply' && (
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Reply Value</label>
                    <input
                      type="text"
                      value={button.value || ''}
                      onChange={(e) => handleButtonValueChange(index, 'value', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                      placeholder="Value returned when clicked"
                    />
                  </div>
                )}

                {button.type === 'url' && (
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">URL</label>
                    <input
                      type="url"
                      value={button.url || ''}
                      onChange={(e) => handleButtonValueChange(index, 'url', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                      placeholder="https://"
                    />
                  </div>
                )}

                {button.type === 'call' && (
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Phone Number</label>
                    <input
                      type="tel"
                      value={button.phoneNumber || ''}
                      onChange={(e) => handleButtonValueChange(index, 'phoneNumber', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                      placeholder="+91"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Action buttons */}
          <div className="flex justify-between space-x-3 pt-4 border-t border-gray-200">
            {/* Preview button */}
            <button
              type="button"
              onClick={() => setIsPreviewOpen(true)}
              className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md shadow-sm hover:bg-blue-100 flex items-center"
            >
              <EyeIcon className="h-4 w-4 mr-1" />
              Preview
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700"
              >
                Save Changes
              </button>
            </div>
          </div>

          {/* WhatsApp Template Preview */}
          <WhatsAppTemplatePreview
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            message={{
              content,
              buttons,
              header,
              footer,
              image: imagePreview
            }}
          />
        </form>
      </div>
    </div>
  );
};

export default MessageEditor;
