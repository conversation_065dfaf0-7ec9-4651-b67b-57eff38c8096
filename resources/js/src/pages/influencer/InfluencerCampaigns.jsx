import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  MegaphoneIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  LinkIcon,
  CurrencyRupeeIcon,
  ShoppingBagIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerCampaigns() {
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  // Fetch campaigns
  useEffect(() => {
    const fetchCampaigns = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockCampaigns = [
          {
            id: 1,
            vendor: 'Grocery Store A',
            vendorLogo: 'https://randomuser.me/api/portraits/men/32.jpg',
            product: 'Organic Vegetables',
            description: 'Promote our fresh organic vegetables to your audience. We offer a wide range of seasonal produce directly from farms.',
            commission: 15,
            planType: 'Standard',
            planPrice: 1499,
            status: 'active',
            startDate: '2023-10-01',
            endDate: '2023-12-31',
            referralLink: 'whamart.com/ref/rahul123/grocery-a',
            referrals: 32,
            signups: 12,
            sales: 5,
            earnings: 1125
          },
          {
            id: 2,
            vendor: 'Snack Center B',
            vendorLogo: 'https://randomuser.me/api/portraits/women/44.jpg',
            product: 'Healthy Snacks',
            description: 'Promote our range of healthy snacks made with natural ingredients. Perfect for health-conscious consumers.',
            commission: 20,
            planType: 'Gold',
            planPrice: 3000,
            status: 'active',
            startDate: '2023-09-15',
            endDate: '2023-12-15',
            referralLink: 'whamart.com/ref/rahul123/snack-b',
            referrals: 45,
            signups: 18,
            sales: 8,
            earnings: 4800
          },
          {
            id: 3,
            vendor: 'Home Business C',
            vendorLogo: 'https://randomuser.me/api/portraits/women/68.jpg',
            product: 'Handmade Crafts',
            description: 'Help promote our handmade crafts and home decor items. Each piece is unique and made with love.',
            commission: 25,
            planType: 'Premium',
            planPrice: 4999,
            status: 'pending',
            startDate: '2023-11-01',
            endDate: '2024-01-31',
            referralLink: 'whamart.com/ref/rahul123/home-c',
            referrals: 0,
            signups: 0,
            sales: 0,
            earnings: 0
          },
          {
            id: 4,
            vendor: 'Local Bakery D',
            vendorLogo: 'https://randomuser.me/api/portraits/men/75.jpg',
            product: 'Fresh Bread',
            description: 'Promote our freshly baked bread and pastries. We use traditional recipes and high-quality ingredients.',
            commission: 15,
            planType: 'Standard',
            planPrice: 1499,
            status: 'active',
            startDate: '2023-10-10',
            endDate: '2023-12-10',
            referralLink: 'whamart.com/ref/rahul123/bakery-d',
            referrals: 28,
            signups: 10,
            sales: 4,
            earnings: 900
          },
          {
            id: 5,
            vendor: 'Organic Farm E',
            vendorLogo: 'https://randomuser.me/api/portraits/women/90.jpg',
            product: 'Farm Products',
            description: 'Help us promote our organic farm products including fruits, vegetables, and dairy items.',
            commission: 25,
            planType: 'Premium',
            planPrice: 4999,
            status: 'completed',
            startDate: '2023-07-01',
            endDate: '2023-09-30',
            referralLink: 'whamart.com/ref/rahul123/farm-e',
            referrals: 65,
            signups: 25,
            sales: 12,
            earnings: 15000
          },
          {
            id: 6,
            vendor: 'Fashion Store F',
            vendorLogo: 'https://randomuser.me/api/portraits/women/22.jpg',
            product: 'Ethnic Wear',
            description: 'Promote our collection of traditional ethnic wear for all occasions. Handcrafted with premium fabrics.',
            commission: 20,
            planType: 'Gold',
            planPrice: 3000,
            status: 'active',
            startDate: '2023-09-01',
            endDate: '2023-11-30',
            referralLink: 'whamart.com/ref/rahul123/fashion-f',
            referrals: 38,
            signups: 15,
            sales: 6,
            earnings: 3600
          },
          {
            id: 7,
            vendor: 'Tech Gadgets G',
            vendorLogo: 'https://randomuser.me/api/portraits/men/22.jpg',
            product: 'Mobile Accessories',
            description: 'Promote our range of high-quality mobile accessories including cases, chargers, and screen protectors.',
            commission: 15,
            planType: 'Standard',
            planPrice: 1499,
            status: 'rejected',
            startDate: '2023-10-15',
            endDate: '2023-12-15',
            referralLink: '',
            referrals: 0,
            signups: 0,
            sales: 0,
            earnings: 0,
            rejectionReason: 'Campaign not aligned with platform guidelines'
          },
          {
            id: 8,
            vendor: 'Beauty Shop H',
            vendorLogo: 'https://randomuser.me/api/portraits/women/28.jpg',
            product: 'Natural Skincare',
            description: 'Help promote our natural skincare products made with organic ingredients. Suitable for all skin types.',
            commission: 25,
            planType: 'Premium',
            planPrice: 4999,
            status: 'pending',
            startDate: '2023-11-15',
            endDate: '2024-02-15',
            referralLink: 'whamart.com/ref/rahul123/beauty-h',
            referrals: 0,
            signups: 0,
            sales: 0,
            earnings: 0
          }
        ];
        
        setCampaigns(mockCampaigns);
        setTotalPages(Math.ceil(mockCampaigns.length / 5) || 1);
      } catch (error) {
        console.error('Error fetching campaigns:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  // Filter campaigns based on search term and status
  useEffect(() => {
    if (!campaigns.length) return;
    
    const filteredCampaigns = campaigns.filter(campaign => {
      const matchesSearch = searchTerm === '' || 
        campaign.vendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.product.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || campaign.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });
    
    setTotalPages(Math.ceil(filteredCampaigns.length / 5) || 1);
  }, [searchTerm, filterStatus, campaigns]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return { color: 'bg-green-100 text-green-800', text: 'Active', icon: CheckCircleIcon };
      case 'pending':
        return { color: 'bg-yellow-100 text-yellow-800', text: 'Pending', icon: ClockIcon };
      case 'completed':
        return { color: 'bg-blue-100 text-blue-800', text: 'Completed', icon: CheckCircleIcon };
      case 'rejected':
        return { color: 'bg-red-100 text-red-800', text: 'Rejected', icon: XCircleIcon };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown', icon: ClockIcon };
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle campaign selection
  const handleSelectCampaign = (campaign) => {
    setSelectedCampaign(campaign);
  };

  // Handle close campaign details
  const handleCloseCampaignDetails = () => {
    setSelectedCampaign(null);
  };

  // Copy referral link to clipboard
  const handleCopyReferralLink = (link) => {
    navigator.clipboard.writeText(link);
    alert('Referral link copied to clipboard!');
  };

  // Get filtered and paginated campaigns
  const getFilteredCampaigns = () => {
    if (!campaigns.length) return [];
    
    const filteredCampaigns = campaigns.filter(campaign => {
      const matchesSearch = searchTerm === '' || 
        campaign.vendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.product.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || campaign.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });
    
    return filteredCampaigns.slice((currentPage - 1) * 5, currentPage * 5);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Marketing Campaigns</div>
          <h1 className="welcome-title">
            Promotional <span style={{ color: '#25D366' }}>Campaigns</span>
          </h1>
          <p className="welcome-subtitle">
            Browse and manage your promotional campaigns, track performance, and maximize your earnings.
          </p>
        </div>
      </div>

      {/* Campaigns Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Your Campaigns</h2>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search campaigns..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <div className="relative">
              <select
                value={filterStatus}
                onChange={(e) => handleFilterChange(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
                <option value="rejected">Rejected</option>
              </select>
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading campaigns...</p>
          </div>
        ) : (
          <>
            {getFilteredCampaigns().length === 0 ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>No campaigns found matching your criteria.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {getFilteredCampaigns().map((campaign) => {
                  const statusBadge = getStatusBadge(campaign.status);
                  const StatusIcon = statusBadge.icon;
                  return (
                    <div key={campaign.id} className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                      <div className="flex flex-col md:flex-row gap-6">
                        {/* Campaign Info */}
                        <div className="md:w-2/3">
                          <div className="flex items-start">
                            <div className="flex-shrink-0 mr-4">
                              <img 
                                src={campaign.vendorLogo} 
                                alt={campaign.vendor} 
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            </div>
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">{campaign.product}</h3>
                              <p className="text-sm text-gray-500">{campaign.vendor}</p>
                              <div className="flex items-center mt-1">
                                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color} flex items-center`}>
                                  <StatusIcon className="h-4 w-4 mr-1" />
                                  {statusBadge.text}
                                </span>
                                <span className="text-xs text-gray-500 ml-2">
                                  {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="mt-4">
                            <p className="text-sm text-gray-600">{campaign.description}</p>
                          </div>
                          
                          <div className="mt-4 flex flex-wrap gap-4">
                            <div className="flex items-center">
                              <CurrencyRupeeIcon className="h-5 w-5 text-gray-400 mr-1" />
                              <span className="text-sm font-medium">{campaign.commission}% Commission</span>
                            </div>
                            <div className="flex items-center">
                              <ShoppingBagIcon className="h-5 w-5 text-gray-400 mr-1" />
                              <span className="text-sm font-medium">{campaign.planType} Plan ({formatCurrency(campaign.planPrice)}/year)</span>
                            </div>
                          </div>
                          
                          {campaign.status === 'active' && (
                            <div className="mt-4">
                              <div className="flex items-center">
                                <LinkIcon className="h-5 w-5 text-gray-400 mr-1" />
                                <span className="text-sm text-gray-600 mr-2">{campaign.referralLink}</span>
                                <button
                                  onClick={() => handleCopyReferralLink(campaign.referralLink)}
                                  className="text-xs text-green-600 hover:text-green-700"
                                >
                                  Copy
                                </button>
                              </div>
                            </div>
                          )}
                          
                          {campaign.rejectionReason && (
                            <div className="mt-4">
                              <p className="text-sm text-red-500">Reason: {campaign.rejectionReason}</p>
                            </div>
                          )}
                        </div>
                        
                        {/* Campaign Stats */}
                        <div className="md:w-1/3 flex flex-col justify-between">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-xs text-gray-500">Referrals</p>
                              <p className="text-lg font-bold">{campaign.referrals}</p>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-xs text-gray-500">Signups</p>
                              <p className="text-lg font-bold">{campaign.signups}</p>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-xs text-gray-500">Sales</p>
                              <p className="text-lg font-bold">{campaign.sales}</p>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-xs text-gray-500">Earnings</p>
                              <p className="text-lg font-bold text-green-600">{formatCurrency(campaign.earnings)}</p>
                            </div>
                          </div>
                          
                          <div className="mt-4 flex justify-end">
                            <button
                              onClick={() => handleSelectCampaign(campaign)}
                              className="btn btn-primary"
                            >
                              View Details
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex items-center justify-center">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                  >
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Campaign Details Modal */}
      {selectedCampaign && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Campaign Details</h3>
              <button
                onClick={handleCloseCampaignDetails}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="flex items-center mb-6">
              <img 
                src={selectedCampaign.vendorLogo} 
                alt={selectedCampaign.vendor} 
                className="w-16 h-16 rounded-full object-cover mr-4"
              />
              <div>
                <h2 className="text-xl font-bold text-gray-900">{selectedCampaign.product}</h2>
                <p className="text-gray-600">{selectedCampaign.vendor}</p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const statusBadge = getStatusBadge(selectedCampaign.status);
                    const StatusIcon = statusBadge.icon;
                    return (
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color} flex items-center`}>
                        <StatusIcon className="h-4 w-4 mr-1" />
                        {statusBadge.text}
                      </span>
                    );
                  })()}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Campaign Details</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm mb-2"><span className="font-medium">Description:</span> {selectedCampaign.description}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Duration:</span> {formatDate(selectedCampaign.startDate)} to {formatDate(selectedCampaign.endDate)}</p>
                  <p className="text-sm mb-2"><span className="font-medium">Commission:</span> {selectedCampaign.commission}%</p>
                  <p className="text-sm mb-2"><span className="font-medium">Plan Type:</span> {selectedCampaign.planType} ({formatCurrency(selectedCampaign.planPrice)}/year)</p>
                  {selectedCampaign.rejectionReason && (
                    <p className="text-sm text-red-500 mb-2"><span className="font-medium">Rejection Reason:</span> {selectedCampaign.rejectionReason}</p>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">Performance Metrics</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Total Referrals</p>
                      <p className="text-lg font-bold">{selectedCampaign.referrals}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Total Signups</p>
                      <p className="text-lg font-bold">{selectedCampaign.signups}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Total Sales</p>
                      <p className="text-lg font-bold">{selectedCampaign.sales}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Total Earnings</p>
                      <p className="text-lg font-bold text-green-600">{formatCurrency(selectedCampaign.earnings)}</p>
                    </div>
                  </div>
                  
                  {selectedCampaign.referrals > 0 && (
                    <div className="mt-4">
                      <p className="text-xs text-gray-500 mb-1">Conversion Rate</p>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div 
                          className="bg-green-600 h-2.5 rounded-full" 
                          style={{ 
                            width: `${(selectedCampaign.sales / selectedCampaign.referrals) * 100}%`,
                            backgroundColor: '#25D366'
                          }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 text-right">
                        {Math.round((selectedCampaign.sales / selectedCampaign.referrals) * 100)}% ({selectedCampaign.sales} of {selectedCampaign.referrals})
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {selectedCampaign.status === 'active' && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Your Referral Link</h4>
                <div className="flex items-center bg-gray-50 p-3 rounded-lg">
                  <LinkIcon className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" />
                  <span className="text-sm text-gray-600 mr-2 truncate">{selectedCampaign.referralLink}</span>
                  <button
                    onClick={() => handleCopyReferralLink(selectedCampaign.referralLink)}
                    className="btn btn-primary btn-sm flex-shrink-0"
                  >
                    Copy Link
                  </button>
                </div>
              </div>
            )}
            
            <div className="flex justify-end">
              <button
                onClick={handleCloseCampaignDetails}
                className="btn btn-outline"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
