import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeftIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { serviceAPI } from '../../../services/api';

export default function EditService() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Service form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    duration: '',
    isOnline: false,
    location: '',
    discountPrice: '',
    isFeatured: false,
  });

  // Image state
  const [serviceImage, setServiceImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [existingImageUrl, setExistingImageUrl] = useState(null);

  // Fetch service data
  useEffect(() => {
    const fetchService = async () => {
      try {
        const response = await serviceAPI.getServiceById(id);
        const service = response.data.service;

        // Set form data
        setFormData({
          name: service.name || '',
          description: service.description || '',
          price: service.price || '',
          category: service.category || '',
          duration: service.duration || '',
          isOnline: service.isOnline || false,
          location: service.location || '',
          discountPrice: service.discountPrice || '',
          isFeatured: service.isFeatured || false,
        });

        // Set image preview if exists
        if (service.imageUrl) {
          setExistingImageUrl(service.imageUrl);
          // If the image URL is relative, prepend the API base URL
          const fullImageUrl = service.imageUrl.startsWith('http')
            ? service.imageUrl
            : `https://api.whamart.shop${service.imageUrl}`;
          setImagePreview(fullImageUrl);
        }

      } catch (err) {
        setError('Failed to load service. Please try again.');
      } finally {
        setFetchLoading(false);
      }
    };

    fetchService();
  }, [id]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // Validate file is an image
        if (!file.type.startsWith('image/')) {
          setError('Please select a valid image file');
          return;
        }

        setServiceImage(file);

        // Revoke previous object URL to avoid memory leaks
        if (imagePreview && !existingImageUrl) {
          try {
            URL.revokeObjectURL(imagePreview);
          } catch (revokeError) {
            // Silent error handling for object URL revocation
          }
        }

        const objectUrl = URL.createObjectURL(file);
        setImagePreview(objectUrl);
        setExistingImageUrl(null);
      } catch (error) {
        setError('Failed to preview image. Please try again.');
      }
    }
  };

  // Remove image
  const handleRemoveImage = () => {
    if (imagePreview && !existingImageUrl) {
      try {
        URL.revokeObjectURL(imagePreview);
      } catch (error) {
        // Silent error handling for object URL revocation
      }
    }
    setServiceImage(null);
    setImagePreview(null);
    setExistingImageUrl(null);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Create FormData object for file upload
      const serviceData = new FormData();

      // Add all form fields to FormData
      Object.keys(formData).forEach(key => {
        if (formData[key] !== '') {
          serviceData.append(key, formData[key]);
        }
      });

      // Add image if exists
      if (serviceImage) {
        serviceData.append('serviceImage', serviceImage);
      }

      // Send data to backend
      const response = await serviceAPI.updateService(id, serviceData);

      // Show success message
      setSuccess(true);

      // Reset form after 2 seconds and redirect
      setTimeout(() => {
        navigate('/vendor/services');
      }, 2000);

    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update service. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Categories for dropdown
  const categories = [
    'Consulting',
    'Design',
    'Education',
    'Health & Wellness',
    'Home Services',
    'IT & Software',
    'Legal',
    'Marketing',
    'Personal Care',
    'Professional Services',
    'Other'
  ];

  if (fetchLoading) {
    return (
      <div className="vendor-dashboard">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <p>Loading service data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Edit Service</div>
          <h1 className="welcome-title">
            Edit <span style={{ color: '#25D366' }}>{formData.name}</span>
          </h1>
          <p className="welcome-subtitle">
            Update your service information and keep your catalog up to date.
          </p>
        </div>
      </div>

      {/* Back Button */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => navigate('/vendor/services')}
          className="btn btn-outline"
          style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
        >
          <ArrowLeftIcon style={{ width: '16px', height: '16px' }} />
          Back to Services
        </button>
      </div>

      {/* Success Message */}
      {success && (
        <div className="alert alert-success" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(37, 211, 102, 0.1)', color: '#25D366', border: '1px solid #25D366' }}>
          Service updated successfully! Redirecting...
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="alert alert-danger" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(220, 53, 69, 0.1)', color: '#dc3545', border: '1px solid #dc3545' }}>
          {error}
        </div>
      )}

      {/* Service Form */}
      <div className="card" style={{ marginBottom: '30px' }}>
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '24px' }}>
            <h2 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '20px' }}>Service Information</h2>

            {/* Basic Info Section */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Service Name */}
              <div className="form-group">
                <label htmlFor="name" className="form-label">Service Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter service name"
                  required
                />
              </div>

              {/* Category */}
              <div className="form-group">
                <label htmlFor="category" className="form-label">Category *</label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Price and Duration Section */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Price */}
              <div className="form-group">
                <label htmlFor="price" className="form-label">Price (₹) *</label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              {/* Discount Price */}
              <div className="form-group">
                <label htmlFor="discountPrice" className="form-label">Discount Price (₹)</label>
                <input
                  type="number"
                  id="discountPrice"
                  name="discountPrice"
                  value={formData.discountPrice}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>

              {/* Duration */}
              <div className="form-group">
                <label htmlFor="duration" className="form-label">Duration</label>
                <input
                  type="text"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="e.g., 1 hour, 30 minutes"
                />
              </div>
            </div>

            {/* Service Type and Location */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '20px', marginBottom: '24px' }}>
              {/* Service Type */}
              <div className="form-group" style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  id="isOnline"
                  name="isOnline"
                  checked={formData.isOnline}
                  onChange={handleInputChange}
                  style={{ marginRight: '8px' }}
                />
                <label htmlFor="isOnline" className="form-label" style={{ margin: 0 }}>
                  This is an online service
                </label>
              </div>

              {/* Location (only if not online) */}
              {!formData.isOnline && (
                <div className="form-group">
                  <label htmlFor="location" className="form-label">Service Location</label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Where is this service provided?"
                  />
                </div>
              )}

              {/* Featured Service */}
              <div className="form-group" style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  id="isFeatured"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={handleInputChange}
                  style={{ marginRight: '8px' }}
                />
                <label htmlFor="isFeatured" className="form-label" style={{ margin: 0 }}>
                  Featured Service
                </label>
              </div>
            </div>

            {/* Description */}
            <div className="form-group" style={{ marginBottom: '24px' }}>
              <label htmlFor="description" className="form-label">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="form-textarea"
                placeholder="Enter service description"
                rows="4"
              ></textarea>
            </div>

            {/* Service Image */}
            <div className="form-group">
              <label className="form-label">Service Image</label>
              <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                {imagePreview ? (
                  <div style={{ position: 'relative', marginBottom: '16px' }}>
                    <img
                      src={imagePreview}
                      alt="Service preview"
                      style={{ width: '200px', height: '200px', objectFit: 'cover', borderRadius: '8px' }}
                    />
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      style={{
                        position: 'absolute',
                        top: '-8px',
                        right: '-8px',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px'
                      }}
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <div
                    style={{
                      width: '200px',
                      height: '200px',
                      border: '2px dashed #ddd',
                      borderRadius: '8px',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '16px',
                      cursor: 'pointer'
                    }}
                    onClick={() => document.getElementById('serviceImage').click()}
                  >
                    <PhotoIcon style={{ width: '48px', height: '48px', color: '#aaa' }} />
                    <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>Click to upload image</p>
                  </div>
                )}
                <input
                  type="file"
                  id="serviceImage"
                  name="serviceImage"
                  onChange={handleImageUpload}
                  accept="image/*"
                  style={{ display: 'none' }}
                />
                <button
                  type="button"
                  onClick={() => document.getElementById('serviceImage').click()}
                  className="btn btn-outline"
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <PhotoIcon style={{ width: '16px', height: '16px' }} />
                  {imagePreview ? 'Change Image' : 'Upload Image'}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <div style={{ marginTop: '24px', display: 'flex', justifyContent: 'flex-end' }}>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
                style={{ minWidth: '120px' }}
              >
                {loading ? 'Updating...' : 'Update Service'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
