<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PaymentMethodController extends Controller
{
    public function index()
    {
        $store = Auth::user()->store;
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $paymentMethods = PaymentMethod::where('store_id', $store->id)->orderBy('created_at', 'desc')->get();

        return response()->json(['paymentMethods' => $paymentMethods]);
    }

    public function store(Request $request)
    {
        $store = Auth::user()->store;
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:upi,bank_transfer,cod',
            'name' => 'required|string|max:255',
            'is_default' => 'boolean',
            'upi_id' => 'required_if:type,upi|string|max:255',
            'account_name' => 'required_if:type,bank_transfer|string|max:255',
            'account_number' => 'required_if:type,bank_transfer|string|max:255',
            'bank_name' => 'required_if:type,bank_transfer|string|max:255',
            'ifsc_code' => 'required_if:type,bank_transfer|string|max:255',
            'instructions' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($request->input('is_default')) {
            PaymentMethod::where('store_id', $store->id)
                ->where('type', $request->input('type'))
                ->update(['is_default' => false]);
        }

        $qrCodeUrl = null;
        // QR Code generation logic will be added here once the package is installed.

        $paymentMethod = PaymentMethod::create(array_merge($request->all(), [
            'store_id' => $store->id,
            'qr_code_url' => $qrCodeUrl,
        ]));

        return response()->json(['message' => 'Payment method created successfully', 'paymentMethod' => $paymentMethod], 201);
    }

    public function show(PaymentMethod $paymentMethod)
    {
        $this->authorize('view', $paymentMethod);
        return response()->json(['paymentMethod' => $paymentMethod]);
    }

    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        $this->authorize('update', $paymentMethod);

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'upi_id' => 'string|max:255',
            'account_name' => 'string|max:255',
            'account_number' => 'string|max:255',
            'bank_name' => 'string|max:255',
            'ifsc_code' => 'string|max:255',
            'instructions' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($request->input('is_default') && !$paymentMethod->is_default) {
            PaymentMethod::where('store_id', $paymentMethod->store_id)
                ->where('type', $paymentMethod->type)
                ->where('id', '!=', $paymentMethod->id)
                ->update(['is_default' => false]);
        }

        // QR Code update logic will be added here.

        $paymentMethod->update($request->all());

        return response()->json(['message' => 'Payment method updated successfully', 'paymentMethod' => $paymentMethod]);
    }

    public function destroy(PaymentMethod $paymentMethod)
    {
        $this->authorize('delete', $paymentMethod);

        // Delete QR code file if it exists.
        if ($paymentMethod->qr_code_url) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $paymentMethod->qr_code_url));
        }

        $paymentMethod->delete();

        return response()->json(['message' => 'Payment method deleted successfully']);
    }
}
