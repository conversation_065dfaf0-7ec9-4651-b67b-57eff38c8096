import { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, MagnifyingGlassIcon, FunnelIcon, ArrowDownTrayIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link, useNavigate } from 'react-router-dom';
import '../../../src/styles/vendor-dashboard.css';
import { serviceAPI } from '../../services/api';

export default function VendorServices() {
  const navigate = useNavigate();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const servicesPerPage = 10;

  // Fetch services from backend
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        
        // Build query params
        const queryParams = new URLSearchParams();
        queryParams.append('page', currentPage);
        queryParams.append('limit', servicesPerPage);
        
        if (searchTerm) {
          queryParams.append('search', searchTerm);
        }
        
        if (categoryFilter) {
          queryParams.append('category', categoryFilter);
        }
        
        const response = await serviceAPI.getAllServices(queryParams.toString());
        
        if (response.data) {
          setServices(response.data.services || []);
          setTotalCount(response.data.count || 0);
        } else {
          setServices([]);
          setTotalCount(0);
        }
        
        setError(null);
      } catch (err) {
//         // Removed console.error
        setError('Failed to load services. Please try again.');
        setServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [currentPage, searchTerm, categoryFilter]);

  // Handle search input change
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  // Handle category filter change
  const handleCategoryFilter = (e) => {
    setCategoryFilter(e.target.value);
    setCurrentPage(1); // Reset to first page on filter change
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= Math.ceil(totalCount / servicesPerPage)) {
      setCurrentPage(newPage);
    }
  };

  // Handle service deletion
  const handleDeleteService = async (id) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await serviceAPI.deleteService(id);
        // Refresh the services list
        setServices(services.filter(service => service.id !== id));
        setTotalCount(prevCount => prevCount - 1);
      } catch (err) {
//         // Removed console.error
        setError('Failed to delete service. Please try again.');
      }
    }
  };

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / servicesPerPage);
  const startItem = (currentPage - 1) * servicesPerPage + 1;
  const endItem = Math.min(currentPage * servicesPerPage, totalCount);

  return (
    <div className="vendor-dashboard">
      {/* Page Header */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Services Management</div>
          <h1 className="welcome-title">
            Manage Your <span style={{ color: '#25D366' }}>Services</span>
          </h1>
          <p className="welcome-subtitle">
            Add, edit, and manage your service catalog. Keep your offerings updated to provide the best experience for your customers.
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card" style={{ marginBottom: 'var(--spacing-xl)' }}>
        <div style={{ padding: 'var(--spacing-lg)', display: 'flex', flexWrap: 'wrap', gap: 'var(--spacing-md)', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ position: 'relative', width: '40%', minWidth: '250px' }}>
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={handleSearch}
              className="search-input"
              style={{ paddingLeft: '40px', width: '100%' }}
            />
            <MagnifyingGlassIcon style={{ position: 'absolute', left: '12px', top: '50%', transform: 'translateY(-50%)', width: '20px', height: '20px', color: '#6C757D' }} />
          </div>
          
          <div style={{ display: 'flex', gap: 'var(--spacing-md)' }}>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FunnelIcon style={{ width: '16px', height: '16px' }} />
              Filter
            </button>
            <button className="btn btn-outline" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ArrowDownTrayIcon style={{ width: '16px', height: '16px' }} />
              Export
            </button>
            <Link to="/vendor/services/add" className="btn btn-primary" style={{ display: 'flex', alignItems: 'center', gap: '8px', textDecoration: 'none' }}>
              <PlusIcon style={{ width: '16px', height: '16px' }} />
              Add Service
            </Link>
          </div>
        </div>
      </div>

      {/* Services Table */}
      <div style={{ marginBottom: 'var(--spacing-xl)' }}>
        <div className="card">
          <div className="table-container">
            {loading ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p>Loading services...</p>
              </div>
            ) : services.length === 0 ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <p>No services found. Add your first service to get started!</p>
                <Link to="/vendor/services/add" className="btn btn-primary" style={{ marginTop: '16px', display: 'inline-flex', alignItems: 'center', gap: '8px' }}>
                  <PlusIcon style={{ width: '16px', height: '16px' }} />
                  Add Service
                </Link>
              </div>
            ) : (
              <table className="products-table">
                <thead>
                  <tr>
                    <th>Service</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Duration</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {services.map((service) => (
                    <tr key={service.id}>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <div style={{ width: '40px', height: '40px', borderRadius: '4px', overflow: 'hidden', flexShrink: 0 }}>
                            <img
                              src={service.imageUrl || '/placeholder-service.jpg'}
                              alt={service.name}
                              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                              onError={(e) => { e.target.src = '/placeholder-service.jpg' }}
                            />
                          </div>
                          <div>
                            <div style={{ fontWeight: '500' }}>{service.name}</div>
                            <div style={{ fontSize: '12px', color: '#6C757D' }}>ID: {service.id}</div>
                          </div>
                        </div>
                      </td>
                      <td>{service.category || 'Uncategorized'}</td>
                      <td>₹{parseFloat(service.price).toFixed(2)}</td>
                      <td>{service.duration || 'Not specified'}</td>
                      <td>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <button
                            onClick={() => navigate(`/vendor/services/edit/${service.id}`)}
                            className="btn-icon"
                            title="Edit"
                          >
                            <PencilIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                          <button
                            onClick={() => handleDeleteService(service.id)}
                            className="btn-icon btn-icon-danger"
                            title="Delete"
                          >
                            <TrashIcon style={{ width: '16px', height: '16px' }} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {/* Pagination */}
          {!loading && services.length > 0 && (
            <div style={{ padding: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderTop: '1px solid #e9ecef' }}>
              <div style={{ fontSize: '14px', color: '#6C757D' }}>
                Showing {startItem} to {endItem} of {totalCount} services
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="btn-icon"
                  style={{ opacity: currentPage === 1 ? 0.5 : 1 }}
                >
                  <ChevronLeftIcon style={{ width: '16px', height: '16px' }} />
                </button>
                <span style={{ display: 'flex', alignItems: 'center', fontSize: '14px' }}>
                  Page {currentPage} of {totalPages || 1}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="btn-icon"
                  style={{ opacity: currentPage >= totalPages ? 0.5 : 1 }}
                >
                  <ChevronRightIcon style={{ width: '16px', height: '16px' }} />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ marginBottom: 'var(--spacing-xl)' }}>
        <h2 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '16px' }}>Quick Actions</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '16px' }}>
          <Link to="/vendor/services/add" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <PlusIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Add New Service</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Add a new service to your store catalog
              </p>
            </div>
          </Link>

          <Link to="/vendor/services/categories" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <FunnelIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Manage Categories</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Organize your services into categories
              </p>
            </div>
          </Link>

          <Link to="/vendor/services/import" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <ArrowDownTrayIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Import Services</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Bulk import services from CSV or Excel
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
