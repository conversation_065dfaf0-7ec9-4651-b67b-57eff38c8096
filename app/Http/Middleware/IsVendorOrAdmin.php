<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsVendorOrAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user() && ($request->user()->user_type === 'vendor' || $request->user()->user_type === 'admin')) {
            return $next($request);
        }

        return response()->json(['message' => 'Access denied. Vendor or Admin role required.'], 403);
    }
}
