import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  UserGroupIcon,
  UserIcon,
  CurrencyDollarIcon,
  ChartPieIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  ArrowRightIcon,
  PlusCircleIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminInvestorManagement() {
  const [loading, setLoading] = useState(true);
  const [investmentData, setInvestmentData] = useState({
    totalInvestors: 0,
    totalRaisedCapital: 0,
    totalDilutedEquity: 0,
    shareholders: [],
    managementBoard: [],
    recentActivities: []
  });

  useEffect(() => {
    // In a real application, this would be an API call
    const fetchInvestmentData = async () => {
      setLoading(true);
      try {
        // Simulating API call with mock data
        const mockData = {
          totalInvestors: 15,
          totalRaisedCapital: 5000000,
          totalDilutedEquity: 40,
          availableCapital: 2500000,
          shareholders: [
            { name: '<PERSON><PERSON>', equity: 60, role: 'Founder & CEO', investmentDate: '2022-01-15' },
            { name: 'Investor 1', equity: 20, investmentDate: '2022-03-22' },
            { name: 'Investor 2', equity: 10, investmentDate: '2022-06-10' },
            { name: 'Others', equity: 10, investmentDate: '2022-08-05' }
          ],
          managementBoard: [
            { name: 'Karan Solanki', role: 'Founder & CEO', joinDate: '2022-01-01' },
            { name: 'John Doe', role: 'CTO', joinDate: '2022-02-15' },
            { name: 'Jane Smith', role: 'CFO', joinDate: '2022-03-10' }
          ],
          recentActivities: [
            { id: 1, type: 'investment', investor: 'New Investor', amount: '₹500,000', date: '2023-05-15' },
            { id: 2, type: 'equity_change', description: 'Equity redistribution', date: '2023-04-22' },
            { id: 3, type: 'board_addition', member: 'Jane Smith', role: 'CFO', date: '2023-03-10' },
            { id: 4, type: 'capital_use', amount: '₹200,000', purpose: 'Marketing Campaign', date: '2023-02-18' },
            { id: 5, type: 'document_added', document: 'Q1 Financial Report', date: '2023-01-05' }
          ]
        };
        setInvestmentData(mockData);
      } catch (error) {
        console.error('Error fetching investment data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInvestmentData();
  }, []);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading investment data...</p>
      </div>
    );
  }

  // Define stats with real data
  const stats = [
    {
      name: 'Total Investors',
      stat: investmentData.totalInvestors,
      icon: UserGroupIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Active investors'
    },
    {
      name: 'Total Raised Capital',
      stat: `₹${investmentData.totalRaisedCapital.toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Capital raised to date'
    },
    {
      name: 'Available Capital',
      stat: `₹${investmentData.availableCapital.toLocaleString()}`,
      icon: DocumentTextIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Funds available for use'
    },
    {
      name: 'Total Diluted Equity',
      stat: `${investmentData.totalDilutedEquity}%`,
      icon: ChartPieIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Equity distributed to investors'
    },
  ];

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Investment Management</div>
          <h1 className="welcome-title">
            Investment <span style={{ color: '#25D366' }}>Overview</span>
          </h1>
          <p className="welcome-subtitle">
            Manage company investments, equity distribution, and capital allocation. Track investor relations and board membership.
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Investment Overview</h2>
        </div>
        <div className="stats-grid">
          {stats.map((item) => (
            <div key={item.name} className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
              </div>
              <div className="stat-content">
                <h3 className="stat-label">{item.name}</h3>
                <p className="stat-value">{item.stat}</p>
                <p className="stat-description">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Shareholders Section */}
      <div className="orders-section">
        <div className="section-header">
          <h2 className="section-title">Shareholders</h2>
          <button className="btn btn-outline">
            Add Shareholder
            <PlusCircleIcon style={{ width: '16px', height: '16px' }} />
          </button>
        </div>
        <div className="card">
          <div className="table-container">
            <table className="products-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Equity Percentage</th>
                  <th>Role</th>
                  <th>Investment Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {investmentData.shareholders.map((shareholder, index) => (
                  <tr key={index}>
                    <td>
                      <div className="flex items-center gap-4">
                        <div className="stat-icon-container" style={{ 
                          backgroundColor: 'rgba(37, 211, 102, 0.1)', 
                          width: '36px', 
                          height: '36px' 
                        }}>
                          <UserIcon style={{ color: '#25D366', width: '18px', height: '18px' }} />
                        </div>
                        <span style={{ fontWeight: '600' }}>{shareholder.name}</span>
                      </div>
                    </td>
                    <td>
                      <span className="status-badge status-completed">{shareholder.equity}%</span>
                    </td>
                    <td>{shareholder.role || 'Investor'}</td>
                    <td>{shareholder.investmentDate}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button className="action-button edit" title="Edit">
                          <DocumentDuplicateIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Management Board Section */}
      <div className="orders-section">
        <div className="section-header">
          <h2 className="section-title">Management Board</h2>
          <button className="btn btn-outline">
            Add Board Member
            <PlusCircleIcon style={{ width: '16px', height: '16px' }} />
          </button>
        </div>
        <div className="card">
          <div className="table-container">
            <table className="products-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Role</th>
                  <th>Join Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {investmentData.managementBoard.map((member, index) => (
                  <tr key={index}>
                    <td>
                      <div className="flex items-center gap-4">
                        <div className="stat-icon-container" style={{ 
                          backgroundColor: 'rgba(7, 94, 84, 0.1)', 
                          width: '36px', 
                          height: '36px' 
                        }}>
                          <UserIcon style={{ color: '#075E54', width: '18px', height: '18px' }} />
                        </div>
                        <span style={{ fontWeight: '600' }}>{member.name}</span>
                      </div>
                    </td>
                    <td>{member.role}</td>
                    <td>{member.joinDate}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button className="action-button edit" title="Edit">
                          <DocumentDuplicateIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Recent Activity Section */}
      <div className="orders-section">
        <div className="section-header">
          <h2 className="section-title">Recent Investment Activity</h2>
          <Link to="/admin/investment/activity" className="btn btn-outline">
            View all activity
            <ArrowRightIcon style={{ width: '16px', height: '16px' }} />
          </Link>
        </div>
        <div className="card">
          {investmentData.recentActivities.length > 0 ? (
            <ul className="orders-list">
              {investmentData.recentActivities.map((activity) => (
                <li key={activity.id} className="order-item">
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div>
                      <p style={{ fontSize: '16px', fontWeight: '600', color: '#212529', margin: '0 0 4px 0' }}>
                        {activity.type === 'investment' && 'New Investment'}
                        {activity.type === 'equity_change' && 'Equity Change'}
                        {activity.type === 'board_addition' && 'Board Member Added'}
                        {activity.type === 'capital_use' && 'Capital Allocation'}
                        {activity.type === 'document_added' && 'Document Added'}
                      </p>
                      <p style={{ fontSize: '14px', color: '#6C757D', margin: '0' }}>
                        {activity.investor && `Investor: ${activity.investor}, Amount: ${activity.amount}`}
                        {activity.description && `${activity.description}`}
                        {activity.member && `${activity.member} joined as ${activity.role}`}
                        {activity.purpose && `${activity.amount} allocated for ${activity.purpose}`}
                        {activity.document && `Document: ${activity.document}`}
                      </p>
                    </div>
                    <div>
                      <span style={{ fontSize: '14px', color: '#6C757D' }}>{activity.date}</span>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div style={{ padding: '24px', textAlign: 'center' }}>
              <p>No recent investment activity found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Actions</h2>
        </div>
        <div className="stats-grid">
          <Link to="/admin/investment/add-investor" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <UserGroupIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Add New Investor</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Register a new investor with equity allocation
              </p>
            </div>
          </Link>

          <Link to="/admin/investment/capital-allocation" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <CurrencyDollarIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Allocate Capital</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Manage and allocate available capital
              </p>
            </div>
          </Link>

          <Link to="/admin/investment/documents" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <DocumentTextIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Investment Documents</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                View and manage investment documents
              </p>
            </div>
          </Link>

          <Link to="/admin/investment/equity" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <ChartPieIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Equity Management</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Manage equity distribution and dilution
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}