import { useState } from 'react';
import { EnvelopeIcon, PhoneIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { QuestionMarkCircleIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/solid';
import '../../styles/vendor-dashboard.css';

export default function VendorSupport() {
  const [openFaq, setOpenFaq] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  // Toggle FAQ item
  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, you would send this data to your backend

    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
    // Show success message (in a real app)
    alert('Your support request has been submitted. We will get back to you soon!');
  };

  // FAQ data
  const faqs = [
    {
      question: 'How do I add products to my store?',
      answer: 'You can add products by navigating to the Products section in your vendor dashboard. Click on "Add New Product" and fill in the required details including product name, description, price, and images.'
    },
    {
      question: 'How do I process customer orders?',
      answer: 'Customer orders can be managed in the Orders section. You can view new orders, update their status, and communicate with customers directly through the built-in messaging system.'
    },
    {
      question: 'How do I customize my store appearance?',
      answer: 'You can customize your store appearance in the Settings section. You can update your store name, logo, banner images, and color scheme to match your brand identity.'
    },
    {
      question: 'How do payments work?',
      answer: 'We support multiple payment methods including credit/debit cards, UPI, and bank transfers. Payments are processed securely and funds are transferred to your linked bank account after a standard settlement period of 2-3 business days.'
    },
    {
      question: 'How can I view my sales analytics?',
      answer: 'You can access detailed sales analytics in the Analytics section of your dashboard. View metrics like total sales, popular products, customer demographics, and more to help grow your business.'
    }
  ];

  // Support contact methods
  const contactMethods = [
    {
      name: 'Email Support',
      description: 'Our support team typically responds within 24 hours.',
      icon: EnvelopeIcon,
      contact: '<EMAIL>',
      action: 'Email us',
      href: 'mailto:<EMAIL>'
    },
    {
      name: 'Phone Support',
      description: 'Available Monday-Friday, 9AM-6PM IST',
      icon: PhoneIcon,
      contact: '+91 **********',
      action: 'Call us',
      href: 'tel:+************'
    },
    {
      name: 'Live Chat',
      description: 'Get instant help from our support team',
      icon: ChatBubbleLeftRightIcon,
      contact: 'Available 24/7',
      action: 'Start chat',
      href: '#chat'
    }
  ];

  return (
    <div className="vendor-dashboard" style={{ padding: '16px' }}>
      {/* Page Header */}
      <div className="welcome-section" style={{ padding: '20px', marginBottom: '16px' }}>
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Help & Support</div>
          <h1 className="welcome-title" style={{ marginBottom: '8px' }}>
            Need <span style={{ color: '#25D366' }}>Help</span>?
          </h1>
          <p className="welcome-subtitle" style={{ marginBottom: '0' }}>
            Get assistance with your WhaMart store. Browse our FAQs, contact our support team, or submit a support request.
          </p>
        </div>
      </div>

      {/* Support Options Section */}
      <div className="stats-section" style={{ marginBottom: '16px' }}>
        <div className="section-header" style={{ marginBottom: '12px' }}>
          <h2 className="section-title">Contact Support</h2>
        </div>
        <div className="contact-methods-grid">
          {contactMethods.map((method, index) => (
            <div key={index} className="stat-card" style={{
              padding: '12px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              height: 'auto',
              margin: 0,
              maxWidth: '100%'
            }}>
              <div className="stat-icon-container" style={{
                backgroundColor: index % 2 === 0 ? 'rgba(37, 211, 102, 0.1)' : 'rgba(7, 94, 84, 0.1)',
                width: '40px',
                height: '40px',
                marginBottom: '8px'
              }}>
                <method.icon className="stat-icon" style={{ color: index % 2 === 0 ? '#25D366' : '#075E54', width: '20px', height: '20px' }} />
              </div>
              <h3 className="stat-label" style={{ fontSize: '15px', marginBottom: '4px' }}>{method.name}</h3>
              <p className="stat-value" style={{
                wordBreak: 'break-word',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontSize: '16px',
                marginBottom: '4px',
                fontWeight: '600'
              }}>
                {method.contact}
              </p>
              <p className="stat-description" style={{
                marginBottom: '8px',
                fontSize: '13px',
                lineHeight: '1.3'
              }}>
                {method.description}
              </p>
              <a
                href={method.href}
                className="btn btn-primary"
                style={{
                  display: 'inline-block',
                  padding: '6px 12px',
                  backgroundColor: '#25D366',
                  color: 'white',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontSize: '13px',
                  fontWeight: '500',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 2px 8px rgba(37, 211, 102, 0.2)'
                }}
              >
                {method.action}
              </a>
            </div>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="stats-section" style={{ marginBottom: '16px', marginTop: '16px' }}>
        <div className="section-header" style={{ marginBottom: '12px' }}>
          <h2 className="section-title">Frequently Asked Questions</h2>
        </div>
        <div className="card" style={{ padding: '0', margin: 0 }}>
          <div className="faq-container">
            {faqs.map((faq, index) => (
              <div key={index} className="faq-item" style={{
                borderBottom: index < faqs.length - 1 ? '1px solid #e5e7eb' : 'none',
                padding: '12px 16px',
                transition: 'all 0.3s ease',
                backgroundColor: openFaq === index ? 'rgba(37, 211, 102, 0.05)' : 'transparent',
                margin: 0
              }}>
                <button
                  className="faq-question"
                  onClick={() => toggleFaq(index)}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                    textAlign: 'left',
                    padding: '4px 0',
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                    fontWeight: '600',
                    fontSize: '16px',
                    color: openFaq === index ? '#25D366' : '#212529'
                  }}
                >
                  <span style={{ flex: 1, paddingRight: '16px' }}>{faq.question}</span>
                  {openFaq === index ?
                    <ChevronUpIcon style={{ width: '20px', height: '20px', color: '#25D366', flexShrink: 0 }} /> :
                    <ChevronDownIcon style={{ width: '20px', height: '20px', color: '#6C757D', flexShrink: 0 }} />
                  }
                </button>
                {openFaq === index && (
                  <div className="faq-answer" style={{
                    padding: '8px 0 0',
                    color: '#6C757D',
                    fontSize: '14px',
                    lineHeight: '1.5'
                  }}>
                    {faq.answer}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Support Request Form */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Submit a Support Request</h2>
        </div>
        <div className="card" style={{ padding: '20px' }}>
          <form onSubmit={handleSubmit}>
            <div style={{
              marginBottom: '16px'
            }}>
              <div className="form-group" style={{ margin: '0 0 16px 0' }}>
                <label htmlFor="name" className="form-label">Your Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter your full name"
                  required
                  style={{ maxWidth: '100%', boxSizing: 'border-box' }}
                />
              </div>
              <div className="form-group" style={{ margin: 0 }}>
                <label htmlFor="email" className="form-label">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter your email address"
                  required
                  style={{ maxWidth: '100%', boxSizing: 'border-box' }}
                />
              </div>
            </div>
            <div className="form-group" style={{ marginBottom: '16px' }}>
              <label htmlFor="subject" className="form-label">Subject</label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                className="form-input"
                placeholder="What is your query about?"
                required
                style={{ maxWidth: '100%', boxSizing: 'border-box' }}
              />
            </div>
            <div className="form-group" style={{ marginBottom: '20px' }}>
              <label htmlFor="message" className="form-label">Message</label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                className="form-textarea"
                placeholder="Please describe your issue in detail..."
                rows="4"
                required
                style={{ maxWidth: '100%', boxSizing: 'border-box' }}
              ></textarea>
            </div>
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <button
                type="submit"
                className="btn btn-primary"
                style={{
                  backgroundColor: '#25D366',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '10px 20px',
                  fontSize: '15px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 2px 8px rgba(37, 211, 102, 0.2)',
                  maxWidth: '100%',
                  boxSizing: 'border-box'
                }}
              >
                Submit Request
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
