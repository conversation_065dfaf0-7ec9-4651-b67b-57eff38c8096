import { useState } from 'react';
import { 
  CogIcon,
  CheckIcon,
  XMarkIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  BellIcon,
  ShieldCheckIcon,
  CurrencyRupeeIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminSettings() {
  // State for different settings sections
  const [activeTab, setActiveTab] = useState('general');
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'WhaMart',
    siteDescription: 'WhatsApp Store Platform for Small Businesses',
    logoUrl: '/WhaMart_Logo.png',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 9876543210',
    address: 'Mumbai, Maharashtra, India'
  });
  
  const [emailSettings, setEmailSettings] = useState({
    smtpServer: 'smtp.example.com',
    smtpPort: '587',
    smtpUsername: '<EMAIL>',
    smtpPassword: '********',
    senderName: 'WhaMart Support',
    senderEmail: '<EMAIL>',
    enableEmailNotifications: true
  });
  
  const [notificationSettings, setNotificationSettings] = useState({
    newVendorRegistration: true,
    newOrder: true,
    subscriptionPurchase: true,
    subscriptionExpiry: true,
    lowBalance: false,
    systemUpdates: true
  });
  
  const [paymentSettings, setPaymentSettings] = useState({
    currency: 'INR',
    currencySymbol: '₹',
    taxRate: 18,
    enableTax: true,
    minimumWithdrawalAmount: 1000,
    withdrawalProcessingDays: 3
  });
  
  const [securitySettings, setSecuritySettings] = useState({
    enableTwoFactorAuth: false,
    passwordExpiryDays: 90,
    maxLoginAttempts: 5,
    sessionTimeoutMinutes: 60,
    requireStrongPasswords: true
  });

  // Handle form input changes
  const handleGeneralChange = (e) => {
    const { name, value } = e.target;
    setGeneralSettings({
      ...generalSettings,
      [name]: value
    });
  };
  
  const handleEmailChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEmailSettings({
      ...emailSettings,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings({
      ...notificationSettings,
      [name]: checked
    });
  };
  
  const handlePaymentChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPaymentSettings({
      ...paymentSettings,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) : value
    });
  };
  
  const handleSecurityChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSecuritySettings({
      ...securitySettings,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseInt(value) : value
    });
  };

  // Handle form submissions
  const handleSaveSettings = (settingType) => {
    // In a real implementation, this would be an API call
    console.log(`Saving ${settingType} settings...`);
    
    // Show success message
    alert(`${settingType.charAt(0).toUpperCase() + settingType.slice(1)} settings saved successfully!`);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Platform Settings</div>
          <h1 className="welcome-title">
            System <span style={{ color: '#25D366' }}>Configuration</span>
          </h1>
          <p className="welcome-subtitle">
            Configure platform settings, email notifications, payment options, and security preferences.
          </p>
        </div>
      </div>

      {/* Settings Tabs */}
      <div className="stats-section">
        <div className="flex border-b border-gray-200 overflow-x-auto">
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'general' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('general')}
          >
            General
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'email' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('email')}
          >
            Email
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'notifications' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('notifications')}
          >
            Notifications
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'payment' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('payment')}
          >
            Payment
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'security' ? 'text-green-600 border-b-2 border-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('security')}
          >
            Security
          </button>
        </div>

        {/* General Settings */}
        {activeTab === 'general' && (
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-6">
              <GlobeAltIcon className="h-6 w-6 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">General Settings</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Site Name
                </label>
                <input
                  type="text"
                  name="siteName"
                  value={generalSettings.siteName}
                  onChange={handleGeneralChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Email
                </label>
                <input
                  type="email"
                  name="contactEmail"
                  value={generalSettings.contactEmail}
                  onChange={handleGeneralChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Phone
                </label>
                <input
                  type="text"
                  name="contactPhone"
                  value={generalSettings.contactPhone}
                  onChange={handleGeneralChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Logo URL
                </label>
                <input
                  type="text"
                  name="logoUrl"
                  value={generalSettings.logoUrl}
                  onChange={handleGeneralChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Site Description
                </label>
                <textarea
                  name="siteDescription"
                  value={generalSettings.siteDescription}
                  onChange={handleGeneralChange}
                  rows="2"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                ></textarea>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <textarea
                  name="address"
                  value={generalSettings.address}
                  onChange={handleGeneralChange}
                  rows="2"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                ></textarea>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => handleSaveSettings('general')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}

        {/* Email Settings */}
        {activeTab === 'email' && (
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-6">
              <EnvelopeIcon className="h-6 w-6 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Email Settings</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SMTP Server
                </label>
                <input
                  type="text"
                  name="smtpServer"
                  value={emailSettings.smtpServer}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SMTP Port
                </label>
                <input
                  type="text"
                  name="smtpPort"
                  value={emailSettings.smtpPort}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SMTP Username
                </label>
                <input
                  type="text"
                  name="smtpUsername"
                  value={emailSettings.smtpUsername}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SMTP Password
                </label>
                <input
                  type="password"
                  name="smtpPassword"
                  value={emailSettings.smtpPassword}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sender Name
                </label>
                <input
                  type="text"
                  name="senderName"
                  value={emailSettings.senderName}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sender Email
                </label>
                <input
                  type="email"
                  name="senderEmail"
                  value={emailSettings.senderEmail}
                  onChange={handleEmailChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableEmailNotifications"
                    name="enableEmailNotifications"
                    checked={emailSettings.enableEmailNotifications}
                    onChange={handleEmailChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableEmailNotifications" className="ml-2 block text-sm text-gray-900">
                    Enable Email Notifications
                  </label>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => handleSaveSettings('email')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}

        {/* Notification Settings */}
        {activeTab === 'notifications' && (
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-6">
              <BellIcon className="h-6 w-6 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Notification Settings</h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">New Vendor Registration</h3>
                  <p className="text-sm text-gray-500">Receive notifications when a new vendor registers</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="newVendorRegistration"
                    name="newVendorRegistration"
                    checked={notificationSettings.newVendorRegistration}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">New Order</h3>
                  <p className="text-sm text-gray-500">Receive notifications for new orders</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="newOrder"
                    name="newOrder"
                    checked={notificationSettings.newOrder}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Subscription Purchase</h3>
                  <p className="text-sm text-gray-500">Receive notifications when a vendor purchases a subscription</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="subscriptionPurchase"
                    name="subscriptionPurchase"
                    checked={notificationSettings.subscriptionPurchase}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Subscription Expiry</h3>
                  <p className="text-sm text-gray-500">Receive notifications when a vendor's subscription is about to expire</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="subscriptionExpiry"
                    name="subscriptionExpiry"
                    checked={notificationSettings.subscriptionExpiry}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Low Balance</h3>
                  <p className="text-sm text-gray-500">Receive notifications when account balance is low</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="lowBalance"
                    name="lowBalance"
                    checked={notificationSettings.lowBalance}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between py-2 border-b">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">System Updates</h3>
                  <p className="text-sm text-gray-500">Receive notifications about system updates and maintenance</p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <input
                    type="checkbox"
                    id="systemUpdates"
                    name="systemUpdates"
                    checked={notificationSettings.systemUpdates}
                    onChange={handleNotificationChange}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => handleSaveSettings('notification')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}

        {/* Payment Settings */}
        {activeTab === 'payment' && (
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-6">
              <CurrencyRupeeIcon className="h-6 w-6 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Payment Settings</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <select
                  name="currency"
                  value={paymentSettings.currency}
                  onChange={handlePaymentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="INR">Indian Rupee (INR)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                  <option value="GBP">British Pound (GBP)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency Symbol
                </label>
                <input
                  type="text"
                  name="currencySymbol"
                  value={paymentSettings.currencySymbol}
                  onChange={handlePaymentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Rate (%)
                </label>
                <input
                  type="number"
                  name="taxRate"
                  value={paymentSettings.taxRate}
                  onChange={handlePaymentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Withdrawal Amount
                </label>
                <input
                  type="number"
                  name="minimumWithdrawalAmount"
                  value={paymentSettings.minimumWithdrawalAmount}
                  onChange={handlePaymentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Withdrawal Processing Days
                </label>
                <input
                  type="number"
                  name="withdrawalProcessingDays"
                  value={paymentSettings.withdrawalProcessingDays}
                  onChange={handlePaymentChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableTax"
                  name="enableTax"
                  checked={paymentSettings.enableTax}
                  onChange={handlePaymentChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="enableTax" className="ml-2 block text-sm text-gray-900">
                  Enable Tax Calculation
                </label>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => handleSaveSettings('payment')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}

        {/* Security Settings */}
        {activeTab === 'security' && (
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-6">
              <ShieldCheckIcon className="h-6 w-6 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Security Settings</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password Expiry (Days)
                </label>
                <input
                  type="number"
                  name="passwordExpiryDays"
                  value={securitySettings.passwordExpiryDays}
                  onChange={handleSecurityChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Login Attempts
                </label>
                <input
                  type="number"
                  name="maxLoginAttempts"
                  value={securitySettings.maxLoginAttempts}
                  onChange={handleSecurityChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Session Timeout (Minutes)
                </label>
                <input
                  type="number"
                  name="sessionTimeoutMinutes"
                  value={securitySettings.sessionTimeoutMinutes}
                  onChange={handleSecurityChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableTwoFactorAuth"
                  name="enableTwoFactorAuth"
                  checked={securitySettings.enableTwoFactorAuth}
                  onChange={handleSecurityChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="enableTwoFactorAuth" className="ml-2 block text-sm text-gray-900">
                  Enable Two-Factor Authentication
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireStrongPasswords"
                  name="requireStrongPasswords"
                  checked={securitySettings.requireStrongPasswords}
                  onChange={handleSecurityChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="requireStrongPasswords" className="ml-2 block text-sm text-gray-900">
                  Require Strong Passwords
                </label>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => handleSaveSettings('security')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
