<?php

namespace App\Http\Controllers;

use App\Models\ChatFlow;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ChatFlowController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for the user.'], 404);
        }

        $chatFlows = ChatFlow::where('store_id', $store->id)->orderBy('last_modified', 'desc')->get();

        return response()->json($chatFlows);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found for the user.'], 404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'flowData' => 'nullable',
            'nodes' => 'nullable|array',
            'edges' => 'nullable|array',
            'isDefault' => 'nullable|boolean',
        ]);

        $flowData = $this->processFlowData($request);

        // If this is set as default, unset any existing default
        if ($request->input('isDefault', false)) {
            ChatFlow::where('store_id', $store->id)->update(['is_default' => false]);
        }

        $chatFlow = ChatFlow::create([
            'store_id' => $store->id,
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'flow_data' => $flowData,
            'is_default' => $request->input('isDefault', false),
            'nodes_count' => count($flowData['nodes'] ?? []),
            'last_modified' => Carbon::now(),
        ]);

        return response()->json([
            'message' => 'Chat flow created successfully',
            'chatFlow' => $chatFlow
        ], 201);
    }

    private function processFlowData(Request $request): array
    {
        $flowData = $request->input('flowData');

        // Case 1: flowData is a JSON string
        if (is_string($flowData)) {
            $decodedData = json_decode($flowData, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $flowData = $decodedData;
            } else {
                $flowData = ['nodes' => [], 'edges' => []];
            }
        }

        // Case 2: nodes and edges are passed directly
        if (!$flowData && $request->has('nodes')) {
             $flowData = [
                'nodes' => $request->input('nodes', []),
                'edges' => $request->input('edges', [])
            ];
        }

        // Case 3: Handle nested flowData object (e.g., flowData: { flowData: { ... } })
        if (isset($flowData['flowData']) && is_array($flowData['flowData'])) {
            $flowData = $flowData['flowData'];
        }

        // Ensure nodes and edges exist
        $flowData['nodes'] = $flowData['nodes'] ?? [];
        $flowData['edges'] = $flowData['edges'] ?? [];

        // Process nodes to validate image data
        if (is_array($flowData['nodes'])) {
            $flowData['nodes'] = array_map(function ($node) {
                if (isset($node['data']['image'])) {
                    $image = $node['data']['image'];
                    if (!is_string($image) || !str_starts_with($image, 'data:image/')) {
                        $node['data']['image'] = null; // Invalidate if not a proper data URL
                    }
                }
                return $node;
            }, $flowData['nodes']);
        }

        return $flowData;
    }

    /**
     * Display the specified resource.
     */
    public function show(ChatFlow $chatFlow)
    {
        $this->authorize('view', $chatFlow);

        return response()->json($chatFlow);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ChatFlow $chatFlow)
    {
        $this->authorize('update', $chatFlow);

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'flowData' => 'nullable',
            'nodes' => 'nullable|array',
            'edges' => 'nullable|array',
            'isDefault' => 'nullable|boolean',
            'isActive' => 'nullable|boolean',
        ]);

        $flowData = $this->processFlowData($request);

        // If this is set as default, unset any existing default
        if ($request->input('isDefault', false) && !$chatFlow->is_default) {
            ChatFlow::where('store_id', $chatFlow->store_id)->update(['is_default' => false]);
        }

        $chatFlow->update([
            'name' => $validated['name'] ?? $chatFlow->name,
            'description' => $validated['description'] ?? $chatFlow->description,
            'flow_data' => $request->has('flowData') || $request->has('nodes') ? $flowData : $chatFlow->flow_data,
            'is_default' => $request->input('isDefault', $chatFlow->is_default),
            'is_active' => $request->input('isActive', $chatFlow->is_active),
            'nodes_count' => count(($request->has('flowData') || $request->has('nodes') ? $flowData['nodes'] : $chatFlow->flow_data['nodes']) ?? []),
            'last_modified' => Carbon::now(),
        ]);

        return response()->json([
            'message' => 'Chat flow updated successfully',
            'chatFlow' => $chatFlow->fresh()
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ChatFlow $chatFlow)
    {
        $this->authorize('delete', $chatFlow);

        $chatFlow->delete();

        return response()->json(['message' => 'Chat flow deleted successfully']);
    }

    /**
     * Get default chat flow for a store by store URL.
     */
    public function getDefaultChatFlowByStoreUrl(string $storeUrl)
    {
        $store = \App\Models\Store::where('store_url', $storeUrl)->first();

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $chatFlow = ChatFlow::where('store_id', $store->id)
            ->where('is_default', true)
            ->where('is_active', true)
            ->first();

        if (!$chatFlow) {
            $chatFlow = ChatFlow::where('store_id', $store->id)
                ->where('is_active', true)
                ->orderBy('last_modified', 'desc')
                ->first();
        }

        if (!$chatFlow) {
            return response()->json(['message' => 'No active chat flow found for this store'], 404);
        }

        return response()->json(['chatFlow' => $chatFlow]);
    }
}
