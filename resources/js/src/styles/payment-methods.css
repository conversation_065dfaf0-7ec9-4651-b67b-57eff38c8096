/* Payment Methods Styles */

.payment-methods-container {
  margin-top: 1.5rem;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  text-align: center;
}

.empty-state-icon {
  margin-bottom: 1rem;
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 50%;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.empty-state-description {
  color: #6b7280;
  max-width: 24rem;
  margin-bottom: 1rem;
}

/* Payment method card */
.payment-method-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.payment-method-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.payment-method-icon {
  background-color: #f0fdf4;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-right: 1rem;
}

.payment-method-info {
  flex: 1;
}

.payment-method-name {
  font-weight: 600;
  font-size: 1rem;
  color: #111827;
  margin: 0;
}

.payment-method-type {
  color: #6b7280;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  margin: 0;
}

.default-badge {
  background-color: #ecfdf5;
  color: #059669;
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
  margin-left: 0.5rem;
}

.payment-method-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.action-button.edit:hover {
  background-color: #f3f4f6;
  color: #4f46e5;
}

.action-button.delete:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.payment-method-details {
  padding: 1rem;
  background-color: #f9fafb;
}

.detail-item {
  display: flex;
  margin-bottom: 0.5rem;
}

.detail-label {
  font-weight: 500;
  color: #4b5563;
  width: 40%;
}

.detail-value {
  color: #111827;
}

.qr-code-preview {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.qr-code-image {
  width: 150px;
  height: 150px;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: white;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 32rem;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.modal-close:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Form styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-hint {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.payment-type-selector {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.payment-type-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-type-button span {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.payment-type-button.active {
  border-color: #4f46e5;
  background-color: #f5f3ff;
}

.payment-type-button:hover:not(.active) {
  background-color: #f9fafb;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 0.5rem;
}

.checkbox-label {
  font-size: 0.875rem;
  color: #374151;
}
