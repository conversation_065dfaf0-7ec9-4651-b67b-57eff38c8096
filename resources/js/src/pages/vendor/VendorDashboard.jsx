import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Link } from 'react-router-dom';
import {
  ShoppingBagIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  PhoneIcon,
  QrCodeIcon,
  ShareIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { dashboardAPI } from '../../services/api';
import '../../styles/vendor-dashboard.css';
import '../../index.css';

export default function VendorDashboard() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await dashboardAPI.getVendorStats();
        setDashboardData(response.data);
        setLoading(false);
      } catch (err) {
//         // Removed console.error
        setError('Failed to load dashboard data. Please try again.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Define stats with real data or fallbacks
  const stats = [
    {
      name: 'Total Products',
      stat: dashboardData?.stats?.productCount || '0',
      icon: ShoppingBagIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Products in your catalog'
    },
    {
      name: 'Active Chats',
      stat: dashboardData?.stats?.chatFlowCount || '0',
      icon: ChatBubbleLeftRightIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Chat flows in your store'
    },
    {
      name: 'Total Revenue',
      stat: dashboardData?.stats?.totalRevenue || '₹0.00',
      icon: CurrencyDollarIcon,
      color: 'rgba(37, 211, 102, 0.1)',
      iconColor: '#25D366',
      description: 'Total earnings'
    },
    {
      name: 'Total Customers',
      stat: dashboardData?.stats?.customerCount || '0',
      icon: UserGroupIcon,
      color: 'rgba(7, 94, 84, 0.1)',
      iconColor: '#075E54',
      description: 'Unique customers'
    },
  ];

  // Get recent orders from API or use empty array
  const recentOrders = dashboardData?.recentOrders || [];

  // Loading state
  if (loading) {
    return (
      <div className="vendor-dashboard">
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <p>Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="vendor-dashboard">
      {/* Error message */}
      {error && (
        <div className="alert alert-danger" style={{ marginBottom: '20px', padding: '12px', borderRadius: '8px', backgroundColor: 'rgba(220, 53, 69, 0.1)', color: '#dc3545', border: '1px solid #dc3545' }}>
          {error}
        </div>
      )}

      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Dashboard Overview</div>
          <h1 className="welcome-title">
            Welcome back, <span style={{ color: '#25D366' }}>{currentUser?.name || 'Vendor'}</span>
          </h1>
          <p className="welcome-subtitle">
            Here's an overview of your WhatsApp store's performance. Monitor your sales, manage products, and grow your business.
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Store Performance</h2>
        </div>
        <div className="stats-grid">
          {stats.map((item) => (
            <div key={item.name} className="stat-card">
              <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
              </div>
              <div className="stat-content">
                <h3 className="stat-label">{item.name}</h3>
                <p className="stat-value">{item.stat}</p>
                <p className="stat-description">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Orders Section */}
      <div className="orders-section">
        <div className="section-header">
          <h2 className="section-title">Recent Orders</h2>
          <Link to="/vendor/orders" className="btn btn-outline">
            View all orders
            <ArrowRightIcon style={{ width: '16px', height: '16px' }} />
          </Link>
        </div>
        <div className="card">
          {recentOrders.length > 0 ? (
            <ul className="orders-list">
              {recentOrders.map((order) => (
                <li key={order.id} className="order-item">
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div>
                      <p style={{ fontSize: '16px', fontWeight: '600', color: '#212529', margin: '0 0 4px 0' }}>
                        Order #{order.orderNumber || order.id}
                      </p>
                      <p style={{ fontSize: '14px', color: '#6C757D', margin: '0' }}>
                        {order.customer}
                      </p>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                      <div>
                        <p style={{ fontSize: '16px', fontWeight: '600', color: '#212529', margin: '0 0 4px 0', textAlign: 'right' }}>
                          {order.amount}
                        </p>
                        <p style={{ fontSize: '14px', color: '#6C757D', margin: '0', textAlign: 'right' }}>
                          {order.items} {order.items === 1 ? 'item' : 'items'}
                        </p>
                      </div>
                      <div>
                        <span className={`status-badge ${
                          order.status === 'completed' ? 'status-completed' :
                          order.status === 'processing' ? 'status-processing' :
                          order.status === 'shipped' ? 'status-processing' :
                          'status-pending'
                        }`}>
                          {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Pending'}
                        </span>
                        <p style={{ fontSize: '12px', color: '#6C757D', margin: '4px 0 0 0', textAlign: 'right' }}>
                          {order.date ? new Date(order.date).toLocaleDateString() : 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div style={{ padding: '20px', textAlign: 'center', color: '#6C757D' }}>
              <p>No recent orders found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Store Preview Section */}
      <div className="store-preview">
        <div className="card">
          <div className="store-card">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px' }}>
              <div className="welcome-badge">
                Store Status: {dashboardData?.store?.isActive !== false ? 'Active' : 'Inactive'}
              </div>
              <h2 className="section-title">
                {dashboardData?.store?.name || 'Your WhatsApp Store'}
              </h2>
              <p style={{ fontSize: '15px', color: '#6C757D', margin: '8px 0 0 0', maxWidth: '600px' }}>
                Your WhatsApp-style store is active and ready to receive customers. Share your store link with customers to start selling.
              </p>
            </div>

            <div className="store-actions">
              <a
                href={`https://wa.me/?text=Check%20out%20my%20store%20at%20${encodeURIComponent(window.location.origin + '/store/' + (dashboardData?.store?.storeUrl || ''))}`}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary"
              >
                <PhoneIcon style={{ width: '16px', height: '16px' }} />
                Open in WhatsApp
              </a>
              <Link to="/vendor/store" className="btn btn-outline">
                <QrCodeIcon style={{ width: '16px', height: '16px' }} />
                Manage Store
              </Link>
              <button
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: dashboardData?.store?.name || 'My WhatsApp Store',
                      text: 'Check out my WhatsApp store!',
                      url: window.location.origin + '/store/' + (dashboardData?.store?.storeUrl || '')
                    });
                  } else {
                    // Fallback - copy to clipboard
                    navigator.clipboard.writeText(window.location.origin + '/store/' + (dashboardData?.store?.storeUrl || ''));
                    alert('Store link copied to clipboard!');
                  }
                }}
                className="btn btn-outline"
              >
                <ShareIcon style={{ width: '16px', height: '16px' }} />
                Share Store
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Quick Actions</h2>
        </div>
        <div className="stats-grid">
          <Link to="/vendor/products/add" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <ShoppingBagIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Add New Product</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Add a new product to your store catalog
              </p>
            </div>
          </Link>

          <Link to="/vendor/orders" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <ChatBubbleLeftRightIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Manage Orders</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                View and process customer orders
              </p>
            </div>
          </Link>

          <Link to="/vendor/analytics" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
              <CurrencyDollarIcon style={{ color: '#25D366', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">View Analytics</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                Check your store's performance metrics
              </p>
            </div>
          </Link>

          <Link to="/vendor/customers" className="stat-card" style={{ textDecoration: 'none' }}>
            <div className="stat-icon-container" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
              <UserGroupIcon style={{ color: '#075E54', width: '24px', height: '24px' }} aria-hidden="true" />
            </div>
            <div className="stat-content">
              <h3 className="stat-label">Customer Management</h3>
              <p style={{ fontSize: '14px', color: '#6C757D', margin: '4px 0 0 0' }}>
                View and manage your customer database
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
