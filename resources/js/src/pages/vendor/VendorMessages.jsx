import { useState } from 'react';
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';
import { UserCircleIcon } from '@heroicons/react/24/outline';

export default function VendorMessages() {
  const [selectedChat, setSelectedChat] = useState(1);
  const [messageText, setMessageText] = useState('');

  // Mock chat data
  const chats = [
    { id: 1, name: '<PERSON>', lastMessage: 'Do you have this in blue?', unread: 2, time: '10:30 AM' },
    { id: 2, name: '<PERSON>', lastMessage: 'Thanks for the quick delivery!', unread: 0, time: 'Yesterday' },
    { id: 3, name: '<PERSON>', lastMessage: 'I would like to order 5 more.', unread: 1, time: 'Yesterday' },
    { id: 4, name: '<PERSON>', lastMessage: 'Is this still available?', unread: 0, time: 'Monday' },
    { id: 5, name: '<PERSON>', lastMessage: 'Perfect, thank you!', unread: 0, time: 'Sunday' },
  ];

  // Mock messages for the selected chat
  const messages = [
    { id: 1, sender: 'customer', text: 'Hi, I am interested in your products.', time: '10:15 AM' },
    { id: 2, sender: 'vendor', text: 'Hello! Thank you for your interest. How can I help you today?', time: '10:17 AM' },
    { id: 3, sender: 'customer', text: 'Do you have the t-shirt in blue?', time: '10:20 AM' },
    { id: 4, sender: 'vendor', text: 'Yes, we do have it in blue. Would you like to see some pictures?', time: '10:22 AM' },
    { id: 5, sender: 'customer', text: 'That would be great!', time: '10:25 AM' },
    { id: 6, sender: 'vendor', text: 'Here are some pictures of our blue t-shirts. [Images]', time: '10:28 AM' },
    { id: 7, sender: 'customer', text: 'Do you have this in blue?', time: '10:30 AM' },
  ];

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (messageText.trim() === '') return;

    // In a real app, you would send the message to the backend
//     // Removed console.log

    // Clear the input
    setMessageText('');
  };

  return (
    <div className="flex h-[calc(100vh-120px)]">
      {/* Chat list */}
      <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Messages</h2>
        </div>
        <ul className="divide-y divide-gray-200">
          {chats.map((chat) => (
            <li
              key={chat.id}
              className={`cursor-pointer hover:bg-gray-50 ${selectedChat === chat.id ? 'bg-gray-50' : ''}`}
              onClick={() => setSelectedChat(chat.id)}
            >
              <div className="px-4 py-4 flex items-center">
                <div className="flex-shrink-0">
                  <UserCircleIcon className="h-10 w-10 text-gray-400" />
                </div>
                <div className="ml-3 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">{chat.name}</p>
                    <p className="text-xs text-gray-500">{chat.time}</p>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500 truncate">{chat.lastMessage}</p>
                    {chat.unread > 0 && (
                      <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-whatsapp-teal text-xs font-medium text-white">
                        {chat.unread}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Chat messages */}
      <div className="flex-1 flex flex-col">
        {/* Chat header */}
        <div className="p-4 border-b border-gray-200 flex items-center">
          <UserCircleIcon className="h-8 w-8 text-gray-400" />
          <div className="ml-3">
            <h2 className="text-lg font-medium text-gray-900">
              {chats.find(chat => chat.id === selectedChat)?.name}
            </h2>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'vendor' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-4 py-2 rounded-lg ${
                    message.sender === 'vendor'
                      ? 'bg-whatsapp-teal text-white'
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <p className={`text-xs mt-1 text-right ${
                    message.sender === 'vendor' ? 'text-gray-100' : 'text-gray-500'
                  }`}>
                    {message.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Message input */}
        <div className="p-4 border-t border-gray-200">
          <form onSubmit={handleSendMessage} className="flex items-center">
            <input
              type="text"
              className="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-whatsapp-default focus:border-transparent"
              placeholder="Type a message..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
            />
            <button
              type="submit"
              className="ml-2 p-2 rounded-full bg-whatsapp-teal text-white hover:bg-whatsapp-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-whatsapp-default"
            >
              <PaperAirplaneIcon className="h-5 w-5" />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
