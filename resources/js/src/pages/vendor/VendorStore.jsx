import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { CurrencyRupeeIcon } from '@heroicons/react/24/solid';
import {
  BuildingStorefrontIcon,
  QrCodeIcon,
  ShareIcon,
  PhotoIcon,
  LinkIcon,
  ShoppingBagIcon,
  HomeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  TruckIcon,
  BookOpenIcon,
  HeartIcon,
  XMarkIcon,
  TrashIcon,
  CakeIcon,
  TagIcon,
  PlusIcon,
  PencilIcon,
  BanknotesIcon,
  CreditCardIcon,
  CubeIcon,
  CloudIcon
} from '@heroicons/react/24/outline';
import { storeAPI, paymentMethodAPI } from '../../services/api';
import { formatStoreNameForUrl, generateStoreUrl, getFullStoreUrl } from '../../utils/urlUtils';
import '../../styles/vendor-dashboard.css';
import '../../styles/dashboard.css';
import '../../styles/payment-methods.css';

export default function VendorStore() {
  const { currentUser, updateUserStore } = useAuth();
  const [storeData, setStoreData] = useState({
    id: '',
    name: '',
    businessCategory: 'grocery',
    businessSubcategory: 'supermarket',
    businessType: 'product',
    productType: 'physical',
    description: '',
    logo: null,
    logoPreview: null,
    logoUrl: null
  });

  const [isEditing, setIsEditing] = useState(false);
  const [storeLink, setStoreLink] = useState('');
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [showPaymentMethodModal, setShowPaymentMethodModal] = useState(false);
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState({
    type: 'upi',
    name: '',
    upiId: '',
    isDefault: true
  });
  const [isEditingPaymentMethod, setIsEditingPaymentMethod] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Business categories with icons
  const businessCategories = [
    { id: 'grocery', name: 'Grocery', icon: ShoppingBagIcon },
    { id: 'fashion', name: 'Fashion', icon: TagIcon },
    { id: 'electronics', name: 'Electronics', icon: ComputerDesktopIcon },
    { id: 'home', name: 'Home & Furniture', icon: HomeIcon },
    { id: 'beauty', name: 'Beauty & Personal Care', icon: HeartIcon },
    { id: 'mobile', name: 'Mobile & Accessories', icon: DevicePhoneMobileIcon },
    { id: 'food', name: 'Food & Beverages', icon: CakeIcon },
    { id: 'books', name: 'Books & Stationery', icon: BookOpenIcon },
    { id: 'toys', name: 'Toys & Baby Products', icon: BuildingStorefrontIcon },
    { id: 'services', name: 'Services', icon: TruckIcon }
  ];

  // Subcategories based on main category
  const subcategories = {
    grocery: ['Supermarket', 'Organic Store', 'Bakery', 'Meat & Seafood', 'Fruits & Vegetables'],
    fashion: ['Men\'s Clothing', 'Women\'s Clothing', 'Kids\' Clothing', 'Footwear', 'Accessories'],
    electronics: ['Smartphones', 'Laptops', 'Audio', 'Cameras', 'Accessories'],
    home: ['Furniture', 'Decor', 'Kitchen', 'Bedding', 'Lighting'],
    beauty: ['Skincare', 'Makeup', 'Haircare', 'Fragrances', 'Personal Care'],
    mobile: ['Smartphones', 'Cases & Covers', 'Chargers', 'Screen Guards', 'Accessories'],
    food: ['Restaurant', 'Bakery', 'Cafe', 'Fast Food', 'Desserts'],
    books: ['Fiction', 'Non-Fiction', 'Educational', 'Stationery', 'Gifts'],
    toys: ['Toys', 'Baby Care', 'Kids Furniture', 'School Supplies', 'Games'],
    services: ['Repair', 'Cleaning', 'Delivery', 'Professional', 'Home Services']
  };

  // Fetch store data and payment methods from backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch store data
        const storeResponse = await storeAPI.getStoreInfo();
        const store = storeResponse.data.store;

        // Prepare the logo preview URL
        let logoPreviewUrl = null;
        if (store.logoUrl) {
          // Make sure we have the correct URL format
          logoPreviewUrl = store.logoUrl.startsWith('http')
            ? store.logoUrl
            : `https://api.whamart.shop${store.logoUrl.startsWith('/') ? store.logoUrl : '/' + store.logoUrl}`;
        }

        setStoreData({
          id: store.id,
          name: store.name,
          businessCategory: store.businessCategory || 'grocery',
          businessSubcategory: store.businessSubcategory || 'supermarket',
          businessType: store.businessType || 'product',
          productType: store.productType || 'physical',
          description: store.description || '',
          logo: null,
          logoPreview: logoPreviewUrl,
          logoUrl: store.logoUrl
        });

        // Set store link
        if (store.storeUrl) {
          setStoreLink(getFullStoreUrl(store.storeUrl));
        }

        // Fetch payment methods
        const paymentResponse = await paymentMethodAPI.getPaymentMethods();
        const methods = paymentResponse.data.paymentMethods;

        // Process payment methods and prepare QR code URLs
        const processedMethods = methods.map(method => {
          let qrCodeUrl = null;
          if (method.qrCodeUrl) {
            qrCodeUrl = method.qrCodeUrl.startsWith('http')
              ? method.qrCodeUrl
              : `https://api.whamart.shop${method.qrCodeUrl.startsWith('/') ? method.qrCodeUrl : '/' + method.qrCodeUrl}`;
          }
          return { ...method, qrCodePreview: qrCodeUrl };
        });

        setPaymentMethods(processedMethods);

        setLoading(false);
      } catch (err) {
        setError('Failed to load store data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Update store data
    const updatedStoreData = {
      ...storeData,
      [name]: value
    };

    setStoreData(updatedStoreData);

    // If store name changes, update the store link
    if (name === 'name') {
      // Generate store URL using the utility function
      const storeUrl = generateStoreUrl(value, storeData.id);
      setStoreLink(getFullStoreUrl(storeUrl));
    }
  };

  // Handle category change
  const handleCategoryChange = (categoryId) => {
    setStoreData({
      ...storeData,
      businessCategory: categoryId,
      businessSubcategory: subcategories[categoryId][0].toLowerCase()
    });
  };

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // Validate file is an image
        if (!file.type.startsWith('image/')) {
          setError('Please select a valid image file');
          return;
        }

        // Create object URL for preview
        const objectUrl = URL.createObjectURL(file);

        setStoreData({
          ...storeData,
          logo: file,
          logoPreview: objectUrl
        });
      } catch (error) {
        setError('Failed to preview logo. Please try again.');
      }
    }
  };

  // Remove logo
  const handleRemoveLogo = () => {
    // Revoke the object URL to avoid memory leaks
    if (storeData.logoPreview && !storeData.logoPreview.includes('https://api.whamart.shop')) {
      try {
        URL.revokeObjectURL(storeData.logoPreview);
      } catch (error) {
        // Silent error handling
      }
    }

    setStoreData({
      ...storeData,
      logo: null,
      logoPreview: null
    });
  };

  // Toggle edit mode
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  // Save store information
  const saveStoreInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure productType is set correctly based on businessType
      let updatedStoreData = { ...storeData };

      // Log current state before update
      console.log('Current store data before update:', {
        businessType: updatedStoreData.businessType,
        productType: updatedStoreData.productType
      });

      if (updatedStoreData.businessType === 'product') {
        // For product-based businesses, ensure we have a valid productType
        if (!updatedStoreData.productType || updatedStoreData.productType === null) {
          updatedStoreData.productType = 'physical';
          console.log('Setting default productType to physical');
        }
      } else if (updatedStoreData.businessType === 'service') {
        // For service-based businesses, explicitly set productType to null
        updatedStoreData.productType = null;
        console.log('Setting productType to null for service business');
      }

      // Check if we have a logo file to upload
      if (updatedStoreData.logo) {
        // Use FormData for file upload
        const formData = new FormData();
        formData.append('name', updatedStoreData.name);
        formData.append('description', updatedStoreData.description || '');
        formData.append('businessCategory', updatedStoreData.businessCategory);
        formData.append('businessSubcategory', updatedStoreData.businessSubcategory);
        formData.append('businessType', updatedStoreData.businessType);

        // Handle productType based on businessType
        if (updatedStoreData.businessType === 'product') {
          formData.append('productType', updatedStoreData.productType || 'physical');
          console.log(`Appending productType to FormData: ${updatedStoreData.productType || 'physical'}`);
        } else {
          // For service businesses, explicitly set productType to null
          // We need to append something to ensure the server knows we want to set it to null
          formData.append('productType', '');
          console.log('Appending empty productType to FormData for service business');
        }

        // Add logo file
        formData.append('logo', updatedStoreData.logo);

        // Log the data being sent
        console.log('Sending store data with logo:', {
          name: updatedStoreData.name,
          businessCategory: updatedStoreData.businessCategory,
          businessSubcategory: updatedStoreData.businessSubcategory,
          businessType: updatedStoreData.businessType,
          productType: updatedStoreData.businessType === 'product' ? (updatedStoreData.productType || 'physical') : null,
          hasLogo: true
        });

        // Send data to backend with FormData
        const response = await storeAPI.updateStoreInfo(formData);
        const updatedStore = response.data.store;
        handleUpdateSuccess(updatedStore);
        return;
      }

      // If no file upload, use regular JSON
      const updateData = {
        name: updatedStoreData.name,
        description: updatedStoreData.description || '',
        businessCategory: updatedStoreData.businessCategory,
        businessSubcategory: updatedStoreData.businessSubcategory,
        businessType: updatedStoreData.businessType,
        productType: updatedStoreData.businessType === 'product' ? (updatedStoreData.productType || 'physical') : null
      };

      // Log the data being sent
      console.log('Sending store data as JSON:', updateData);

      // Send data to backend as JSON
      const response = await storeAPI.updateStoreInfo(updateData);
      const updatedStore = response.data.store;
      handleUpdateSuccess(updatedStore);
    } catch (err) {
      console.error('Error updating store:', err);
      setError('Failed to update store. Please try again.');
      setLoading(false);
    }
  };

  // Handle successful store update
  const handleUpdateSuccess = (updatedStore) => {
    try {
      // Prepare the logo preview URL
      let logoPreviewUrl = null;
      if (updatedStore.logoUrl) {
        // Make sure we have the correct URL format
        logoPreviewUrl = updatedStore.logoUrl.startsWith('http')
          ? updatedStore.logoUrl
          : `https://api.whamart.shop${updatedStore.logoUrl.startsWith('/') ? updatedStore.logoUrl : '/' + updatedStore.logoUrl}`;
      }

      setStoreData({
        ...storeData,
        id: updatedStore.id,
        name: updatedStore.name,
        businessCategory: updatedStore.businessCategory,
        businessSubcategory: updatedStore.businessSubcategory,
        businessType: updatedStore.businessType,
        productType: updatedStore.productType,
        description: updatedStore.description,
        logoUrl: updatedStore.logoUrl,
        logoPreview: logoPreviewUrl,
        logo: null // Reset the file input
      });

      // Update store link
      if (updatedStore.storeUrl) {
        setStoreLink(getFullStoreUrl(updatedStore.storeUrl));
      }

      // Update the user's store information in the auth context
      updateUserStore({
        id: updatedStore.id,
        name: updatedStore.name,
        storeUrl: updatedStore.storeUrl,
        businessType: updatedStore.businessType,
        productType: updatedStore.productType
      });

      // Store information updated successfully
      setSuccessMessage('Store information updated successfully!');
      setSuccess(true);
      setLoading(false);
      setIsEditing(false);

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
        setSuccessMessage('');
      }, 3000);
    } catch (err) {
      console.error('Error handling update success:', err);
      setError('Failed to process updated store data.');
      setLoading(false);
    }
  };

  // Payment method functions
  const openAddPaymentMethodModal = () => {
    setCurrentPaymentMethod({
      type: 'upi',
      name: '',
      upiId: '',
      isDefault: paymentMethods.length === 0 // First payment method is default
    });
    setIsEditingPaymentMethod(false);
    setError(null); // Clear any previous errors
    setShowPaymentMethodModal(true);
  };

  const openEditPaymentMethodModal = (method) => {
    setCurrentPaymentMethod({
      ...method
    });
    setIsEditingPaymentMethod(true);
    setError(null); // Clear any previous errors
    setShowPaymentMethodModal(true);
  };

  const closePaymentMethodModal = () => {
    setShowPaymentMethodModal(false);
  };

  const handlePaymentMethodInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCurrentPaymentMethod({
      ...currentPaymentMethod,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const savePaymentMethod = async () => {
    // Validate form
    if (!currentPaymentMethod.name) {
      setError('Please enter a display name for the payment method');
      return;
    }

    if (currentPaymentMethod.type === 'upi' && !currentPaymentMethod.upiId) {
      setError('Please enter a UPI ID');
      return;
    }

    if (currentPaymentMethod.type === 'bank_transfer') {
      if (!currentPaymentMethod.accountName) {
        setError('Please enter the account holder name');
        return;
      }
      if (!currentPaymentMethod.accountNumber) {
        setError('Please enter the account number');
        return;
      }
      if (!currentPaymentMethod.bankName) {
        setError('Please enter the bank name');
        return;
      }
      if (!currentPaymentMethod.ifscCode) {
        setError('Please enter the IFSC code');
        return;
      }
    }

    try {
      setLoading(true);
      setError(null);

      if (isEditingPaymentMethod) {
        // Update existing payment method
        const response = await paymentMethodAPI.updatePaymentMethod(
          currentPaymentMethod.id,
          currentPaymentMethod
        );

        // Format QR code URL if it exists
        let updatedMethod = response.data.paymentMethod;
        if (updatedMethod.qrCodeUrl) {
          updatedMethod.qrCodeUrl = updatedMethod.qrCodeUrl.startsWith('http')
            ? updatedMethod.qrCodeUrl
            : `https://api.whamart.shop${updatedMethod.qrCodeUrl.startsWith('/') ? updatedMethod.qrCodeUrl : '/' + updatedMethod.qrCodeUrl}`;
        }

        // Update the payment methods list
        const updatedMethods = paymentMethods.map(method =>
          method.id === updatedMethod.id ? updatedMethod : method
        );

        setPaymentMethods(updatedMethods);
        setSuccessMessage('Payment method updated successfully!');
        setSuccess(true);
        setTimeout(() => {
          setSuccess(false);
          setSuccessMessage('');
        }, 3000);
      } else {
        // Add new payment method
        const response = await paymentMethodAPI.createPaymentMethod(currentPaymentMethod);

        // Format QR code URL if it exists
        let newMethod = response.data.paymentMethod;
        if (newMethod.qrCodeUrl) {
          newMethod.qrCodeUrl = newMethod.qrCodeUrl.startsWith('http')
            ? newMethod.qrCodeUrl
            : `https://api.whamart.shop${newMethod.qrCodeUrl.startsWith('/') ? newMethod.qrCodeUrl : '/' + newMethod.qrCodeUrl}`;
        }

        setPaymentMethods([...paymentMethods, newMethod]);
        setSuccessMessage('Payment method added successfully!');
        setSuccess(true);
        setTimeout(() => {
          setSuccess(false);
          setSuccessMessage('');
        }, 3000);
      }

      closePaymentMethodModal();
      setLoading(false);
    } catch (err) {
      setError('Failed to save payment method. Please try again.');
      setLoading(false);
    }
  };

  const deletePaymentMethod = async (id) => {
    try {
      setLoading(true);

      // Delete from backend
      await paymentMethodAPI.deletePaymentMethod(id);

      // Update local state
      setPaymentMethods(paymentMethods.filter(method => method.id !== id));

      setSuccessMessage('Payment method deleted successfully!');
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
        setSuccessMessage('');
      }, 3000);
      setLoading(false);
    } catch (err) {
      setError('Failed to delete payment method. Please try again.');
      setLoading(false);
    }
  };

  // Get the selected category icon
  const CategoryIcon = businessCategories.find(cat => cat.id === storeData.businessCategory)?.icon || BuildingStorefrontIcon;

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Store Management</div>
          <h1 className="welcome-title">
            Your WhatsApp Store
          </h1>
          <p className="welcome-subtitle">
            Customize your store information and settings. Your store link will be automatically generated based on your store name.
          </p>

          {/* Success message */}
          {success && (
            <div className="success-message">
              {successMessage || 'Operation completed successfully!'}
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Loading state */}
      {loading && !isEditing && (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading store information...</p>
        </div>
      )}

      {/* Store Information Section */}
      <div className="card">
        <div className="store-card">
          <div className="section-header mb-4">
            <h2 className="section-title">Store Information</h2>
            {!isEditing && (
              <button className="btn btn-primary" onClick={toggleEditMode}>
                <BuildingStorefrontIcon style={{ width: '16px', height: '16px', marginRight: '8px' }} />
                Edit Store Info
              </button>
            )}
          </div>

          {isEditing ? (
            <div className="space-y-6">
              {/* Store Logo Upload */}
              <div className="flex flex-col items-center gap-4 mb-6">
                <div
                  className="w-[200px] h-[200px] rounded-full bg-gray-100 flex items-center justify-center overflow-hidden border-2 border-primary flex-shrink-0"
                  style={{ position: 'relative' }}
                >
                  {storeData.logoPreview ? (
                    <>
                      <img
                        src={storeData.logoPreview}
                        alt="Store Logo"
                        className="w-full h-full object-cover"
                        style={{ width: '200px', height: '200px' }}
                      />
                      <button
                        onClick={handleRemoveLogo}
                        className="absolute top-2 right-2 bg-error text-white rounded-full p-1 shadow-md"
                        style={{ width: '24px', height: '24px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                        title="Remove Logo"
                      >
                        <XMarkIcon style={{ width: '16px', height: '16px' }} />
                      </button>
                    </>
                  ) : (
                    <BuildingStorefrontIcon className="w-20 h-20 text-gray-400" />
                  )}
                </div>
                <div className="flex flex-col gap-2 items-center mt-2">
                  <label className="btn btn-outline cursor-pointer">
                    <PhotoIcon style={{ width: '16px', height: '16px', marginRight: '4px' }} />
                    Upload Logo
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleLogoUpload}
                    />
                  </label>
                  <p className="text-xs text-gray-500">Upload a square image (200x200px)</p>
                </div>
              </div>

              {/* Store Name */}
              <div className="mb-6">
                <label className="form-label">Store Name</label>
                <input
                  type="text"
                  name="name"
                  value={storeData.name}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter your store name"
                />
              </div>

              {/* Store Link (Generated) */}
              <div className="mb-6">
                <label className="form-label">Store Link</label>
                <div className="flex items-center">
                  <div className="form-input bg-gray-50 store-link-container">
                    <div className="store-link-icon">
                      <LinkIcon className="w-3 h-3 text-primary" />
                    </div>
                    <span className="text-gray-500 truncate">{storeLink}</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">This link is automatically generated based on your store name and ID.</p>
              </div>

              {/* Business Category */}
              <div>
                <label className="form-label">Business Category</label>
                <div className="mt-3">
                  {/* First row - 5 categories */}
                  <div className="flex flex-wrap justify-between mb-6">
                    {businessCategories.slice(0, 5).map(category => (
                      <button
                        key={category.id}
                        type="button"
                        onClick={() => handleCategoryChange(category.id)}
                        className="flex flex-col items-center cursor-pointer transition-transform hover:scale-105 mb-4 sm:mb-0"
                        style={{ width: '18%' }}
                      >
                        <div className={`p-2 rounded-lg ${
                          storeData.businessCategory === category.id
                            ? 'bg-primary-light-highlight'
                            : 'bg-white shadow-sm'
                        }`}>
                          <category.icon
                            className={`w-10 h-10 ${
                              storeData.businessCategory === category.id
                                ? 'text-primary'
                                : 'text-gray-600'
                            }`}
                          />
                        </div>
                        <div className="bg-white px-2 py-1 mt-2 rounded-md shadow-sm w-full">
                          <span className={`text-xs font-medium text-center block ${storeData.businessCategory === category.id ? 'text-primary' : ''}`}>{category.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>

                  {/* Second row - 5 categories */}
                  <div className="flex flex-wrap justify-between">
                    {businessCategories.slice(5, 10).map(category => (
                      <button
                        key={category.id}
                        type="button"
                        onClick={() => handleCategoryChange(category.id)}
                        className="flex flex-col items-center cursor-pointer transition-transform hover:scale-105"
                        style={{ width: '18%' }}
                      >
                        <div className={`p-2 rounded-lg ${
                          storeData.businessCategory === category.id
                            ? 'bg-primary-light-highlight'
                            : 'bg-white shadow-sm'
                        }`}>
                          <category.icon
                            className={`w-10 h-10 ${
                              storeData.businessCategory === category.id
                                ? 'text-primary'
                                : 'text-gray-600'
                            }`}
                          />
                        </div>
                        <div className="bg-white px-2 py-1 mt-2 rounded-md shadow-sm w-full">
                          <span className={`text-xs font-medium text-center block ${storeData.businessCategory === category.id ? 'text-primary' : ''}`}>{category.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Business Subcategory */}
              <div>
                <label className="form-label">Business Subcategory</label>
                <div className="relative">
                  <select
                    name="businessSubcategory"
                    value={storeData.businessSubcategory}
                    onChange={handleInputChange}
                    className="form-select appearance-none pr-10 bg-white"
                    style={{
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                      padding: '10px 16px',
                      width: '100%',
                      fontSize: '14px'
                    }}
                  >
                    {subcategories[storeData.businessCategory]?.map((subcat, index) => (
                      <option key={index} value={subcat.toLowerCase()}>
                        {subcat}
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-500">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Business Type */}
              <div className="mt-6">
                <label className="form-label">Business Type</label>
                <div className="flex gap-4 mt-2">
                  <div
                    className={`flex items-center gap-2 p-3 rounded-lg cursor-pointer border ${storeData.businessType === 'product' ? 'border-primary bg-primary-light-highlight' : 'border-gray-200'}`}
                    onClick={() => {
                      console.log('Switching to product-based business');
                      // Keep the existing productType if switching back to product
                      const newProductType = storeData.businessType === 'product' ? storeData.productType : 'physical';
                      setStoreData({
                        ...storeData,
                        businessType: 'product',
                        productType: newProductType
                      });
                    }}
                    style={{ width: '48%' }}
                  >
                    <ShoppingBagIcon className={`w-5 h-5 ${storeData.businessType === 'product' ? 'text-primary' : 'text-gray-500'}`} />
                    <div>
                      <div className={`font-medium ${storeData.businessType === 'product' ? 'text-primary' : 'text-gray-700'}`}>Product Based</div>
                      <div className="text-xs text-gray-500">Sell physical or digital products</div>
                    </div>
                  </div>

                  <div
                    className={`flex items-center gap-2 p-3 rounded-lg cursor-pointer border ${storeData.businessType === 'service' ? 'border-primary bg-primary-light-highlight' : 'border-gray-200'}`}
                    onClick={() => {
                      console.log('Switching to service-based business');
                      setStoreData({
                        ...storeData,
                        businessType: 'service',
                        productType: null // Clear product type when switching to service
                      });
                    }}
                    style={{ width: '48%' }}
                  >
                    <TruckIcon className={`w-5 h-5 ${storeData.businessType === 'service' ? 'text-primary' : 'text-gray-500'}`} />
                    <div>
                      <div className={`font-medium ${storeData.businessType === 'service' ? 'text-primary' : 'text-gray-700'}`}>Service Based</div>
                      <div className="text-xs text-gray-500">Offer services to customers</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Type - Only show when business type is product */}
              {storeData.businessType === 'product' && (
                <div className="mt-6">
                  <label className="form-label">Product Type</label>
                  <div className="flex gap-4 mt-2">
                    <div
                      className={`flex items-center gap-2 p-3 rounded-lg cursor-pointer border ${storeData.productType === 'physical' ? 'border-primary bg-primary-light-highlight' : 'border-gray-200'}`}
                      onClick={() => {
                        console.log('Setting product type to physical');
                        setStoreData({
                          ...storeData,
                          productType: 'physical'
                        });
                      }}
                      style={{ width: '48%' }}
                    >
                      <CubeIcon className={`w-5 h-5 ${storeData.productType === 'physical' ? 'text-primary' : 'text-gray-500'}`} />
                      <div>
                        <div className={`font-medium ${storeData.productType === 'physical' ? 'text-primary' : 'text-gray-700'}`}>Physical Products</div>
                        <div className="text-xs text-gray-500">Tangible items that need shipping</div>
                      </div>
                    </div>

                    <div
                      className={`flex items-center gap-2 p-3 rounded-lg cursor-pointer border ${storeData.productType === 'digital' ? 'border-primary bg-primary-light-highlight' : 'border-gray-200'}`}
                      onClick={() => {
                        console.log('Setting product type to digital');
                        setStoreData({
                          ...storeData,
                          productType: 'digital'
                        });
                      }}
                      style={{ width: '48%' }}
                    >
                      <CloudIcon className={`w-5 h-5 ${storeData.productType === 'digital' ? 'text-primary' : 'text-gray-500'}`} />
                      <div>
                        <div className={`font-medium ${storeData.productType === 'digital' ? 'text-primary' : 'text-gray-700'}`}>Digital Products</div>
                        <div className="text-xs text-gray-500">Downloadable or virtual items</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}



              {/* Description */}
              <div>
                <label className="form-label">Description</label>
                <textarea
                  name="description"
                  value={storeData.description}
                  onChange={handleInputChange}
                  className="form-textarea"
                  rows="3"
                  placeholder="Describe your business"
                ></textarea>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 mt-6">
                <button
                  className={`btn btn-primary ${loading ? 'btn-loading' : ''}`}
                  onClick={saveStoreInfo}
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  className="btn btn-outline"
                  onClick={toggleEditMode}
                  disabled={loading}
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex flex-col items-center gap-6 mb-6">
                {/* Store Name and Logo */}
                <div className="flex flex-col items-center gap-4">
                  {/* Store Logo */}
                  <div className="w-[200px] h-[200px] rounded-full bg-gray-100 flex items-center justify-center overflow-hidden border-2 border-primary flex-shrink-0 shadow-md">
                    {storeData.logoPreview ? (
                      <img
                        src={storeData.logoPreview}
                        alt="Store Logo"
                        className="w-full h-full object-cover"
                        style={{ width: '200px', height: '200px' }}
                      />
                    ) : (
                      <BuildingStorefrontIcon className="w-20 h-20 text-gray-400" />
                    )}
                  </div>

                  {/* Store Name */}
                  <div className="text-center">
                    <label className="stat-label">Store Name</label>
                    <div className="dashboard-text font-semibold text-lg">{storeData.name}</div>
                  </div>
                </div>

                {/* Store Link */}
                <div className="w-full max-w-md">
                  <label className="stat-label">Store Link</label>
                  <div className="store-link-container">
                    <div className="store-link-icon">
                      <LinkIcon className="w-3 h-3 text-primary" />
                    </div>
                    <a
                      href={storeLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="store-link-text hover:underline"
                      title={storeLink}
                    >
                      {storeLink}
                    </a>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="p-4 border border-gray-100 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <label className="stat-label">Business Category</label>
                    <div className="flex items-center">
                      <label className="text-xs font-medium text-gray mr-2">Subcategory:</label>
                      <span className="text-sm font-medium">
                        {subcategories[storeData.businessCategory]?.find(
                          subcat => subcat.toLowerCase() === storeData.businessSubcategory
                        ) || storeData.businessSubcategory}
                      </span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="flex flex-col items-center">
                      <div className="p-2 rounded-lg bg-primary-light-highlight">
                        <CategoryIcon className="w-10 h-10 text-primary" />
                      </div>
                      <div className="bg-white px-2 py-1 mt-2 rounded-md shadow-sm w-full">
                        <span className="text-xs font-medium text-center block text-primary">
                          {businessCategories.find(cat => cat.id === storeData.businessCategory)?.name}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-light-gray p-4 rounded-lg">
                  <label className="stat-label">Description</label>
                  <div className="dashboard-text">
                    {storeData.description}
                  </div>
                </div>

                {/* Business Type and Product Type */}
                <div className="p-4 border border-gray-100 rounded-lg">
                  <label className="stat-label">Business Type</label>
                  <div className="flex items-center mt-2">
                    {storeData.businessType === 'product' ? (
                      <div className="flex items-center">
                        <ShoppingBagIcon className="w-5 h-5 text-primary mr-2" />
                        <span className="font-medium">Product Based Business</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <TruckIcon className="w-5 h-5 text-primary mr-2" />
                        <span className="font-medium">Service Based Business</span>
                      </div>
                    )}
                  </div>

                  {storeData.businessType === 'product' && (
                    <div className="mt-4">
                      <label className="stat-label">Product Type</label>
                      <div className="flex items-center mt-2">
                        {storeData.productType === 'physical' ? (
                          <div className="flex items-center">
                            <BuildingStorefrontIcon className="w-5 h-5 text-primary mr-2" />
                            <span className="font-medium">Physical Products</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <ComputerDesktopIcon className="w-5 h-5 text-primary mr-2" />
                            <span className="font-medium">Digital Products</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="store-actions mt-6">
                <button className="btn btn-outline">
                  <QrCodeIcon style={{ width: '14px', height: '14px', marginRight: '4px' }} />
                  View QR Code
                </button>
                <button className="btn btn-outline">
                  <ShareIcon style={{ width: '14px', height: '14px', marginRight: '4px' }} />
                  Share Store
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Methods Section */}
      <div className="card mt-8">
        <div className="store-card">
          <div className="section-header mb-4">
            <h2 className="section-title">Payment Methods</h2>
            <button
              className="btn btn-primary"
              onClick={openAddPaymentMethodModal}
              disabled={loading}
            >
              <PlusIcon style={{ width: '16px', height: '16px', marginRight: '8px' }} />
              Add Payment Method
            </button>
          </div>

          <div className="payment-methods-container">
            {paymentMethods.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-icon">
                  <CurrencyRupeeIcon className="w-12 h-12 text-gray-300" />
                </div>
                <h3 className="empty-state-title">No Payment Methods</h3>
                <p className="empty-state-description">
                  Add your UPI ID to generate payment QR codes for your customers.
                </p>
                <button
                  className="btn btn-primary mt-4"
                  onClick={openAddPaymentMethodModal}
                  disabled={loading}
                >
                  <PlusIcon style={{ width: '16px', height: '16px', marginRight: '8px' }} />
                  Add UPI Payment
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {paymentMethods.map(method => (
                  <div key={method.id} className="payment-method-card">
                    <div className="payment-method-header">
                      <div className="payment-method-icon">
                        {method.type === 'upi' ? (
                          <QrCodeIcon className="w-6 h-6 text-primary" />
                        ) : (
                          <BanknotesIcon className="w-6 h-6 text-primary" />
                        )}
                      </div>
                      <div className="payment-method-info">
                        <h3 className="payment-method-name">{method.name}</h3>
                        <p className="payment-method-type">
                          {method.type === 'upi' ? 'UPI Payment' : 'Bank Transfer'}
                          {method.isDefault && <span className="default-badge">Default</span>}
                        </p>
                      </div>
                      <div className="payment-method-actions">
                        <button
                          className="action-button edit"
                          onClick={() => openEditPaymentMethodModal(method)}
                          disabled={loading}
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          className="action-button delete"
                          onClick={() => deletePaymentMethod(method.id)}
                          disabled={loading}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    <div className="payment-method-details">
                      {method.type === 'upi' && (
                        <>
                          <div className="detail-item">
                            <span className="detail-label">UPI ID:</span>
                            <span className="detail-value">{method.upiId}</span>
                          </div>
                          {method.qrCodeUrl && (
                            <div className="qr-code-preview">
                              <img src={method.qrCodeUrl} alt="UPI QR Code" className="qr-code-image" />
                            </div>
                          )}
                        </>
                      )}
                      {method.type === 'bank_transfer' && (
                        <>
                          <div className="detail-item">
                            <span className="detail-label">Account Name:</span>
                            <span className="detail-value">{method.accountName}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Account Number:</span>
                            <span className="detail-value">{method.accountNumber}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">Bank:</span>
                            <span className="detail-value">{method.bankName}</span>
                          </div>
                          <div className="detail-item">
                            <span className="detail-label">IFSC:</span>
                            <span className="detail-value">{method.ifscCode}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment Method Modal */}
      {showPaymentMethodModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h3 className="modal-title">
                {isEditingPaymentMethod ? 'Edit Payment Method' : 'Add Payment Method'}
              </h3>
              <button className="modal-close" onClick={closePaymentMethodModal}>
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label className="form-label">Payment Type</label>
                <div className="payment-type-selector">
                  <button
                    className={`payment-type-button ${currentPaymentMethod.type === 'upi' ? 'active' : ''}`}
                    onClick={() => setCurrentPaymentMethod({...currentPaymentMethod, type: 'upi'})}
                  >
                    <QrCodeIcon className="w-5 h-5" />
                    <span>UPI</span>
                  </button>
                  <button
                    className={`payment-type-button ${currentPaymentMethod.type === 'bank_transfer' ? 'active' : ''}`}
                    onClick={() => setCurrentPaymentMethod({...currentPaymentMethod, type: 'bank_transfer'})}
                  >
                    <BanknotesIcon className="w-5 h-5" />
                    <span>Bank Transfer</span>
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Display Name</label>
                <input
                  type="text"
                  name="name"
                  value={currentPaymentMethod.name}
                  onChange={handlePaymentMethodInputChange}
                  className="form-input"
                  placeholder="e.g., My UPI Payment"
                />
              </div>

              {currentPaymentMethod.type === 'upi' && (
                <div className="form-group">
                  <label className="form-label">UPI ID</label>
                  <input
                    type="text"
                    name="upiId"
                    value={currentPaymentMethod.upiId}
                    onChange={handlePaymentMethodInputChange}
                    className="form-input"
                    placeholder="yourname@upi"
                  />
                  <p className="form-hint">Enter your UPI ID (e.g., yourname@okicici, yourname@ybl)</p>
                </div>
              )}

              {currentPaymentMethod.type === 'bank_transfer' && (
                <>
                  <div className="form-group">
                    <label className="form-label">Account Holder Name</label>
                    <input
                      type="text"
                      name="accountName"
                      value={currentPaymentMethod.accountName || ''}
                      onChange={handlePaymentMethodInputChange}
                      className="form-input"
                      placeholder="Account Holder Name"
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Account Number</label>
                    <input
                      type="text"
                      name="accountNumber"
                      value={currentPaymentMethod.accountNumber || ''}
                      onChange={handlePaymentMethodInputChange}
                      className="form-input"
                      placeholder="Account Number"
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Bank Name</label>
                    <input
                      type="text"
                      name="bankName"
                      value={currentPaymentMethod.bankName || ''}
                      onChange={handlePaymentMethodInputChange}
                      className="form-input"
                      placeholder="Bank Name"
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">IFSC Code</label>
                    <input
                      type="text"
                      name="ifscCode"
                      value={currentPaymentMethod.ifscCode || ''}
                      onChange={handlePaymentMethodInputChange}
                      className="form-input"
                      placeholder="IFSC Code"
                    />
                  </div>
                </>
              )}

              <div className="form-group">
                <label className="checkbox-container">
                  <input
                    type="checkbox"
                    name="isDefault"
                    checked={currentPaymentMethod.isDefault}
                    onChange={handlePaymentMethodInputChange}
                  />
                  <span className="checkbox-label">Set as default payment method</span>
                </label>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-outline" onClick={closePaymentMethodModal} disabled={loading}>Cancel</button>
              <button
                className={`btn btn-primary ${loading ? 'btn-loading' : ''}`}
                onClick={savePaymentMethod}
                disabled={loading}
              >
                {loading ? 'Saving...' : (isEditingPaymentMethod ? 'Update' : 'Add') + ' Payment Method'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
