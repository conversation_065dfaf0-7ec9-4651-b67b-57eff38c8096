<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $this->authorize('viewAny', [Service::class, $store]);

        $query = Service::where('store_id', $store->id);

        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        $services = $query->orderBy('created_at', 'desc')->paginate($request->input('limit', 10));

        return response()->json($services);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found. Please create a store first.'], 404);
        }

        $this->authorize('create', [Service::class, $store]);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'duration' => 'nullable|string|max:255',
            'availability' => 'nullable|json',
            'is_online' => 'boolean',
            'location' => 'nullable|string|max:255',
            'service_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $validator->validated();
        $data['store_id'] = $store->id;

        if ($request->hasFile('service_image')) {
            $path = $request->file('service_image')->store('public/services');
            $data['image_url'] = Storage::url($path);
        }

        $service = Service::create($data);

        return response()->json(['message' => 'Service created successfully', 'service' => $service], 201);
    }

    public function show(Service $service)
    {
        $this->authorize('view', $service);
        return response()->json($service);
    }

    public function update(Request $request, Service $service)
    {
        $this->authorize('update', $service);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'sometimes|required|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'duration' => 'nullable|string|max:255',
            'availability' => 'nullable|json',
            'is_online' => 'boolean',
            'location' => 'nullable|string|max:255',
            'service_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $validator->validated();

        if ($request->hasFile('service_image')) {
            // Delete old image if it exists
            if ($service->image_url) {
                Storage::delete(str_replace('/storage', 'public', $service->image_url));
            }
            $path = $request->file('service_image')->store('public/services');
            $data['image_url'] = Storage::url($path);
        }

        $service->update($data);

        return response()->json(['message' => 'Service updated successfully', 'service' => $service]);
    }

    public function destroy(Service $service)
    {
        $this->authorize('delete', $service);

        // Delete image if it exists
        if ($service->image_url) {
            Storage::delete(str_replace('/storage', 'public', $service->image_url));
        }

        $service->delete();

        return response()->json(['message' => 'Service deleted successfully']);
    }

    // Public method to get services for a specific store
    public function getServicesByStoreId($storeId)
    {
        $services = Service::where('store_id', $storeId)
            ->where('is_active', true)
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json(['services' => $services]);
    }
}
