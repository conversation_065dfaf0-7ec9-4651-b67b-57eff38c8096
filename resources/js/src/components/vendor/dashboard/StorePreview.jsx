import { Link } from 'react-router-dom';
import { ArrowRightIcon, PhoneIcon, QrCodeIcon, ShareIcon } from '@heroicons/react/24/outline';

export default function StorePreview() {
  return (
    <div className="dashboard-section">
      <div className="dashboard-card">
        <div className="p-8">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-8">
            <div>
              <div className="dashboard-badge">Store Status: Active</div>
              <h2 className="dashboard-subheading mb-3">
                Your WhatsApp Store
              </h2>
              <p className="dashboard-text max-w-xl">
                Your WhatsApp-style store is active and ready to receive customers. Share your store link with customers to start selling.
              </p>

              <div className="flex flex-wrap gap-4 mt-6">
                <Link
                  to="/vendor/store"
                  className="dashboard-button dashboard-button-primary"
                >
                  View Store
                </Link>

                <button className="dashboard-button dashboard-button-outline flex items-center gap-2">
                  <ShareIcon className="h-5 w-5" />
                  Share Store
                </button>
              </div>
            </div>

            <div className="bg-whatsapp-bg-secondary p-6 rounded-xl lg:min-width-[280px]">
              <h4 className="font-semibold text-lg mb-4">Quick Access</h4>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-whatsapp-bg-primary flex items-center justify-center">
                    <PhoneIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">WhatsApp Number</p>
                    <p className="text-sm text-gray-500">+****************</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-whatsapp-bg-accent flex items-center justify-center">
                    <QrCodeIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">Store QR Code</p>
                    <button className="text-sm text-whatsapp-text-primary">Generate QR</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
