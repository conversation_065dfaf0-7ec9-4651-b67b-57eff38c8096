/**
 * Reusable Card component for displaying content in a contained box
 * 
 * @param {Object} props - Component props
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.hover - Whether to add hover effects
 * @param {React.ReactNode} props.children - Card content
 */
export default function Card({ className = "", hover = false, children }) {
  const baseClasses = "bg-white rounded-lg shadow-sm overflow-hidden";
  const hoverClasses = hover ? "transition-transform duration-300 hover:shadow-md hover:-translate-y-1" : "";
  
  return (
    <div className={`${baseClasses} ${hoverClasses} ${className}`}>
      {children}
    </div>
  );
}

/**
 * Card.Body component for consistent padding inside cards
 */
Card.Body = function CardBody({ className = "", children }) {
  return (
    <div className={`p-6 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Card.Image component for images at the top of cards
 */
Card.Image = function CardImage({ src, alt, className = "" }) {
  return (
    <div className="w-full">
      <img 
        src={src} 
        alt={alt} 
        className={`w-full object-cover ${className}`}
      />
    </div>
  );
};

/**
 * Card.Title component for card titles
 */
Card.Title = function CardTitle({ children, className = "" }) {
  return (
    <h3 className={`text-lg font-medium text-gray-900 ${className}`}>
      {children}
    </h3>
  );
};

/**
 * Card.Text component for card body text
 */
Card.Text = function CardText({ children, className = "" }) {
  return (
    <p className={`mt-2 text-base text-gray-500 ${className}`}>
      {children}
    </p>
  );
};

/**
 * Card.Footer component for card footers
 */
Card.Footer = function CardFooter({ children, className = "" }) {
  return (
    <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 ${className}`}>
      {children}
    </div>
  );
};
