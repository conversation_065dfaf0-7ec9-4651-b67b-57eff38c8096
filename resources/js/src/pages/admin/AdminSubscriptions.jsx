import { useState, useEffect } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  PlusIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminSubscriptions() {
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [isAddingPlan, setIsAddingPlan] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    interval: 'yearly',
    maxProducts: '',
    maxChatFlows: '',
    features: [],
    isActive: true,
    isPopular: false,
    sortOrder: 0
  });
  const [newFeature, setNewFeature] = useState('');

  // Fetch subscription plans
  useEffect(() => {
    const fetchSubscriptionPlans = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockPlans = [
          {
            id: 1,
            name: 'Standard',
            description: 'Perfect for small businesses just getting started',
            price: 1499.00,
            originalPrice: 1999.00,
            interval: 'yearly',
            maxProducts: 20,
            maxChatFlows: 1,
            features: [
              '20 products',
              '1 chat flow',
              'Basic support',
              'Verified badge'
            ],
            isActive: true,
            isPopular: false,
            sortOrder: 1
          },
          {
            id: 2,
            name: 'Gold',
            description: 'For growing businesses with more advanced needs',
            price: 3000.00,
            originalPrice: 4000.00,
            interval: 'yearly',
            maxProducts: 50,
            maxChatFlows: 5,
            features: [
              '50 products',
              '5 chat flows',
              'Priority support',
              'Analytics dashboard',
              'Verified badge'
            ],
            isActive: true,
            isPopular: true,
            sortOrder: 2
          },
          {
            id: 3,
            name: 'Premium',
            description: 'For established businesses needing advanced features',
            price: 4999.00,
            originalPrice: 6666.00,
            interval: 'yearly',
            maxProducts: 100,
            maxChatFlows: 15,
            features: [
              '100 products',
              '15 chat flows',
              'Priority support',
              'Advanced analytics',
              'Third-party integrations',
              'Verified badge'
            ],
            isActive: true,
            isPopular: false,
            sortOrder: 3
          }
        ];
        
        setSubscriptionPlans(mockPlans);
      } catch (error) {
        console.error('Error fetching subscription plans:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionPlans();
  }, []);

  // Handle edit plan
  const handleEditPlan = (plan) => {
    setEditingPlan(plan.id);
    setFormData({
      name: plan.name,
      description: plan.description,
      price: plan.price,
      originalPrice: plan.originalPrice,
      interval: plan.interval,
      maxProducts: plan.maxProducts,
      maxChatFlows: plan.maxChatFlows,
      features: [...plan.features],
      isActive: plan.isActive,
      isPopular: plan.isPopular,
      sortOrder: plan.sortOrder
    });
  };

  // Handle add new plan
  const handleAddPlan = () => {
    setIsAddingPlan(true);
    setFormData({
      name: '',
      description: '',
      price: '',
      originalPrice: '',
      interval: 'yearly',
      maxProducts: '',
      maxChatFlows: '',
      features: [],
      isActive: true,
      isPopular: false,
      sortOrder: subscriptionPlans.length + 1
    });
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle adding a feature
  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFormData({
        ...formData,
        features: [...formData.features, newFeature.trim()]
      });
      setNewFeature('');
    }
  };

  // Handle removing a feature
  const handleRemoveFeature = (index) => {
    const updatedFeatures = [...formData.features];
    updatedFeatures.splice(index, 1);
    setFormData({
      ...formData,
      features: updatedFeatures
    });
  };

  // Handle save plan
  const handleSavePlan = () => {
    // In a real implementation, this would be an API call
    if (isAddingPlan) {
      // Add new plan
      const newPlan = {
        id: Date.now(), // Temporary ID for mock data
        ...formData
      };
      setSubscriptionPlans([...subscriptionPlans, newPlan]);
    } else {
      // Update existing plan
      const updatedPlans = subscriptionPlans.map(plan => 
        plan.id === editingPlan ? { ...plan, ...formData } : plan
      );
      setSubscriptionPlans(updatedPlans);
    }
    
    // Reset form
    setEditingPlan(null);
    setIsAddingPlan(false);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingPlan(null);
    setIsAddingPlan(false);
  };

  // Handle delete plan
  const handleDeletePlan = (planId) => {
    if (window.confirm('Are you sure you want to delete this subscription plan?')) {
      // In a real implementation, this would be an API call
      const updatedPlans = subscriptionPlans.filter(plan => plan.id !== planId);
      setSubscriptionPlans(updatedPlans);
    }
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Subscription Management</div>
          <h1 className="welcome-title">
            Manage <span style={{ color: '#25D366' }}>Subscription Plans</span>
          </h1>
          <p className="welcome-subtitle">
            Create and manage subscription plans for vendors. Set pricing, features, and limits for each plan.
          </p>
        </div>
      </div>

      {/* Subscription Plans Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Subscription Plans</h2>
          <button 
            className="btn btn-primary"
            onClick={handleAddPlan}
            disabled={isAddingPlan || editingPlan !== null}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Add New Plan
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading subscription plans...</p>
          </div>
        ) : (
          <div className="card" style={{ padding: '0' }}>
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Price</th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Products</th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Chat Flows</th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {subscriptionPlans.map((plan) => (
                  <tr key={plan.id} className="border-b hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div>
                          <div className="font-medium text-gray-900">{plan.name}</div>
                          <div className="text-sm text-gray-500">{plan.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">₹{plan.price}</div>
                      <div className="text-xs text-gray-500">Original: ₹{plan.originalPrice}</div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">{plan.maxProducts}</td>
                    <td className="px-6 py-4 text-sm text-gray-500">{plan.maxChatFlows}</td>
                    <td className="px-6 py-4">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${plan.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                        {plan.isActive ? 'Active' : 'Inactive'}
                      </span>
                      {plan.isPopular && (
                        <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Popular
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <button
                        onClick={() => handleEditPlan(plan)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                        disabled={editingPlan !== null || isAddingPlan}
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeletePlan(plan.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={editingPlan !== null || isAddingPlan}
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Edit/Add Plan Form */}
      {(editingPlan !== null || isAddingPlan) && (
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">{isAddingPlan ? 'Add New Plan' : 'Edit Plan'}</h2>
          </div>
          <div className="card" style={{ padding: '24px' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. Standard, Premium"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price (₹)
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. 1499"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Original Price (₹)
                </label>
                <input
                  type="number"
                  name="originalPrice"
                  value={formData.originalPrice}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. 1999"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Billing Interval
                </label>
                <select
                  name="interval"
                  value={formData.interval}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Products
                </label>
                <input
                  type="number"
                  name="maxProducts"
                  value={formData.maxProducts}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. 20"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Chat Flows
                </label>
                <input
                  type="number"
                  name="maxChatFlows"
                  value={formData.maxChatFlows}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. 5"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="2"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Brief description of the plan"
                ></textarea>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Features
                </label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="e.g. 20 products"
                  />
                  <button
                    type="button"
                    onClick={handleAddFeature}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Add
                  </button>
                </div>
                <div className="space-y-2">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span>{feature}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveFeature(index)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                  Active
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPopular"
                  name="isPopular"
                  checked={formData.isPopular}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="isPopular" className="ml-2 block text-sm text-gray-900">
                  Mark as Popular
                </label>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSavePlan}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {isAddingPlan ? 'Add Plan' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
