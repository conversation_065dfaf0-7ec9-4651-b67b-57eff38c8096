import { useState, useEffect } from 'react';
import { 
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  PhoneIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminSupport() {
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch support tickets
  useEffect(() => {
    const fetchTickets = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockTickets = [
          {
            id: 1,
            subject: 'Payment issue with subscription',
            message: 'I tried to upgrade my subscription but the payment failed. Please help.',
            status: 'open',
            priority: 'high',
            createdAt: '2023-10-20T10:30:00',
            updatedAt: '2023-10-20T10:30:00',
            user: {
              id: 2,
              name: 'Rahul Sharma',
              email: '<EMAIL>',
              userType: 'vendor'
            },
            replies: []
          },
          {
            id: 2,
            subject: 'How to add products?',
            message: 'I\'m new to the platform and I\'m not sure how to add products to my store. Can you provide some guidance?',
            status: 'open',
            priority: 'medium',
            createdAt: '2023-10-19T14:45:00',
            updatedAt: '2023-10-19T14:45:00',
            user: {
              id: 3,
              name: 'Priya Patel',
              email: '<EMAIL>',
              userType: 'vendor'
            },
            replies: []
          },
          {
            id: 3,
            subject: 'Chat flow not working',
            message: 'I created a chat flow but it\'s not showing up in my store. I\'ve tried refreshing but it still doesn\'t work.',
            status: 'in_progress',
            priority: 'high',
            createdAt: '2023-10-18T09:15:00',
            updatedAt: '2023-10-18T11:30:00',
            user: {
              id: 4,
              name: 'Amit Kumar',
              email: '<EMAIL>',
              userType: 'vendor'
            },
            replies: [
              {
                id: 1,
                message: 'Can you please provide more details about the chat flow you created? What type of messages did you include?',
                createdAt: '2023-10-18T11:30:00',
                user: {
                  id: 1,
                  name: 'Admin User',
                  email: '<EMAIL>',
                  userType: 'admin'
                }
              }
            ]
          },
          {
            id: 4,
            subject: 'Request for refund',
            message: 'I accidentally purchased the wrong subscription plan. Can I get a refund and switch to the correct plan?',
            status: 'closed',
            priority: 'medium',
            createdAt: '2023-10-15T16:20:00',
            updatedAt: '2023-10-17T14:10:00',
            user: {
              id: 5,
              name: 'Neha Singh',
              email: '<EMAIL>',
              userType: 'vendor'
            },
            replies: [
              {
                id: 2,
                message: 'We\'ve processed your refund and you should see the amount back in your account within 3-5 business days. You can now purchase the correct subscription plan.',
                createdAt: '2023-10-17T14:10:00',
                user: {
                  id: 1,
                  name: 'Admin User',
                  email: '<EMAIL>',
                  userType: 'admin'
                }
              }
            ]
          },
          {
            id: 5,
            subject: 'Need help with store verification',
            message: 'I submitted my store for verification but it\'s been pending for over a week. Can you please check the status?',
            status: 'open',
            priority: 'low',
            createdAt: '2023-10-21T08:45:00',
            updatedAt: '2023-10-21T08:45:00',
            user: {
              id: 6,
              name: 'Vikram Joshi',
              email: '<EMAIL>',
              userType: 'vendor'
            },
            replies: []
          }
        ];
        
        // Filter tickets based on search term and status
        let filteredTickets = mockTickets;
        
        if (searchTerm) {
          filteredTickets = filteredTickets.filter(ticket => 
            ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
            ticket.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
            ticket.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            ticket.user.email.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (filterStatus !== 'all') {
          filteredTickets = filteredTickets.filter(ticket => ticket.status === filterStatus);
        }
        
        setTickets(filteredTickets);
        setTotalPages(Math.ceil(filteredTickets.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching tickets:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTickets();
  }, [searchTerm, filterStatus]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle ticket selection
  const handleSelectTicket = (ticket) => {
    setSelectedTicket(ticket);
    setReplyText('');
  };

  // Handle reply text change
  const handleReplyChange = (e) => {
    setReplyText(e.target.value);
  };

  // Handle send reply
  const handleSendReply = () => {
    if (!replyText.trim()) return;
    
    // In a real implementation, this would be an API call
    const newReply = {
      id: Date.now(),
      message: replyText,
      createdAt: new Date().toISOString(),
      user: {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        userType: 'admin'
      }
    };
    
    const updatedTicket = {
      ...selectedTicket,
      replies: [...selectedTicket.replies, newReply],
      updatedAt: new Date().toISOString()
    };
    
    const updatedTickets = tickets.map(ticket => 
      ticket.id === selectedTicket.id ? updatedTicket : ticket
    );
    
    setTickets(updatedTickets);
    setSelectedTicket(updatedTicket);
    setReplyText('');
  };

  // Handle change ticket status
  const handleChangeStatus = (status) => {
    if (!selectedTicket) return;
    
    const updatedTicket = {
      ...selectedTicket,
      status,
      updatedAt: new Date().toISOString()
    };
    
    const updatedTickets = tickets.map(ticket => 
      ticket.id === selectedTicket.id ? updatedTicket : ticket
    );
    
    setTickets(updatedTickets);
    setSelectedTicket(updatedTicket);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-orange-100 text-orange-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get paginated tickets
  const paginatedTickets = tickets.slice((currentPage - 1) * 10, currentPage * 10);

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Customer Support</div>
          <h1 className="welcome-title">
            Help & <span style={{ color: '#25D366' }}>Support</span>
          </h1>
          <p className="welcome-subtitle">
            Manage support tickets, respond to customer inquiries, and provide assistance to vendors.
          </p>
        </div>
      </div>

      {/* Support Tickets Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Support Tickets</h2>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <div className="relative">
              <select
                value={filterStatus}
                onChange={(e) => handleFilterChange(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 appearance-none"
              >
                <option value="all">All Tickets</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="closed">Closed</option>
              </select>
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Tickets List */}
          <div className="w-full md:w-2/5">
            {loading ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>Loading tickets...</p>
              </div>
            ) : (
              <div className="card" style={{ padding: '0', height: '100%' }}>
                {tickets.length === 0 ? (
                  <div style={{ padding: '24px', textAlign: 'center' }}>
                    <p>No tickets found matching your criteria.</p>
                  </div>
                ) : (
                  <>
                    <div className="overflow-y-auto" style={{ maxHeight: '600px' }}>
                      <ul className="divide-y divide-gray-200">
                        {paginatedTickets.map(ticket => (
                          <li 
                            key={ticket.id}
                            className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedTicket?.id === ticket.id ? 'bg-gray-50' : ''}`}
                            onClick={() => handleSelectTicket(ticket)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium text-gray-900">{ticket.subject}</p>
                                <p className="text-xs text-gray-500 mt-1">{ticket.user.name} ({ticket.user.email})</p>
                              </div>
                              <div className="flex flex-col items-end">
                                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(ticket.status)}`}>
                                  {ticket.status === 'in_progress' ? 'In Progress' : ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                                </span>
                                <span className="text-xs text-gray-500 mt-1">{formatDate(ticket.createdAt)}</span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-500 mt-2 line-clamp-2">{ticket.message}</p>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className="px-4 py-3 flex items-center justify-between border-t">
                        <div>
                          <p className="text-sm text-gray-700">
                            Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                            <span className="font-medium">{Math.min(currentPage * 10, tickets.length)}</span> of{' '}
                            <span className="font-medium">{tickets.length}</span> tickets
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                          >
                            <ChevronLeftIcon className="h-5 w-5" />
                          </button>
                          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                              key={page}
                              onClick={() => handlePageChange(page)}
                              className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                            >
                              {page}
                            </button>
                          ))}
                          <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                          >
                            <ChevronRightIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>

          {/* Ticket Details */}
          <div className="w-full md:w-3/5">
            {selectedTicket ? (
              <div className="card" style={{ padding: '0', height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Ticket Header */}
                <div className="p-4 border-b">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{selectedTicket.subject}</h3>
                      <div className="flex items-center mt-1">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(selectedTicket.status)} mr-2`}>
                          {selectedTicket.status === 'in_progress' ? 'In Progress' : selectedTicket.status.charAt(0).toUpperCase() + selectedTicket.status.slice(1)}
                        </span>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getPriorityBadgeColor(selectedTicket.priority)}`}>
                          {selectedTicket.priority.charAt(0).toUpperCase() + selectedTicket.priority.slice(1)} Priority
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {selectedTicket.status !== 'closed' && (
                        <button
                          onClick={() => handleChangeStatus('closed')}
                          className="px-3 py-1 text-xs font-medium rounded-md bg-green-100 text-green-800 hover:bg-green-200"
                        >
                          <CheckCircleIcon className="h-4 w-4 inline mr-1" />
                          Mark as Closed
                        </button>
                      )}
                      {selectedTicket.status === 'open' && (
                        <button
                          onClick={() => handleChangeStatus('in_progress')}
                          className="px-3 py-1 text-xs font-medium rounded-md bg-blue-100 text-blue-800 hover:bg-blue-200"
                        >
                          <ClockIcon className="h-4 w-4 inline mr-1" />
                          Mark as In Progress
                        </button>
                      )}
                      {selectedTicket.status === 'closed' && (
                        <button
                          onClick={() => handleChangeStatus('open')}
                          className="px-3 py-1 text-xs font-medium rounded-md bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
                        >
                          <ExclamationCircleIcon className="h-4 w-4 inline mr-1" />
                          Reopen
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      <span className="font-medium">From:</span> {selectedTicket.user.name} ({selectedTicket.user.email})
                    </p>
                    <p className="text-sm text-gray-500">
                      <span className="font-medium">Created:</span> {formatDate(selectedTicket.createdAt)}
                    </p>
                    <p className="text-sm text-gray-500">
                      <span className="font-medium">Last Updated:</span> {formatDate(selectedTicket.updatedAt)}
                    </p>
                  </div>
                </div>

                {/* Ticket Messages */}
                <div className="p-4 flex-1 overflow-y-auto" style={{ maxHeight: '400px' }}>
                  {/* Original Message */}
                  <div className="mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-900">{selectedTicket.message}</p>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <p className="text-xs text-gray-500">
                        {selectedTicket.user.name} ({selectedTicket.user.userType})
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(selectedTicket.createdAt)}</p>
                    </div>
                  </div>

                  {/* Replies */}
                  {selectedTicket.replies.map(reply => (
                    <div key={reply.id} className="mb-6">
                      <div className={`p-4 rounded-lg ${reply.user.userType === 'admin' ? 'bg-green-50' : 'bg-gray-50'}`}>
                        <p className="text-sm text-gray-900">{reply.message}</p>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-xs text-gray-500">
                          {reply.user.name} ({reply.user.userType})
                        </p>
                        <p className="text-xs text-gray-500">{formatDate(reply.createdAt)}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Reply Form */}
                {selectedTicket.status !== 'closed' && (
                  <div className="p-4 border-t">
                    <div className="flex items-start space-x-3">
                      <div className="flex-1">
                        <textarea
                          value={replyText}
                          onChange={handleReplyChange}
                          placeholder="Type your reply here..."
                          rows="3"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        ></textarea>
                      </div>
                      <button
                        onClick={handleSendReply}
                        disabled={!replyText.trim()}
                        className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${!replyText.trim() ? 'bg-gray-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
                      >
                        <PaperAirplaneIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="card flex flex-col items-center justify-center" style={{ padding: '48px 24px', height: '100%' }}>
                <ChatBubbleLeftRightIcon className="h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Ticket Selected</h3>
                <p className="text-sm text-gray-500 text-center">
                  Select a ticket from the list to view details and respond to customer inquiries.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Support Contact Information</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-4">
              <EnvelopeIcon className="h-8 w-8 text-green-500 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">Email Support</h3>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              For general inquiries and non-urgent issues, please contact us via email.
            </p>
            <p className="text-sm font-medium"><EMAIL></p>
            <p className="text-xs text-gray-500 mt-1">Response time: 24-48 hours</p>
          </div>
          
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-4">
              <PhoneIcon className="h-8 w-8 text-green-500 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">Phone Support</h3>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              For urgent issues that require immediate assistance, please call our support line.
            </p>
            <p className="text-sm font-medium">+91 9876543210</p>
            <p className="text-xs text-gray-500 mt-1">Available: Mon-Fri, 9 AM - 6 PM IST</p>
          </div>
          
          <div className="card" style={{ padding: '24px' }}>
            <div className="flex items-center mb-4">
              <QuestionMarkCircleIcon className="h-8 w-8 text-green-500 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">Help Center</h3>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              Browse our knowledge base for answers to frequently asked questions and tutorials.
            </p>
            <button className="btn btn-primary w-full">
              Visit Help Center
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
