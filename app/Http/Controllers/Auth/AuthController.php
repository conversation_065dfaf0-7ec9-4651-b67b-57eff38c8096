<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'userType' => ['required', 'string', 'in:vendor,customer'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'userType' => $request->userType,
            'isActive' => true, // Or based on your logic
        ]);

        // If the user is a vendor, create a store for them
        if ($user->userType === 'vendor') {
            Store::create([
                'userId' => $user->id,
                'name' => $user->name . "'s Store",
                                'storeUrl' => Str::slug($user->name . '-store-' . uniqid()),
                // Add other default store fields as necessary
            ]);
        }

        Auth::login($user);

        return response()->json([
            'message' => 'User registered and logged in successfully.',
            'user' => $user
        ], 201);
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        Log::info('Login attempt:', $request->only('email', 'password'));
        $user = \App\Models\User::where('email', $request->email)->first();

        if ($user) {
            Log::info('User found:', ['email' => $user->email, 'hashed_password_preview' => substr($user->password, 0, 10).'...' ]); // Log only a preview of the hash
            Log::info('Password check result: ' . (Hash::check($request->password, $user->password) ? 'Match' : 'No Match'));
        } else {
            Log::info('User not found with email: ' . $request->email);
        }

        if (Auth::attempt($request->only('email', 'password'))) {
            $request->session()->regenerate();

            return response()->json([
                'message' => 'Logged in successfully.',
                'user' => Auth::user()
            ]);
        }

        return response()->json([
            'message' => 'The provided credentials do not match our records.',
        ], 401);
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return response()->json(['message' => 'Logged out successfully.']);
    }

    public function me(Request $request)
    {
        return response()->json(Auth::user());
    }
}
