import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { orderAPI } from '../../../services/api';

export default function RecentOrders() {
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Format currency
  const formatCurrency = (amount) => {
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  // Format relative time
  const formatRelativeTime = (dateString) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffSec = Math.floor(diffMs / 1000);
      const diffMin = Math.floor(diffSec / 60);
      const diffHour = Math.floor(diffMin / 60);
      const diffDay = Math.floor(diffHour / 24);

      if (diffDay > 0) {
        return diffDay === 1 ? '1 day ago' : `${diffDay} days ago`;
      } else if (diffHour > 0) {
        return diffHour === 1 ? '1 hour ago' : `${diffHour} hours ago`;
      } else if (diffMin > 0) {
        return diffMin === 1 ? '1 minute ago' : `${diffMin} minutes ago`;
      } else {
        return 'just now';
      }
    } catch (error) {
      return dateString;
    }
  };

  // Capitalize first letter
  const capitalizeFirstLetter = (string) => {
    if (!string) return '';
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoading(true);
        const response = await orderAPI.getAllOrders();
        // Get the 5 most recent orders
        const sortedOrders = response.data.orders
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5);
        setRecentOrders(sortedOrders);
        setError(null);
      } catch (err) {
//         // Removed console.error
        setError('Failed to load recent orders');
      } finally {
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, []);

  return (
    <div className="dashboard-section">
      <div className="dashboard-section-header">
        <h2 className="dashboard-subheading">Recent Orders</h2>
        <Link
          to="/vendor/orders"
          className="dashboard-button dashboard-button-outline flex items-center gap-2 text-sm"
        >
          View all orders
          <ArrowRightIcon className="h-4 w-4" />
        </Link>
      </div>
      <div className="orders-list">
        {loading ? (
          <div style={{ padding: '1rem', textAlign: 'center' }}>
            <p>Loading recent orders...</p>
          </div>
        ) : error ? (
          <div style={{ padding: '1rem', textAlign: 'center', color: 'red' }}>
            <p>{error}</p>
          </div>
        ) : recentOrders.length === 0 ? (
          <div style={{ padding: '1rem', textAlign: 'center' }}>
            <p>No orders found. Create your first order!</p>
          </div>
        ) : (
          <ul>
            {recentOrders.map((order) => (
              <li key={order.id} className="order-item">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-base font-semibold text-gray-900">
                      Order #{order.orderNumber || order.id}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {order.customerName}
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="text-base font-semibold text-gray-900 text-right">
                        {formatCurrency(order.totalAmount)}
                      </p>
                      <p className="text-sm text-gray-500 mt-1 text-right">
                        {order.items?.length || 0} {order.items?.length === 1 ? 'item' : 'items'}
                      </p>
                    </div>
                    <div>
                      <span className={`status-badge ${
                        order.status === 'completed' ? 'status-completed' :
                        order.status === 'processing' ? 'status-processing' :
                        order.status === 'shipped' ? 'status-completed' :
                        'status-pending'
                      }`}>
                        {capitalizeFirstLetter(order.status)}
                      </span>
                      <p className="text-xs text-gray-500 mt-1 text-right">
                        {formatRelativeTime(order.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
