import { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  BuildingStorefrontIcon,
  UsersIcon,
  ChartBarIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorVendorAnalytics() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month');
  const [vendorData, setVendorData] = useState({
    vendorMetrics: {
      totalVendors: 0,
      activeVendors: 0,
      inactiveVendors: 0,
      newVendorsThisMonth: 0,
      vendorGrowthRate: 0,
      vendorRetentionRate: 0,
      averageRevenuePerVendor: 0,
      vendorsByPlan: []
    },
    vendorGrowth: {
      monthly: [],
      projectedGrowth: []
    },
    vendorList: [],
    vendorChurn: {
      rate: 0,
      reasons: []
    },
    geographicDistribution: []
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlan, setFilterPlan] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch vendor analytics data
  useEffect(() => {
    const fetchVendorData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          vendorMetrics: {
            totalVendors: 128,
            activeVendors: 115,
            inactiveVendors: 13,
            newVendorsThisMonth: 15,
            vendorGrowthRate: 13.2,
            vendorRetentionRate: 92,
            averageRevenuePerVendor: 976,
            vendorsByPlan: [
              { plan: 'Standard', count: 78, percentage: 60.9 },
              { plan: 'Gold', count: 35, percentage: 27.3 },
              { plan: 'Premium', count: 15, percentage: 11.8 }
            ]
          },
          vendorGrowth: {
            monthly: [
              { month: 'Jan', value: 85 },
              { month: 'Feb', value: 92 },
              { month: 'Mar', value: 98 },
              { month: 'Apr', value: 105 },
              { month: 'May', value: 112 },
              { month: 'Jun', value: 118 },
              { month: 'Jul', value: 128 }
            ],
            projectedGrowth: [
              { month: 'Aug', value: 138 },
              { month: 'Sep', value: 149 },
              { month: 'Oct', value: 161 },
              { month: 'Nov', value: 174 },
              { month: 'Dec', value: 188 }
            ]
          },
          vendorList: Array.from({ length: 50 }, (_, i) => ({
            id: i + 1,
            name: `Vendor ${i + 1}`,
            plan: i % 5 === 0 ? 'Premium' : i % 3 === 0 ? 'Gold' : 'Standard',
            status: i % 10 === 0 ? 'inactive' : 'active',
            joinDate: new Date(2023, 0, i + 1).toISOString().split('T')[0],
            revenue: Math.floor(500 + Math.random() * 2000),
            products: Math.floor(5 + Math.random() * 45),
            location: i % 3 === 0 ? 'Mumbai' : i % 4 === 0 ? 'Delhi' : i % 5 === 0 ? 'Bangalore' : 'Other'
          })),
          vendorChurn: {
            rate: 8,
            reasons: [
              { reason: 'Pricing', percentage: 35 },
              { reason: 'Competition', percentage: 25 },
              { reason: 'Poor Sales', percentage: 20 },
              { reason: 'Technical Issues', percentage: 15 },
              { reason: 'Other', percentage: 5 }
            ]
          },
          geographicDistribution: [
            { city: 'Mumbai', vendors: 32 },
            { city: 'Delhi', vendors: 28 },
            { city: 'Bangalore', vendors: 22 },
            { city: 'Hyderabad', vendors: 15 },
            { city: 'Chennai', vendors: 12 },
            { city: 'Kolkata', vendors: 10 },
            { city: 'Pune', vendors: 9 },
            { city: 'Other', vendors: 0 }
          ]
        };

        setVendorData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching vendor data:', error);
        setLoading(false);
      }
    };

    fetchVendorData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  // Filter vendors based on search term, plan, and status
  const filteredVendors = vendorData?.vendorList.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlan = filterPlan === 'all' || vendor.plan === filterPlan;
    const matchesStatus = filterStatus === 'all' || vendor.status === filterStatus;

    return matchesSearch && matchesPlan && matchesStatus;
  }) || [];

  // Pagination
  const indexOfLastVendor = currentPage * itemsPerPage;
  const indexOfFirstVendor = indexOfLastVendor - itemsPerPage;
  const currentVendors = filteredVendors.slice(indexOfFirstVendor, indexOfLastVendor);
  const totalPages = Math.ceil(filteredVendors.length / itemsPerPage);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Get vendor metrics
  const getVendorMetrics = () => {
    if (!vendorData) return [];

    return [
      {
        name: 'Total Vendors',
        value: vendorData.vendorMetrics.totalVendors,
        change: vendorData.vendorMetrics.vendorGrowthRate,
        trend: 'up',
        icon: BuildingStorefrontIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Active Vendors',
        value: vendorData.vendorMetrics.activeVendors,
        change: vendorData.vendorMetrics.vendorRetentionRate,
        trend: 'up',
        icon: UsersIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'New This Month',
        value: vendorData.vendorMetrics.newVendorsThisMonth,
        change: vendorData.vendorMetrics.vendorGrowthRate,
        trend: 'up',
        icon: UsersIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Avg. Revenue/Vendor',
        value: formatCurrency(vendorData.vendorMetrics.averageRevenuePerVendor),
        change: 8.5,
        trend: 'up',
        icon: ChartBarIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Vendor Analytics</div>
          <h1 className="welcome-title">
            Vendor <span style={{ color: '#25D366' }}>Metrics</span>
          </h1>
          <p className="welcome-subtitle">
            Comprehensive analytics on vendor growth, distribution, and performance.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Time Range and Export Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                timeRange === 'month'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('month')}
            >
              Month
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium ${
                timeRange === 'quarter'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('quarter')}
            >
              Quarter
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                timeRange === 'year'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('year')}
            >
              Year
            </button>
          </div>

          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Report
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading vendor analytics...</p>
          </div>
        ) : (
          <>
            {/* Vendor Metrics */}
            <div className="stats-grid">
              {vendorData && getVendorMetrics().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-sm ${item.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                        {item.change > 0 ? '+' : ''}{item.change}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Vendor Growth Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Vendor Growth Trend</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div style={{ height: '300px', position: 'relative' }}>
                  {/* This would be a real chart in production */}
                  <div className="bg-gray-100 h-full w-full rounded-lg flex items-center justify-center">
                    <div className="flex items-end h-64 w-full px-8">
                      {vendorData.vendorGrowth.monthly.concat(vendorData.vendorGrowth.projectedGrowth).map((item, index) => {
                        const isProjected = index >= vendorData.vendorGrowth.monthly.length;
                        const maxValue = Math.max(
                          ...vendorData.vendorGrowth.monthly.concat(vendorData.vendorGrowth.projectedGrowth).map(i => i.value)
                        );
                        const height = (item.value / maxValue) * 240;

                        return (
                          <div key={index} className="flex-1 flex flex-col items-center">
                            <div
                              className={`w-12 rounded-t-md ${isProjected ? 'border border-dashed border-green-500' : ''}`}
                              style={{
                                height: `${height}px`,
                                backgroundColor: isProjected ? 'rgba(37, 211, 102, 0.2)' : '#25D366'
                              }}
                            ></div>
                            <div className="text-xs text-gray-500 mt-2">{item.month}</div>
                            <div className="text-xs font-medium text-gray-900">{item.value}</div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
                <div className="flex justify-center mt-4">
                  <div className="flex items-center mr-4">
                    <div className="w-4 h-4 bg-green-500 mr-2"></div>
                    <span className="text-sm text-gray-600">Actual</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-green-200 border border-dashed border-green-500 mr-2"></div>
                    <span className="text-sm text-gray-600">Projected</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Vendor Distribution */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Vendor Distribution</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">By Subscription Plan</h3>
                  <div className="space-y-4">
                    {vendorData.vendorMetrics.vendorsByPlan.map((plan) => (
                      <div key={plan.plan} className="flex items-center">
                        <div className="w-32 text-sm font-medium text-gray-900">{plan.plan}</div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full"
                              style={{
                                width: `${plan.percentage}%`,
                                backgroundColor: plan.plan === 'Standard' ? '#25D366' : plan.plan === 'Gold' ? '#FFD700' : '#075E54'
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-16 text-right text-sm font-medium text-gray-900">{plan.count}</div>
                        <div className="w-16 text-right text-xs text-gray-500">
                          {formatPercentage(plan.percentage)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">By Geographic Location</h3>
                  <div className="space-y-4">
                    {vendorData.geographicDistribution.map((location) => (
                      <div key={location.city} className="flex items-center">
                        <div className="w-32 text-sm font-medium text-gray-900">{location.city}</div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full bg-accent"
                              style={{
                                width: `${(location.vendors / vendorData.vendorMetrics.totalVendors) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-16 text-right text-sm font-medium text-gray-900">{location.vendors}</div>
                        <div className="w-16 text-right text-xs text-gray-500">
                          {formatPercentage(Math.round((location.vendors / vendorData.vendorMetrics.totalVendors) * 100))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Vendor List */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Vendor List</h2>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="Search vendors..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>

                <div className="flex gap-4">
                  <select
                    value={filterPlan}
                    onChange={(e) => {
                      setFilterPlan(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="all">All Plans</option>
                    <option value="Standard">Standard</option>
                    <option value="Gold">Gold</option>
                    <option value="Premium">Premium</option>
                  </select>

                  <select
                    value={filterStatus}
                    onChange={(e) => {
                      setFilterStatus(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>

              {/* Vendor Table */}
              <div className="card overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vendor
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Plan
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Join Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Revenue
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Products
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Location
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {currentVendors.map((vendor) => (
                        <tr key={vendor.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {vendor.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              vendor.plan === 'Premium'
                                ? 'bg-purple-100 text-purple-800'
                                : vendor.plan === 'Gold'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                            }`}>
                              {vendor.plan}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              vendor.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {vendor.status.charAt(0).toUpperCase() + vendor.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(vendor.joinDate)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(vendor.revenue)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vendor.products}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vendor.location}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{indexOfFirstVendor + 1}</span> to{' '}
                        <span className="font-medium">
                          {indexOfLastVendor > filteredVendors.length ? filteredVendors.length : indexOfLastVendor}
                        </span>{' '}
                        of <span className="font-medium">{filteredVendors.length}</span> vendors
                      </p>
                    </div>
                    <div>
                      <nav className="flex items-center">
                        <button
                          onClick={() => paginate(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === 1
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          Previous
                        </button>
                        {Array.from({ length: totalPages }, (_, i) => (
                          <button
                            key={i + 1}
                            onClick={() => paginate(i + 1)}
                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                              currentPage === i + 1
                                ? 'text-green-600 bg-green-50'
                                : 'text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {i + 1}
                          </button>
                        ))}
                        <button
                          onClick={() => paginate(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === totalPages
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          Next
                        </button>
                      </nav>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
