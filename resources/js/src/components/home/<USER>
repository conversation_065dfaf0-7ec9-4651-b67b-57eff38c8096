import { 
  ChatBubbleLeftRightIcon, 
  ShoppingBagIcon, 
  UsersIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Card from '../ui/Card';

export default function Features() {
  const features = [
    {
      name: 'WhatsApp-Style Chat Interface',
      description: 'Create automated chat flows that guide customers through your products and services with a familiar WhatsApp-like experience.',
      icon: ChatBubbleLeftRightIcon,
    },
    {
      name: 'Easy Store Management',
      description: 'Manage your products, orders, and customers all in one place with our intuitive dashboard designed for small businesses.',
      icon: ShoppingBagIcon,
    },
    {
      name: 'Influencer Collaboration',
      description: 'Connect with influencers to promote your products and reach a wider audience through our built-in influencer marketplace.',
      icon: UsersIcon,
    },
    {
      name: 'Secure Payment Processing',
      description: 'Accept payments directly through WhatsApp with our secure payment gateway integration, supporting multiple payment methods.',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Mobile-First Design',
      description: 'Optimized for mobile devices to provide the best shopping experience for your customers wherever they are.',
      icon: DevicePhoneMobileIcon,
    },
    {
      name: 'Analytics & Insights',
      description: 'Track your store performance with detailed analytics and get insights to optimize your sales and marketing strategies.',
      icon: ChartBarIcon,
    },
  ];

  return (
    <div id="features" className="py-24 bg-gray-50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-base font-semibold leading-7 text-whatsapp-default">Features</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Everything you need to succeed
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Our platform provides all the tools you need to create a successful WhatsApp-style store.
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature) => (
            <Card key={feature.name} hover className="h-full">
              <Card.Body>
                <div className="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-whatsapp-default bg-opacity-10">
                  <feature.icon className="h-6 w-6 text-whatsapp-default" aria-hidden="true" />
                </div>
                <Card.Title>{feature.name}</Card.Title>
                <Card.Text>{feature.description}</Card.Text>
              </Card.Body>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
