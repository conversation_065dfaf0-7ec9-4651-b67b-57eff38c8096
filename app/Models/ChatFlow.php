<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChatFlow extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'name',
        'description',
        'flow_data',
        'is_default',
        'is_active',
        'nodes_count',
        'last_modified',
    ];

    protected $casts = [
        'flow_data' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'last_modified' => 'datetime',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
