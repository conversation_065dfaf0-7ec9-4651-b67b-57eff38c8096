import React from 'react';

/**
 * DateSeparator component
 * Displays a date separator between messages (Today, Yesterday, Friday, etc.)
 * Styled to match WhatsApp's date separator with a light background and rounded corners
 */
const DateSeparator = ({ date }) => {
  const formatDate = (date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      // Return day name for dates within the last week
      const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24));
      if (daysDiff < 7) {
        return date.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
        });
      }
    }
  };

  return (
    <div className="date-separator">
      <span>{formatDate(date)}</span>
    </div>
  );
};

export default DateSeparator;
