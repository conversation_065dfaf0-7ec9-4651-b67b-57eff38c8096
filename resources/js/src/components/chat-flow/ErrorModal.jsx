import React from 'react';
import { XMarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

/**
 * ErrorModal component for the Chat Flow Builder
 * Displays error messages in a modal dialog
 */
const ErrorModal = ({ isOpen, message, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" 
        onClick={onClose}
      ></div>
      
      {/* Modal */}
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden relative z-10 transform transition-all">
        {/* Header */}
        <div className="bg-red-50 px-6 py-4 flex items-center border-b border-red-100">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-500 mr-3" />
          <h2 className="text-lg font-medium text-red-700">Connection Error</h2>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4">
          <p className="text-gray-700">{message}</p>
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-6 py-3 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm font-medium transition-colors"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorModal;
