import { useState, useEffect } from 'react';
import { 
  ChartBarIcon,
  ArrowPathIcon,
  LinkIcon,
  UsersIcon,
  ShoppingBagIcon,
  CurrencyRupeeIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerPerformance() {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [performanceData, setPerformanceData] = useState(null);
  const [referrals, setReferrals] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Fetch performance data
  useEffect(() => {
    const fetchPerformanceData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          overview: {
            totalReferrals: {
              value: 128,
              change: 15.2,
              trend: 'up'
            },
            totalSignups: {
              value: 42,
              change: 8.3,
              trend: 'up'
            },
            totalSales: {
              value: 18,
              change: 12.5,
              trend: 'up'
            },
            totalEarnings: {
              value: 12450,
              change: 10.2,
              trend: 'up'
            }
          },
          conversionRates: {
            visitToSignup: 32.8,
            signupToPurchase: 42.9,
            visitToPurchase: 14.1
          },
          performanceByMonth: [
            { month: 'Jan', referrals: 8, signups: 3, sales: 1 },
            { month: 'Feb', referrals: 12, signups: 5, sales: 2 },
            { month: 'Mar', referrals: 15, signups: 6, sales: 3 },
            { month: 'Apr', referrals: 10, signups: 4, sales: 1 },
            { month: 'May', referrals: 14, signups: 5, sales: 2 },
            { month: 'Jun', referrals: 18, signups: 7, sales: 3 },
            { month: 'Jul', referrals: 22, signups: 8, sales: 4 },
            { month: 'Aug', referrals: 25, signups: 10, sales: 5 },
            { month: 'Sep', referrals: 28, signups: 12, sales: 6 },
            { month: 'Oct', referrals: 32, signups: 15, sales: 8 },
            { month: 'Nov', referrals: 0, signups: 0, sales: 0 },
            { month: 'Dec', referrals: 0, signups: 0, sales: 0 }
          ],
          referralsByPlatform: [
            { platform: 'Instagram', count: 65 },
            { platform: 'Facebook', count: 32 },
            { platform: 'WhatsApp', count: 18 },
            { platform: 'YouTube', count: 8 },
            { platform: 'Twitter', count: 5 }
          ]
        };
        
        const mockReferrals = [
          { id: 1, name: 'Rahul Sharma', date: '2023-10-25', status: 'signed_up', commission: 0, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 2, name: 'Priya Patel', date: '2023-10-24', status: 'purchased', commission: 1500, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
          { id: 3, name: 'Amit Kumar', date: '2023-10-23', status: 'visited', commission: 0, platform: 'WhatsApp', link: 'whamart.com/ref/rahul123' },
          { id: 4, name: 'Neha Singh', date: '2023-10-22', status: 'purchased', commission: 1200, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 5, name: 'Vikram Joshi', date: '2023-10-21', status: 'signed_up', commission: 0, platform: 'WhatsApp', link: 'whamart.com/ref/rahul123' },
          { id: 6, name: 'Ananya Desai', date: '2023-10-20', status: 'purchased', commission: 900, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
          { id: 7, name: 'Rajesh Gupta', date: '2023-10-19', status: 'visited', commission: 0, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 8, name: 'Meera Reddy', date: '2023-10-18', status: 'signed_up', commission: 0, platform: 'YouTube', link: 'whamart.com/ref/rahul123' },
          { id: 9, name: 'Kiran Shah', date: '2023-10-17', status: 'purchased', commission: 1800, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 10, name: 'Sanjay Verma', date: '2023-10-16', status: 'visited', commission: 0, platform: 'Twitter', link: 'whamart.com/ref/rahul123' },
          { id: 11, name: 'Pooja Mehta', date: '2023-10-15', status: 'purchased', commission: 1350, platform: 'WhatsApp', link: 'whamart.com/ref/rahul123' },
          { id: 12, name: 'Arjun Nair', date: '2023-10-14', status: 'signed_up', commission: 0, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 13, name: 'Divya Sharma', date: '2023-10-13', status: 'visited', commission: 0, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
          { id: 14, name: 'Ravi Patel', date: '2023-10-12', status: 'purchased', commission: 2100, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
          { id: 15, name: 'Sunita Jain', date: '2023-10-11', status: 'signed_up', commission: 0, platform: 'YouTube', link: 'whamart.com/ref/rahul123' }
        ];
        
        setPerformanceData(mockData);
        setReferrals(mockReferrals);
        setTotalPages(Math.ceil(mockReferrals.length / 10) || 1);
      } catch (error) {
        console.error('Error fetching performance data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPerformanceData();
  }, [timeRange]);

  // Filter referrals based on search term and status
  useEffect(() => {
    if (!performanceData) return;
    
    const filteredReferrals = referrals.filter(referral => {
      const matchesSearch = searchTerm === '' || 
        referral.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        referral.platform.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || referral.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });
    
    setTotalPages(Math.ceil(filteredReferrals.length / 10) || 1);
  }, [searchTerm, filterStatus, referrals, performanceData]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 'visited':
        return { color: 'bg-gray-100 text-gray-800', text: 'Visited' };
      case 'signed_up':
        return { color: 'bg-blue-100 text-blue-800', text: 'Signed Up' };
      case 'purchased':
        return { color: 'bg-green-100 text-green-800', text: 'Purchased' };
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Get stats cards data
  const getStatsCards = () => {
    if (!performanceData) return [];

    return [
      {
        name: 'Total Referrals',
        value: performanceData.overview.totalReferrals.value,
        change: performanceData.overview.totalReferrals.change,
        trend: performanceData.overview.totalReferrals.trend,
        icon: LinkIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Signups',
        value: performanceData.overview.totalSignups.value,
        change: performanceData.overview.totalSignups.change,
        trend: performanceData.overview.totalSignups.trend,
        icon: UsersIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Total Sales',
        value: performanceData.overview.totalSales.value,
        change: performanceData.overview.totalSales.change,
        trend: performanceData.overview.totalSales.trend,
        icon: ShoppingBagIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Total Earnings',
        value: formatCurrency(performanceData.overview.totalEarnings.value),
        change: performanceData.overview.totalEarnings.change,
        trend: performanceData.overview.totalEarnings.trend,
        icon: CurrencyRupeeIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  // Get filtered and paginated referrals
  const getFilteredReferrals = () => {
    if (!referrals) return [];
    
    const filteredReferrals = referrals.filter(referral => {
      const matchesSearch = searchTerm === '' || 
        referral.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        referral.platform.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || referral.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });
    
    return filteredReferrals.slice((currentPage - 1) * 10, currentPage * 10);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Performance Analytics</div>
          <h1 className="welcome-title">
            Referral <span style={{ color: '#25D366' }}>Performance</span>
          </h1>
          <p className="welcome-subtitle">
            Track your referral performance, monitor conversion rates, and analyze your marketing effectiveness.
          </p>
        </div>
      </div>

      {/* Time Range Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Performance Overview</h2>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 90 Days</option>
              <option value="year">Last 12 Months</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 500);
              }}
            >
              <ArrowPathIcon className="h-5 w-5" />
              Refresh
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading performance data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              {performanceData && getStatsCards().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={item.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                        {item.change}%
                      </span>
                      <span className="text-gray-500 ml-1">vs. previous period</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Conversion Rates */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Conversion Rates</h2>
              </div>
              {performanceData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Visit to Signup</p>
                      <p className="text-2xl font-bold text-blue-600">{performanceData.conversionRates.visitToSignup}%</p>
                      <p className="text-xs text-gray-500 mt-2">
                        {performanceData.overview.totalReferrals.value} visits → {performanceData.overview.totalSignups.value} signups
                      </p>
                    </div>
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Signup to Purchase</p>
                      <p className="text-2xl font-bold text-green-600">{performanceData.conversionRates.signupToPurchase}%</p>
                      <p className="text-xs text-gray-500 mt-2">
                        {performanceData.overview.totalSignups.value} signups → {performanceData.overview.totalSales.value} purchases
                      </p>
                    </div>
                    <div className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-500 mb-1">Overall Conversion</p>
                      <p className="text-2xl font-bold text-purple-600">{performanceData.conversionRates.visitToPurchase}%</p>
                      <p className="text-xs text-gray-500 mt-2">
                        {performanceData.overview.totalReferrals.value} visits → {performanceData.overview.totalSales.value} purchases
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Monthly Performance Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Monthly Performance</h2>
              </div>
              {performanceData && (
                <div className="card" style={{ padding: '24px' }}>
                  <div style={{ height: '300px', position: 'relative' }}>
                    <div className="chart-container">
                      <div className="flex justify-between items-end h-64 relative">
                        <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between text-xs text-gray-500">
                          <span>35</span>
                          <span>30</span>
                          <span>25</span>
                          <span>20</span>
                          <span>15</span>
                          <span>10</span>
                          <span>5</span>
                          <span>0</span>
                        </div>
                        <div className="ml-10 flex-1 flex items-end justify-between">
                          {performanceData.performanceByMonth.map((item, index) => (
                            <div key={index} className="flex flex-col items-center">
                              <div className="relative">
                                {/* Referrals bar */}
                                <div 
                                  className="w-8 bg-blue-200 rounded-t"
                                  style={{ 
                                    height: `${(item.referrals / 35) * 240}px`,
                                  }}
                                ></div>
                                {/* Signups bar */}
                                <div 
                                  className="w-8 bg-green-200 rounded-t absolute bottom-0 left-0"
                                  style={{ 
                                    height: `${(item.signups / 35) * 240}px`,
                                  }}
                                ></div>
                                {/* Sales bar */}
                                <div 
                                  className="w-8 bg-green-600 rounded-t absolute bottom-0 left-0"
                                  style={{ 
                                    height: `${(item.sales / 35) * 240}px`,
                                    backgroundColor: '#25D366'
                                  }}
                                ></div>
                              </div>
                              <span className="text-xs mt-1 text-gray-500">{item.month}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-center mt-4">
                      <div className="flex items-center mr-4">
                        <div className="w-3 h-3 bg-blue-200 mr-1"></div>
                        <span className="text-xs text-gray-500">Referrals</span>
                      </div>
                      <div className="flex items-center mr-4">
                        <div className="w-3 h-3 bg-green-200 mr-1"></div>
                        <span className="text-xs text-gray-500">Signups</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-600 mr-1" style={{ backgroundColor: '#25D366' }}></div>
                        <span className="text-xs text-gray-500">Sales</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Referrals List */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Referral Details</h2>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search referrals..."
                      value={searchTerm}
                      onChange={handleSearchChange}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    />
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                  <div className="relative">
                    <select
                      value={filterStatus}
                      onChange={(e) => handleFilterChange(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 appearance-none"
                    >
                      <option value="all">All Status</option>
                      <option value="visited">Visited</option>
                      <option value="signed_up">Signed Up</option>
                      <option value="purchased">Purchased</option>
                    </select>
                    <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="card" style={{ padding: '0' }}>
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Date</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Platform</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                      <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Commission</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getFilteredReferrals().map((referral) => {
                      const statusBadge = getStatusBadge(referral.status);
                      return (
                        <tr key={referral.id} className="border-b hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="font-medium text-gray-900">{referral.name}</div>
                            <div className="text-xs text-gray-500">{referral.link}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-500">{formatDate(referral.date)}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-900">{referral.platform}</div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadge.color}`}>
                              {statusBadge.text}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900">
                              {referral.commission > 0 ? formatCurrency(referral.commission) : '-'}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-3 flex items-center justify-between border-t">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">{Math.min(currentPage * 10, getFilteredReferrals().length)}</span> of{' '}
                        <span className="font-medium">{getFilteredReferrals().length}</span> referrals
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 rounded ${currentPage === page ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50 border'}`}
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
