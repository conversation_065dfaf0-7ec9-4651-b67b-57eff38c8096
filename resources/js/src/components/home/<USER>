import { CheckCircleIcon } from '@heroicons/react/24/solid';

export default function HowItWorks() {
  const steps = [
    {
      id: '01',
      name: 'Sign Up',
      description: 'Create your account and choose your subscription plan.',
    },
    {
      id: '02',
      name: 'Set Up Your Store',
      description: 'Add your products, customize your chat flows, and configure your payment methods.',
    },
    {
      id: '03',
      name: 'Share Your Store Link',
      description: 'Share your unique WhatsApp store link with your customers through social media or direct messages.',
    },
    {
      id: '04',
      name: 'Start Selling',
      description: 'Customers can browse and purchase products through a familiar WhatsApp-like chat interface.',
    },
  ];

  return (
    <div id="how-it-works" className="py-24 bg-white">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-base font-semibold leading-7 text-whatsapp-default">How It Works</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Start selling in minutes
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Our simple 4-step process gets you up and running quickly with your WhatsApp store.
          </p>
        </div>
        
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-4">
            {steps.map((step) => (
              <div key={step.id} className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-whatsapp-default text-white text-lg font-bold">
                    {step.id}
                  </div>
                  {step.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                  <p className="flex-auto">{step.description}</p>
                  {step.id === '04' && (
                    <p className="mt-6 flex items-center text-sm font-medium text-whatsapp-default">
                      <CheckCircleIcon className="h-5 w-5 mr-2" />
                      Start your 14-day free trial
                    </p>
                  )}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
}
