<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    /**
     * Get all active subscription plans.
     */
    public function getAllPlans()
    {
        $plans = SubscriptionPlan::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->get();

        return response()->json(['plans' => $plans]);
    }

    /**
     * Get a specific subscription plan by ID.
     */
    public function getPlanById($id)
    {
        $plan = SubscriptionPlan::find($id);

        if (!$plan) {
            return response()->json(['message' => 'Subscription plan not found'], 404);
        }

        return response()->json(['plan' => $plan]);
    }

    /**
     * Get the current user's active subscription.
     */
    public function getCurrentSubscription()
    {
        $user = Auth::user();

        $subscription = UserSubscription::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->with('plan')
            ->orderBy('end_date', 'desc')
            ->first();

        if (!$subscription) {
            // Mimic the original Node.js behavior of returning a default free plan object
            $defaultPlan = [
                'name' => 'Free',
                'startDate' => now()->toIso8601String(),
                'endDate' => now()->addYear()->toIso8601String(),
                'features' => ['20 products', '1 chat flow', 'Basic support', 'Verified badge'],
                'price' => '₹1,499',
                'originalPrice' => '₹1,999'
            ];
            return response()->json([
                'message' => 'No active subscription found',
                'defaultPlan' => $defaultPlan
            ], 404);
        }

        return response()->json(['subscription' => $subscription]);
    }

    /**
     * Create a new subscription for the authenticated user.
     */
    public function createSubscription(Request $request)
    {
        $request->validate([
            'planId' => 'required|exists:subscription_plans,id',
            'paymentMethod' => 'required|string',
            'transactionId' => 'required|string',
            'amount' => 'sometimes|numeric',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::find($request->planId);

        $startDate = Carbon::now();
        $endDate = $plan->interval === 'monthly' 
            ? $startDate->copy()->addMonth() 
            : $startDate->copy()->addYear();

        $subscription = UserSubscription::create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'active',
            'payment_status' => 'paid',
            'payment_method' => $request->paymentMethod,
            'transaction_id' => $request->transactionId,
            'amount' => $request->amount ?? $plan->price,
            'auto_renew' => false, // Defaulting to false as in the original controller
        ]);

        return response()->json([
            'message' => 'Subscription created successfully',
            'subscription' => $subscription
        ], 201);
    }

    /**
     * Update the status of a subscription.
     */
    public function updateSubscriptionStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|string|in:active,cancelled,expired',
        ]);

        $subscription = UserSubscription::find($id);

        if (!$subscription) {
            return response()->json(['message' => 'Subscription not found'], 404);
        }

        // Authorization check will be handled by a Policy
        $this->authorize('update', $subscription);

        $subscription->status = $request->status;

        if ($request->status === 'cancelled') {
            $subscription->cancelled_at = now();
            $subscription->auto_renew = false;
        }

        $subscription->save();

        return response()->json([
            'message' => 'Subscription updated successfully',
            'subscription' => $subscription
        ]);
    }
    /** Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
