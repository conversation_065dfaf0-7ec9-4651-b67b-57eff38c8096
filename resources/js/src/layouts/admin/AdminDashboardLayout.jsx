import { useState, useEffect } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/dashboard.css';
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  HomeIcon,
  UsersIcon,
  ChartBarIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  BuildingStorefrontIcon,
  CreditCardIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  AcademicCapIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

import MarketingRoutes from '../../routes/marketing.routes';
export default function AdminDashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true); // For desktop sidebar visibility
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  }, [location.pathname]);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    if (window.innerWidth < 768) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarVisible(!sidebarVisible);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      // Handle logout error silently
    }
  };

  // Define navigation items for admin
  const getNavItems = () => {
    // Main navigation items
    const mainItems = [
      { name: 'Dashboard', href: '/admin', icon: HomeIcon },
      { name: 'Vendors', href: '/admin/vendors', icon: BuildingStorefrontIcon },
      { name: 'Users', href: '/admin/users', icon: UsersIcon },
      { name: 'Analytics', href: '/admin/analytics', icon: ChartBarIcon },
      { name: 'Marketing', href: '/admin/marketing', icon: ChartBarIcon },
      { name: 'Investment', href: '/admin/investment', icon: CurrencyDollarIcon },
    ];

    // Platform management items
    const platformItems = [
      { name: 'Subscription Plans', href: '/admin/subscriptions', icon: CreditCardIcon },
      { name: 'Message Templates', href: '/admin/message-templates', icon: ChatBubbleOvalLeftEllipsisIcon },
      { name: 'Academy Content', href: '/admin/academy', icon: AcademicCapIcon },
      { name: 'Permissions', href: '/admin/permissions', icon: ShieldCheckIcon },
    ];

    // Secondary navigation items
    const secondaryItems = [
      { name: 'Settings', href: '/admin/settings', icon: CogIcon },
      { name: 'Help & Support', href: '/admin/support', icon: QuestionMarkCircleIcon },
    ];

    return {
      main: mainItems,
      platform: platformItems,
      secondary: secondaryItems
    };
  };

  // Determine the active route
  const isActiveRoute = (href) => {
    const currentPath = window.location.pathname;
    return currentPath === href || (href !== '/admin' && currentPath.startsWith(href));
  };

  const navItems = getNavItems();

  return (
    <div className="dashboard-layout">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div className={`dashboard-sidebar md:hidden z-40 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button
            type="button"
            className="flex items-center justify-center h-8 w-8 rounded-full bg-accent hover:bg-primary transition-colors"
            onClick={() => setSidebarOpen(false)}
          >
            <span className="sr-only">Close sidebar</span>
            <XMarkIcon className="h-5 w-5 text-white" aria-hidden="true" />
          </button>
        </div>

        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => {
              if (item.subItems) {
                return (
                  <div key={item.name}>
                    <div className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}>
                      <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                      {item.name}
                    </div>
                    {item.subItems.map((subItem) => (
                      <Link
                        key={subItem.name}
                        to={subItem.href}
                        className={`dashboard-nav-subitem ${isActiveRoute(subItem.href) ? 'active' : ''}`}
                      >
                        {subItem.name}
                      </Link>
                    ))}
                  </div>
                );
              } else {
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
                  >
                    <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                    {item.name}
                  </Link>
                );
              }
            })}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Platform Management</div>
            {navItems.platform.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Help & Settings</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className={`hidden md:block dashboard-sidebar ${!sidebarVisible ? 'sidebar-collapsed' : ''}`}>
        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => {
              if (item.subItems) {
                return (
                  <div key={item.name}>
                    <div className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}>
                      <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                      {item.name}
                    </div>
                    {item.subItems.map((subItem) => (
                      <Link
                        key={subItem.name}
                        to={subItem.href}
                        className={`dashboard-nav-subitem ${isActiveRoute(subItem.href) ? 'active' : ''}`}
                      >
                        {subItem.name}
                      </Link>
                    ))}
                  </div>
                );
              } else {
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
                  >
                    <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                    {item.name}
                  </Link>
                );
              }
            })}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Platform Management</div>
            {navItems.platform.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Help & Settings</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className={`dashboard-main-content ${!sidebarVisible ? 'sidebar-collapsed-content' : ''}`}>
        {/* Mobile header */}
        <div className="dashboard-mobile-header">
          <button
            type="button"
            className="dashboard-mobile-menu-button"
            onClick={toggleSidebar}
          >
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
          <h1 className="dashboard-mobile-brand">Whamart Admin</h1>
          <div className="w-6"></div> {/* Empty div for flex spacing */}
        </div>

        {/* Desktop header */}
        <div className="dashboard-header">
          <div className="dashboard-header-content">
            <div className="dashboard-header-left">
              <button
                type="button"
                className="dashboard-header-menu-button"
                onClick={toggleSidebar}
                title={sidebarVisible ? "Hide Sidebar" : "Show Sidebar"}
              >
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
              <h1 className="dashboard-header-title">Admin Dashboard</h1>
            </div>
            <div className="dashboard-header-actions">
              <div className="dashboard-header-search">
                <input type="text" placeholder="Search..." className="dashboard-search-input" />
              </div>
              <div className="dashboard-header-user">
                <div className="dashboard-header-user-info">
                  <p className="dashboard-header-user-name">{currentUser?.name || 'User'}</p>
                  <p className="dashboard-header-user-role">Admin</p>
                </div>
                <div className="dashboard-header-user-avatar">
                  <UserCircleIcon className="h-8 w-8 text-gray-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-content-wrapper">
          <Outlet />
          <MarketingRoutes />
        </div>
      </div>
    </div>
  );
}
