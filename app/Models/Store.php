<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Store extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'businessCategory',
        'businessSubcategory',
        'businessType',
        'productType',
        'logoUrl',
        'storeUrl',
        'isVerified',
        'isActive',
        'theme',
        'contactPhone',
        'contactEmail',
        'address',
    ];
    
    /**
     * Get the user that owns the store.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full URL for the store's logo.
     *
     * @return string|null
     */
    public function getLogoUrlAttribute(): ?string
    {
        // Access the raw attribute to avoid conflicts with accessors.
        $logoPath = $this->attributes['logoUrl'] ?? null;

        if ($logoPath) {
            return asset('storage/' . $logoPath);
        }
        return null;
    }

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['logo_url'];
}
