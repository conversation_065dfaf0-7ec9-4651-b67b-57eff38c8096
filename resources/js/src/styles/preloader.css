/* Preloader styles for components */
.preloader-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(37, 211, 102, 0.2);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin 1s linear infinite;
  margin: 20px 0;
}

.preloader-tagline {
  font-size: 18px;
  font-weight: 500;
  color: #075E54;
  text-align: center;
  font-family: 'Poppins', sans-serif;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
