import { useState, useEffect } from 'react';
import { 
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PaperAirplaneIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InfluencerSupport() {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('faq');
  const [expandedFaq, setExpandedFaq] = useState(null);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'normal'
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // FAQ data
  const faqData = [
    {
      id: 1,
      question: 'How do I earn commissions as an influencer?',
      answer: 'As an influencer, you earn commissions when users sign up for WhaMart through your referral links and purchase a subscription plan. You earn 25% of the subscription fee for the first year. Commissions are calculated automatically and added to your earnings dashboard.'
    },
    {
      id: 2,
      question: 'When can I withdraw my earnings?',
      answer: 'Earnings are available for withdrawal 7 days after a successful subscription purchase through your referral link. This holding period ensures that subscriptions are not cancelled or refunded. The minimum withdrawal amount is ₹100.'
    },
    {
      id: 3,
      question: 'How do I create and share referral links?',
      answer: 'Go to the "Referral Links" section in your dashboard to create custom links for different platforms. You can customize the URL slug and add UTM parameters for tracking. Each link includes your unique referral code that attributes sign-ups and purchases to your account.'
    },
    {
      id: 4,
      question: 'What promotional materials are available?',
      answer: 'WhaMart provides a variety of promotional materials including banners, social media templates, product showcases, and video scripts. Visit the "Promotional Material" section to browse and download these resources for your marketing campaigns.'
    },
    {
      id: 5,
      question: 'How do I track my referral performance?',
      answer: 'The "Performance" section of your dashboard provides detailed analytics on your referral activities. You can track clicks, sign-ups, conversions, and earnings for each referral link. Data can be filtered by date range and platform.'
    },
    {
      id: 6,
      question: 'What payment methods are available for withdrawals?',
      answer: 'WhaMart supports withdrawals via bank transfer (NEFT/IMPS), UPI, and popular digital wallets. You can set up and manage your preferred payment methods in the "Settings" section of your dashboard.'
    },
    {
      id: 7,
      question: 'How long does it take to process a withdrawal request?',
      answer: 'Withdrawal requests are typically processed within 2-3 business days. Once processed, the funds will be transferred to your selected payment method, which may take an additional 1-2 business days depending on your bank or payment provider.'
    },
    {
      id: 8,
      question: 'Can I promote specific vendors or products?',
      answer: 'Yes, you can promote specific vendors or products on WhaMart. In the "Campaigns" section, you can find featured vendors and products that offer special commission rates. You can generate specific referral links for these campaigns.'
    },
    {
      id: 9,
      question: 'What happens if a user clicks my link but purchases later?',
      answer: 'WhaMart uses a 30-day cookie to track referrals. If a user clicks your link and then makes a purchase within 30 days, you will still receive the commission, even if they didn\'t complete the purchase immediately.'
    },
    {
      id: 10,
      question: 'How can I increase my conversion rate?',
      answer: 'To increase your conversion rate: 1) Target the right audience interested in online shopping, 2) Highlight the benefits of WhaMart like convenience and no app downloads, 3) Use the promotional materials provided, 4) Create authentic content showing how WhaMart works, and 5) Engage with your audience to answer their questions about the platform.'
    }
  ];

  // Tutorial data
  const tutorialData = [
    {
      id: 1,
      title: 'Getting Started as a WhaMart Influencer',
      description: 'Learn the basics of the WhaMart influencer program and how to set up your account.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '5:32'
    },
    {
      id: 2,
      title: 'Creating Effective Referral Links',
      description: 'Master the art of creating and tracking custom referral links for different platforms.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '4:15'
    },
    {
      id: 3,
      title: 'Using Promotional Materials',
      description: 'Learn how to effectively use the promotional materials provided by WhaMart.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '6:48'
    },
    {
      id: 4,
      title: 'Understanding Your Analytics Dashboard',
      description: 'A deep dive into the performance metrics and how to interpret your results.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '8:21'
    },
    {
      id: 5,
      title: 'Maximizing Your Earnings',
      description: 'Advanced strategies to increase your referrals and commissions.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      duration: '7:55'
    }
  ];

  // Documentation data
  const documentationData = [
    {
      id: 1,
      title: 'Influencer Program Terms & Conditions',
      description: 'Detailed terms and conditions for the WhaMart Influencer Program.',
      icon: DocumentTextIcon,
      url: '#'
    },
    {
      id: 2,
      title: 'Commission Structure Guide',
      description: 'Comprehensive guide to understanding how commissions are calculated and paid.',
      icon: DocumentTextIcon,
      url: '#'
    },
    {
      id: 3,
      title: 'Promotional Guidelines',
      description: 'Guidelines for promoting WhaMart in compliance with advertising regulations.',
      icon: DocumentTextIcon,
      url: '#'
    },
    {
      id: 4,
      title: 'WhaMart Brand Assets',
      description: 'Official brand guidelines and assets for WhaMart promotions.',
      icon: DocumentTextIcon,
      url: '#'
    },
    {
      id: 5,
      title: 'Influencer Dashboard Manual',
      description: 'Detailed guide to using all features of the influencer dashboard.',
      icon: DocumentTextIcon,
      url: '#'
    }
  ];

  // Handle FAQ toggle
  const toggleFaq = (id) => {
    if (expandedFaq === id) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(id);
    }
  };

  // Handle contact form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setContactForm({
      ...contactForm,
      [name]: value
    });
  };

  // Handle contact form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // In a real implementation, this would send the form data to an API
    console.log('Form submitted:', contactForm);
    
    // Show success message
    setFormSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setFormSubmitted(false);
      setContactForm({
        subject: '',
        message: '',
        priority: 'normal'
      });
    }, 3000);
  };

  // Filter FAQ based on search term
  const filteredFaq = faqData.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) || 
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Support Center</div>
          <h1 className="welcome-title">
            Help & <span style={{ color: '#25D366' }}>Support</span>
          </h1>
          <p className="welcome-subtitle">
            Find answers to common questions, watch tutorials, and get in touch with our support team.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Tabs */}
        <div className="flex border-b mb-6 overflow-x-auto">
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'faq' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('faq')}
          >
            <QuestionMarkCircleIcon className="h-5 w-5 mr-2" />
            Frequently Asked Questions
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'tutorials' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('tutorials')}
          >
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Video Tutorials
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'documentation' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('documentation')}
          >
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Documentation
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap flex items-center ${activeTab === 'contact' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('contact')}
          >
            <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
            Contact Support
          </button>
        </div>

        {/* FAQ Tab */}
        {activeTab === 'faq' && (
          <div>
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {filteredFaq.length === 0 ? (
              <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
                <p>No FAQs found matching your search criteria.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredFaq.map((faq) => (
                  <div key={faq.id} className="card hover:shadow-sm transition-shadow">
                    <button
                      className="w-full text-left px-6 py-4 flex justify-between items-center focus:outline-none"
                      onClick={() => toggleFaq(faq.id)}
                    >
                      <h3 className="text-lg font-medium text-gray-900">{faq.question}</h3>
                      {expandedFaq === faq.id ? (
                        <ChevronUpIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                    
                    {expandedFaq === faq.id && (
                      <div className="px-6 pb-4">
                        <p className="text-gray-700">{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Tutorials Tab */}
        {activeTab === 'tutorials' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {tutorialData.map((tutorial) => (
              <div key={tutorial.id} className="card hover:shadow-md transition-shadow overflow-hidden">
                <div className="aspect-video bg-gray-100">
                  <iframe
                    src={tutorial.videoUrl}
                    title={tutorial.title}
                    className="w-full h-full"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
                
                <div className="p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-1">{tutorial.title}</h3>
                  <p className="text-sm text-gray-500 mb-2">{tutorial.description}</p>
                  <div className="flex items-center text-xs text-gray-500">
                    <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{tutorial.duration}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Documentation Tab */}
        {activeTab === 'documentation' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {documentationData.map((doc) => (
              <a
                key={doc.id}
                href={doc.url}
                className="card hover:shadow-md transition-shadow p-6 flex flex-col items-center text-center"
              >
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <doc.icon className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{doc.title}</h3>
                <p className="text-sm text-gray-500">{doc.description}</p>
              </a>
            ))}
          </div>
        )}

        {/* Contact Support Tab */}
        {activeTab === 'contact' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="card" style={{ padding: '24px' }}>
                <h2 className="text-xl font-semibold mb-4">Contact Our Support Team</h2>
                
                {formSubmitted ? (
                  <div className="success-message">
                    <CheckIcon className="h-5 w-5" />
                    Your message has been sent successfully! Our team will get back to you soon.
                  </div>
                ) : (
                  <form onSubmit={handleSubmit}>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Subject *
                        </label>
                        <input
                          type="text"
                          name="subject"
                          value={contactForm.subject}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="e.g. Question about referral links"
                          required
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Priority
                        </label>
                        <select
                          name="priority"
                          value={contactForm.priority}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                          <option value="low">Low - General Question</option>
                          <option value="normal">Normal - Need Help</option>
                          <option value="high">High - Urgent Issue</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Message *
                        </label>
                        <textarea
                          name="message"
                          value={contactForm.message}
                          onChange={handleInputChange}
                          rows={6}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          placeholder="Please describe your issue or question in detail..."
                          required
                        ></textarea>
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          className="btn btn-primary"
                        >
                          <PaperAirplaneIcon style={{ width: '16px', height: '16px' }} />
                          Send Message
                        </button>
                      </div>
                    </div>
                  </form>
                )}
              </div>
            </div>
            
            <div>
              <div className="card" style={{ padding: '24px' }}>
                <h3 className="text-lg font-medium mb-4">Support Hours</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Monday - Friday</p>
                    <p className="text-sm text-gray-500">10:00 AM - 7:00 PM IST</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Saturday</p>
                    <p className="text-sm text-gray-500">10:00 AM - 5:00 PM IST</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Sunday</p>
                    <p className="text-sm text-gray-500">Closed</p>
                  </div>
                </div>
                
                <hr className="my-4" />
                
                <h3 className="text-lg font-medium mb-4">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Email</p>
                    <p className="text-sm text-gray-500"><EMAIL></p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Phone</p>
                    <p className="text-sm text-gray-500">+91 1234567890</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">WhatsApp Support</p>
                    <p className="text-sm text-gray-500">+91 9876543210</p>
                  </div>
                </div>
                
                <hr className="my-4" />
                
                <p className="text-sm text-gray-500">
                  Average response time: 24 hours for normal priority, 4 hours for high priority issues.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
