import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  UsersIcon,
  BuildingStorefrontIcon,
  CurrencyRupeeIcon,
  ChartBarIcon,
  PresentationChartLineIcon,
  BanknotesIcon,
  ArrowPathIcon,
  GlobeAltIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorDashboard() {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month');
  const [dashboardData, setDashboardData] = useState({
    overview: {
      totalInvestment: {
        value: 0,
        change: 0,
        trend: 'neutral'
      },
      currentValue: {
        value: 0,
        change: 0,
        trend: 'neutral'
      },
      equityPercentage: {
        value: 0,
        change: 0,
        trend: 'neutral'
      },
      monthlyRevenue: {
        value: 0,
        change: 0,
        trend: 'neutral'
      }
    },
    vendorMetrics: {
      totalVendors: 0,
      newVendorsThisMonth: 0,
      vendorsByPlan: [],
      vendorRetentionRate: 0
    },
    revenueMetrics: {
      monthlyRevenue: [],
      projectedAnnualRevenue: 0,
      revenueByPlan: []
    },
    growthProjections: {
      capitalGrowth: [],
      equityValue: [],
      marketExpansion: {
        currentCities: 0,
        plannedExpansion: 0,
        targetCities: []
      }
    },
    equityDistribution: [],
    upcomingFeatures: []
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          overview: {
            totalInvestment: {
              value: 300000,
              change: 0,
              trend: 'neutral'
            },
            currentValue: {
              value: 345000,
              change: 15,
              trend: 'up'
            },
            equityPercentage: {
              value: 5,
              change: 0,
              trend: 'neutral'
            },
            monthlyRevenue: {
              value: 117000,
              change: 12.5,
              trend: 'up'
            }
          },
          marketMetrics: {
            totalMarketSize: 500000, // 5 लाख वेंडर्स
            currentMarketShare: 0.156, // 0.156%
            targetMarketShare: 4, // 4% in 5 years
            competitorGrowth: 25, // 25% YoY
            whamartGrowth: 85 // 85% YoY
          },
          vendorMetrics: {
            totalVendors: 780,
            newVendorsThisMonth: 120,
            targetVendors: 10000,
            progressToTarget: 7.8,
            vendorsByPlan: [
              { plan: 'Standard', count: 468, revenue: 701532, annualFee: 1499, avgRevenue: 1499 },
              { plan: 'Gold', count: 234, revenue: 702000, annualFee: 3000, avgRevenue: 3000 },
              { plan: 'Premium', count: 78, revenue: 389922, annualFee: 4999, avgRevenue: 4999 }
            ],
            vendorRetentionRate: 94,
            acquisitionCost: 200
          },
          revenueMetrics: {
            monthlyRevenue: [
              { month: 'May', value: 78000 },
              { month: 'Jun', value: 89000 },
              { month: 'Jul', value: 98000 },
              { month: 'Aug', value: 104000 },
              { month: 'Sep', value: 117000 }
            ],
            projectedAnnualRevenue: 1800000,
            revenueByPlan: [
              { plan: 'Standard', value: 58500, percentage: 50 },
              { plan: 'Gold', value: 42000, percentage: 35.9 },
              { plan: 'Premium', value: 16500, percentage: 14.1 }
            ],
            expenseBreakdown: [
              { category: 'Development', value: 45000, percentage: 38.5 },
              { category: 'Server & Infrastructure', value: 18000, percentage: 15.4 },
              { category: 'Marketing', value: 30000, percentage: 25.6 },
              { category: 'Operations', value: 15000, percentage: 12.8 },
              { category: 'Administrative', value: 9000, percentage: 7.7 }
            ],
            profitMargin: 25.6
          },
          growthProjections: {
            capitalGrowth: [
              { year: '2025', value: 345000 },
              { year: '2026', value: 1150000 },
              { year: '2027', value: 3450000 },
              { year: '2028', value: 8625000 },
              { year: '2029', value: 17250000 }
            ],
            equityValue: [
              { year: '2025', value: 30000000 }, // 3 करोड़
              { year: '2026', value: 100000000 }, // 10 करोड़
              { year: '2027', value: 300000000 }, // 30 करोड़
              { year: '2028', value: 750000000 }, // 75 करोड़
              { year: '2029', value: 1500000000 } // 150 करोड़
            ],
            vendorGrowth: [
              { year: '2025', value: 2500 },
              { year: '2026', value: 6000 },
              { year: '2027', value: 12000 },
              { year: '2028', value: 18000 },
              { year: '2029', value: 25000 }
            ],
            marketShare: [
              { year: '2025', value: 0.5 }, // 0.5%
              { year: '2026', value: 1.2 }, // 1.2%
              { year: '2027', value: 2.4 }, // 2.4%
              { year: '2028', value: 3.6 }, // 3.6%
              { year: '2029', value: 5.0 } // 5.0%
            ],
            marketExpansion: {
              currentCities: 8,
              plannedExpansion: 12,
              targetCities: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur', 'Lucknow', 'Surat', 'Kanpur']
            }
          },
          equityDistribution: [
            { holder: 'Founder (Karan Solanki)', percentage: 60 },
            { holder: 'Current Investor', percentage: 5 },
            { holder: 'Reserved for Future Investors', percentage: 20 },
            { holder: 'Employee Stock Options', percentage: 10 },
            { holder: 'Advisors', percentage: 5 }
          ],
          upcomingFeatures: [
            { name: 'AI-Powered Chat Recommendations', status: 'In Development', eta: 'Q4 2025' },
            { name: 'Multi-language Support', status: 'Planning', eta: 'Q1 2026' },
            { name: 'Advanced Analytics Dashboard', status: 'In Development', eta: 'Q4 2025' },
            { name: 'Integrated Payment Gateway', status: 'Research', eta: 'Q2 2026' },
            { name: 'Mobile App for Vendors', status: 'Planning', eta: 'Q3 2026' }
          ],
          financialMetrics: {
            monthlyProfit: 30000,
            investorShare: 0, // इन्वेस्टर को प्रॉफिट शेयर नहीं मिलेगा
            operatingExpenses: 87000,
            cashReserves: 180000,
            burnRate: 87000,
            runway: 6.2,
            reinvestmentRate: 100, // 100% प्रॉफिट रीइन्वेस्ट किया जाएगा
            exitStrategy: "Equity sale to larger investor or founder buyback after valuation growth"
          }
        };

        setDashboardData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Get stats cards data
  const getStatsCards = () => {
    if (!dashboardData) return [];

    return [
      {
        name: 'Total Investment',
        value: formatCurrency(dashboardData.overview.totalInvestment.value),
        change: dashboardData.overview.totalInvestment.change,
        trend: dashboardData.overview.totalInvestment.trend,
        icon: BanknotesIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Current Value',
        value: formatCurrency(dashboardData.overview.currentValue.value),
        change: dashboardData.overview.currentValue.change,
        trend: dashboardData.overview.currentValue.trend,
        icon: ChartBarIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Projected EOY Value',
        value: formatCurrency(dashboardData.growthProjections.equityValue[0].value * dashboardData.overview.equityPercentage.value / 100),
        change: Math.round((dashboardData.growthProjections.equityValue[0].value * dashboardData.overview.equityPercentage.value / 100 / dashboardData.overview.currentValue.value - 1) * 100),
        trend: 'up',
        icon: PresentationChartLineIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Monthly Revenue',
        value: formatCurrency(dashboardData.overview.monthlyRevenue.value),
        change: dashboardData.overview.monthlyRevenue.change,
        trend: dashboardData.overview.monthlyRevenue.trend,
        icon: CurrencyRupeeIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Investor Dashboard</div>
          <h1 className="welcome-title">
            Welcome, <span style={{ color: '#25D366' }}>{currentUser?.name || 'Investor'}</span>
          </h1>
          <p className="welcome-subtitle">
            Track your investment performance, monitor platform growth, and view future projections.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Time Range Filter */}
        <div className="flex justify-end mb-6">
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                timeRange === 'month'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('month')}
            >
              Month
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium ${
                timeRange === 'quarter'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('quarter')}
            >
              Quarter
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                timeRange === 'year'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('year')}
            >
              Year
            </button>
          </div>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="stats-grid">
              {dashboardData && getStatsCards().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : item.trend === 'down' ? (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      ) : (
                        <span className="h-4 w-4 inline-block"></span>
                      )}
                      <span className={`text-sm ${
                        item.trend === 'up'
                          ? 'text-green-500'
                          : item.trend === 'down'
                            ? 'text-red-500'
                            : 'text-gray-500'
                      }`}>
                        {item.change > 0 ? '+' : ''}{item.change}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Vendor Metrics */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Vendor Metrics</h2>
                <Link to="/investor/vendor-analytics" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  View Detailed Analytics
                </Link>
              </div>
              {dashboardData && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Vendor Acquisition</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-500 mb-1">Total Vendors</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.vendorMetrics.totalVendors}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-500 mb-1">New This Month</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.vendorMetrics.newVendorsThisMonth}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-500 mb-1">Acquisition Cost</p>
                        <p className="text-2xl font-bold text-gray-900">{formatCurrency(dashboardData.vendorMetrics.acquisitionCost)}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-500 mb-1">2025 Target</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.vendorMetrics.targetVendors.toLocaleString()}</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-gray-500">Vendor Acquisition Progress</p>
                        <p className="text-sm font-medium text-gray-900">{dashboardData.vendorMetrics.totalVendors} / {dashboardData.vendorMetrics.targetVendors} ({dashboardData.vendorMetrics.progressToTarget}%)</p>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-green-500 rounded-full"
                          style={{ width: `${dashboardData.vendorMetrics.progressToTarget}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Plans</h3>
                    <div className="space-y-4">
                      {dashboardData.vendorMetrics.vendorsByPlan.map((plan) => (
                        <div key={plan.plan} className="mb-4">
                          <div className="flex items-center mb-2">
                            <div className="w-32 text-sm font-medium text-gray-900">{plan.plan}</div>
                            <div className="flex-1">
                              <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                                <div
                                  className="h-full rounded-full"
                                  style={{
                                    width: `${(plan.count / dashboardData.vendorMetrics.totalVendors) * 100}%`,
                                    backgroundColor: plan.plan === 'Standard' ? '#25D366' : plan.plan === 'Gold' ? '#FFD700' : '#075E54'
                                  }}
                                ></div>
                              </div>
                            </div>
                            <div className="w-16 text-right text-sm font-medium text-gray-900">{plan.count}</div>
                            <div className="w-16 text-right text-xs text-gray-500">
                              {Math.round((plan.count / dashboardData.vendorMetrics.totalVendors) * 100)}%
                            </div>
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 px-32">
                            <span>Annual Fee: {formatCurrency(plan.annualFee)}</span>
                            <span>Revenue: {formatCurrency(plan.revenue)}</span>
                          </div>
                        </div>
                      ))}
                      <div className="pt-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-900">Total Annual Revenue</span>
                          <span className="text-sm font-bold text-gray-900">
                            {formatCurrency(dashboardData.vendorMetrics.vendorsByPlan.reduce((sum, plan) => sum + plan.revenue, 0))}
                          </span>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <span className="text-sm font-medium text-gray-900">Avg. Revenue Per Vendor</span>
                          <span className="text-sm font-bold text-gray-900">
                            {formatCurrency(dashboardData.vendorMetrics.vendorsByPlan.reduce((sum, plan) => sum + plan.revenue, 0) / dashboardData.vendorMetrics.totalVendors)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Financial Performance */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Financial Performance</h2>
                <Link to="/investor/revenue-analytics" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  View Detailed Analytics
                </Link>
              </div>
              {dashboardData && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue Trend</h3>
                    <div style={{ height: '200px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {dashboardData.revenueMetrics.monthlyRevenue.map((item, index) => {
                          const maxValue = Math.max(...dashboardData.revenueMetrics.monthlyRevenue.map(i => i.value));
                          const height = (item.value / maxValue) * 180;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className="w-12 rounded-t-md"
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === dashboardData.revenueMetrics.monthlyRevenue.length - 1 ? '#25D366' : '#E9F7EF'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.month}</div>
                              <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Expense Breakdown</h3>
                    <div className="space-y-4">
                      {dashboardData.revenueMetrics.expenseBreakdown.map((expense, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-32 text-sm font-medium text-gray-900">{expense.category}</div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full rounded-full"
                                style={{
                                  width: `${expense.percentage}%`,
                                  backgroundColor: index % 2 === 0 ? '#25D366' : '#075E54'
                                }}
                              ></div>
                            </div>
                          </div>
                          <div className="w-24 text-right text-sm font-medium text-gray-900">{formatCurrency(expense.value)}</div>
                          <div className="w-16 text-right text-xs text-gray-500">
                            {expense.percentage}%
                          </div>
                        </div>
                      ))}
                      <div className="pt-4 border-t border-gray-200 flex justify-between">
                        <span className="text-sm font-medium text-gray-900">Total Monthly Expenses</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(dashboardData.financialMetrics.operatingExpenses)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Growth Projections */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Growth Projections</h2>
                <Link to="/investor/projections" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  View Detailed Projections
                </Link>
              </div>
              {dashboardData && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">5-Year Vendor Growth</h3>
                    <div style={{ height: '200px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {dashboardData.growthProjections.vendorGrowth.map((item, index) => {
                          const maxValue = Math.max(...dashboardData.growthProjections.vendorGrowth.map(i => i.value));
                          const height = (item.value / maxValue) * 180;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className="w-12 rounded-t-md"
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#25D366' : 'rgba(37, 211, 102, 0.6)'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900">{item.value.toLocaleString()}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">5-Year Valuation Growth</h3>
                    <div style={{ height: '200px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {dashboardData.growthProjections.equityValue.map((item, index) => {
                          const maxValue = Math.max(...dashboardData.growthProjections.equityValue.map(i => i.value));
                          const height = (item.value / maxValue) * 180;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className="w-12 rounded-t-md"
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#075E54' : 'rgba(7, 94, 84, 0.6)'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Market Share */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Market Analysis</h2>
                <Link to="/investor/projections" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  View Detailed Analysis
                </Link>
              </div>
              {dashboardData && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Market Share Growth</h3>
                    <div style={{ height: '200px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {dashboardData.growthProjections.marketShare.map((item, index) => {
                          const maxValue = Math.max(...dashboardData.growthProjections.marketShare.map(i => i.value));
                          const height = (item.value / maxValue) * 180;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className="w-12 rounded-t-md"
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#25D366' : 'rgba(37, 211, 102, 0.6)'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900">{item.value}%</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="card" style={{ padding: '24px' }}>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Market Overview</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Total Addressable Market</p>
                          <p className="text-xl font-bold text-gray-900">{dashboardData.marketMetrics.totalMarketSize.toLocaleString()} vendors</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Current Market Share</p>
                          <p className="text-xl font-bold text-gray-900">{dashboardData.marketMetrics.currentMarketShare}%</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">5-Year Target Market Share</p>
                          <p className="text-xl font-bold text-gray-900">{dashboardData.marketMetrics.targetMarketShare}%</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-gray-500 mb-1">WhaMart Growth Rate (YoY)</p>
                          <p className="text-xl font-bold text-green-600">{dashboardData.marketMetrics.whamartGrowth}%</p>
                        </div>
                        <div className="text-sm text-green-600 flex items-center">
                          <ArrowUpIcon className="h-4 w-4 mr-1" />
                          {dashboardData.marketMetrics.whamartGrowth - dashboardData.marketMetrics.competitorGrowth}% vs competitors
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Quick Actions</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link to="/investor/capital" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                      <PlusIcon className="h-6 w-6" style={{ color: '#25D366' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Add Capital</h3>
                      <p className="text-sm text-gray-500">Invest additional funds</p>
                    </div>
                  </div>
                </Link>
                <Link to="/investor/equity" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(7, 94, 84, 0.1)' }}>
                      <PresentationChartLineIcon className="h-6 w-6" style={{ color: '#075E54' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Equity Distribution</h3>
                      <p className="text-sm text-gray-500">View ownership details</p>
                    </div>
                  </div>
                </Link>
                <Link to="/investor/market-expansion" className="card hover:shadow-md transition-shadow" style={{ padding: '24px' }}>
                  <div className="flex items-center">
                    <div className="rounded-full p-3 mr-4" style={{ backgroundColor: 'rgba(37, 211, 102, 0.1)' }}>
                      <GlobeAltIcon className="h-6 w-6" style={{ color: '#25D366' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Market Expansion</h3>
                      <p className="text-sm text-gray-500">View expansion plans</p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
