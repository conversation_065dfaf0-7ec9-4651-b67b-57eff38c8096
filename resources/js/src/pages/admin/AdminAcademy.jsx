import { useState, useEffect } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  PlusIcon,
  XMarkIcon,
  PlayIcon,
  ClockIcon,
  AcademicCapIcon,
  ArrowUpTrayIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import '../../styles/vendor-dashboard.css';

export default function AdminAcademy() {
  const [tutorials, setTutorials] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  const [editingTutorial, setEditingTutorial] = useState(null);
  const [isAddingTutorial, setIsAddingTutorial] = useState(false);
  const [previewTutorial, setPreviewTutorial] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    videoUrl: '',
    thumbnail: '',
    duration: '',
    featured: false
  });
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState('');

  // Fetch tutorials and categories
  useEffect(() => {
    const fetchTutorialsAndCategories = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockCategories = [
          { id: 'getting-started', name: 'Getting Started' },
          { id: 'store-setup', name: 'Store Setup' },
          { id: 'product-management', name: 'Product Management' },
          { id: 'chat-flow', name: 'Chat Flow Builder' },
          { id: 'orders', name: 'Order Management' },
          { id: 'marketing', name: 'Marketing & Growth' }
        ];
        
        const mockTutorials = [
          {
            id: 1,
            title: 'Welcome to Whamart',
            description: 'Learn about Whamart and how it can help you grow your business on WhatsApp.',
            thumbnail: '/academy/welcome-thumbnail.jpg',
            duration: '3:45',
            category: 'getting-started',
            featured: true,
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
          },
          {
            id: 2,
            title: 'Creating Your Account',
            description: 'Step-by-step guide to creating and setting up your Whamart vendor account.',
            thumbnail: '/academy/account-setup-thumbnail.jpg',
            duration: '5:20',
            category: 'getting-started',
            featured: false,
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
          },
          {
            id: 3,
            title: 'Setting Up Your Store',
            description: 'Learn how to customize your WhatsApp store with your brand information.',
            thumbnail: '/academy/store-setup-thumbnail.jpg',
            duration: '7:15',
            category: 'store-setup',
            featured: true,
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
          },
          {
            id: 4,
            title: 'Adding Products to Your Store',
            description: 'How to add products, set prices, and manage your product catalog.',
            thumbnail: '/academy/add-products-thumbnail.jpg',
            duration: '6:30',
            category: 'product-management',
            featured: true,
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
          },
          {
            id: 5,
            title: 'Creating Your First Chat Flow',
            description: 'Learn how to create automated chat flows for your customers.',
            thumbnail: '/academy/chat-flow-thumbnail.jpg',
            duration: '8:45',
            category: 'chat-flow',
            featured: true,
            videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
          }
        ];
        
        setCategories(mockCategories);
        setTutorials(mockTutorials);
      } catch (error) {
        console.error('Error fetching tutorials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTutorialsAndCategories();
  }, []);

  // Filter tutorials based on selected category
  const filteredTutorials = activeCategory === 'all'
    ? tutorials
    : tutorials.filter(tutorial => tutorial.category === activeCategory);

  // Handle edit tutorial
  const handleEditTutorial = (tutorial) => {
    setEditingTutorial(tutorial.id);
    setFormData({
      title: tutorial.title,
      description: tutorial.description,
      category: tutorial.category,
      videoUrl: tutorial.videoUrl,
      thumbnail: tutorial.thumbnail,
      duration: tutorial.duration,
      featured: tutorial.featured
    });
    setThumbnailPreview(tutorial.thumbnail);
  };

  // Handle add new tutorial
  const handleAddTutorial = () => {
    setIsAddingTutorial(true);
    setFormData({
      title: '',
      description: '',
      category: categories.length > 0 ? categories[0].id : '',
      videoUrl: '',
      thumbnail: '',
      duration: '',
      featured: false
    });
    setThumbnailPreview('');
    setThumbnailFile(null);
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle thumbnail upload
  const handleThumbnailChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setThumbnailFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setThumbnailPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save tutorial
  const handleSaveTutorial = () => {
    // In a real implementation, this would be an API call with file upload
    const thumbnailUrl = thumbnailPreview || '/academy/default-thumbnail.jpg';
    
    if (isAddingTutorial) {
      // Add new tutorial
      const newTutorial = {
        id: Date.now(), // Temporary ID for mock data
        ...formData,
        thumbnail: thumbnailUrl
      };
      setTutorials([...tutorials, newTutorial]);
    } else {
      // Update existing tutorial
      const updatedTutorials = tutorials.map(tutorial => 
        tutorial.id === editingTutorial ? { ...tutorial, ...formData, thumbnail: thumbnailUrl } : tutorial
      );
      setTutorials(updatedTutorials);
    }
    
    // Reset form
    setEditingTutorial(null);
    setIsAddingTutorial(false);
    setThumbnailFile(null);
    setThumbnailPreview('');
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingTutorial(null);
    setIsAddingTutorial(false);
    setThumbnailFile(null);
    setThumbnailPreview('');
  };

  // Handle delete tutorial
  const handleDeleteTutorial = (tutorialId) => {
    if (window.confirm('Are you sure you want to delete this tutorial?')) {
      // In a real implementation, this would be an API call
      const updatedTutorials = tutorials.filter(tutorial => tutorial.id !== tutorialId);
      setTutorials(updatedTutorials);
    }
  };

  // Handle preview tutorial
  const handlePreviewTutorial = (tutorial) => {
    setPreviewTutorial(tutorial);
  };

  // Format duration from seconds to MM:SS
  const formatDuration = (durationString) => {
    // If already formatted, return as is
    if (durationString.includes(':')) return durationString;
    
    const seconds = parseInt(durationString, 10);
    if (isNaN(seconds)) return '00:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Academy Management</div>
          <h1 className="welcome-title">
            Whamart <span style={{ color: '#25D366' }}>Academy</span>
          </h1>
          <p className="welcome-subtitle">
            Manage tutorial videos and educational content for vendors. Organize tutorials by category and highlight featured content.
          </p>
        </div>
      </div>

      {/* Category Filter */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Tutorial Categories</h2>
        </div>
        <div className="flex flex-wrap gap-2 mb-6">
          <button
            className={`btn ${activeCategory === 'all' ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => setActiveCategory('all')}
          >
            All Tutorials
          </button>
          {categories.map(category => (
            <button
              key={category.id}
              className={`btn ${activeCategory === category.id ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tutorials Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Tutorial Videos</h2>
          <button 
            className="btn btn-primary"
            onClick={handleAddTutorial}
            disabled={isAddingTutorial || editingTutorial !== null}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Add New Tutorial
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading tutorials...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTutorials.map(tutorial => (
              <div key={tutorial.id} className="card" style={{ padding: '0', overflow: 'hidden' }}>
                <div style={{ position: 'relative', width: '100%', height: '160px', overflow: 'hidden' }}>
                  <img
                    src={tutorial.thumbnail || '/academy/default-thumbnail.jpg'}
                    alt={tutorial.title}
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    onError={(e) => { e.target.src = '/academy/default-thumbnail.jpg' }}
                  />
                  <div style={{
                    position: 'absolute',
                    bottom: '8px',
                    right: '8px',
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <ClockIcon style={{ width: '12px', height: '12px' }} />
                    {tutorial.duration}
                  </div>
                  <button
                    style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      width: '48px',
                      height: '48px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                    onClick={() => handlePreviewTutorial(tutorial)}
                  >
                    <PlayIcon style={{ width: '24px', height: '24px' }} />
                  </button>
                  {tutorial.featured && (
                    <div style={{
                      position: 'absolute',
                      top: '8px',
                      left: '8px',
                      backgroundColor: 'rgba(37, 211, 102, 0.9)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}>
                      <StarIconSolid style={{ width: '12px', height: '12px' }} />
                      Featured
                    </div>
                  )}
                </div>
                <div style={{ padding: '16px' }}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '4px' }}>{tutorial.title}</h3>
                      <p style={{ fontSize: '14px', color: 'var(--gray)', marginBottom: '8px' }}>{tutorial.description}</p>
                      <span className="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {categories.find(c => c.id === tutorial.category)?.name || tutorial.category}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditTutorial(tutorial)}
                        className="text-indigo-600 hover:text-indigo-900"
                        disabled={editingTutorial !== null || isAddingTutorial}
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteTutorial(tutorial.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={editingTutorial !== null || isAddingTutorial}
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Edit/Add Tutorial Form */}
      {(editingTutorial !== null || isAddingTutorial) && (
        <div className="stats-section">
          <div className="section-header">
            <h2 className="section-title">{isAddingTutorial ? 'Add New Tutorial' : 'Edit Tutorial'}</h2>
          </div>
          <div className="card" style={{ padding: '24px' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tutorial Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. Getting Started with Whamart"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  required
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="2"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Brief description of the tutorial"
                  required
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Video URL (YouTube Embed)
                </label>
                <input
                  type="text"
                  name="videoUrl"
                  value={formData.videoUrl}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="https://www.youtube.com/embed/VIDEO_ID"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Duration (MM:SS)
                </label>
                <input
                  type="text"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g. 5:30"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Thumbnail Image
                </label>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                      <div className="space-y-1 text-center">
                        <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                          <label
                            htmlFor="thumbnail-upload"
                            className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500"
                          >
                            <span>Upload a file</span>
                            <input
                              id="thumbnail-upload"
                              name="thumbnail-upload"
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={handleThumbnailChange}
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                      </div>
                    </div>
                  </div>
                  {thumbnailPreview && (
                    <div className="w-32 h-24 relative">
                      <img
                        src={thumbnailPreview}
                        alt="Thumbnail preview"
                        className="w-full h-full object-cover rounded"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setThumbnailPreview('');
                          setThumbnailFile(null);
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={formData.featured}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                  Featured Tutorial (shown on dashboard)
                </label>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSaveTutorial}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {isAddingTutorial ? 'Add Tutorial' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tutorial Preview Modal */}
      {previewTutorial && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-3xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">{previewTutorial.title}</h3>
              <button
                onClick={() => setPreviewTutorial(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden', borderRadius: '8px' }}>
              <iframe
                src={previewTutorial.videoUrl}
                style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', border: 'none' }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title={previewTutorial.title}
              ></iframe>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-500">{previewTutorial.description}</p>
              <div className="mt-2 flex items-center space-x-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {categories.find(c => c.id === previewTutorial.category)?.name || previewTutorial.category}
                </span>
                <span className="text-sm text-gray-500 flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {previewTutorial.duration}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
