# WhatsApp Store Theme

This is a full-page WhatsApp-style chat interface that serves as the theme for vendor stores. When visitors access a vendor's unique store link, they will see this interface, giving them the feeling of interacting with the store via WhatsApp.

## Features

- Full-page WhatsApp-style chat interface with header, chat body, and input bar
- Official service banner ("This business uses Whamart official service to manage this chat")
- Date separators (Today, Yesterday, Friday, etc.)
- Message status indicators (sent, delivered, read)
- Verification badge for verified stores
- Responsive design optimized for both mobile and desktop

## Components

- `StoreTheme`: Main container component
- `ChatHeader`: Header with back button, logo, store name, verification badge, and menu
- `ChatBody`: Message display area with date separators and official service banner
- `ChatMessage`: Individual message component with status indicators
- `ChatInputBar`: Input bar with attachment button, text input, and send button
- `DateSeparator`: Date divider between messages
- `OfficialServiceBanner`: Banner at the top of the chat
- `VerificationBadge`: Green verification badge with white checkmark

## Usage

```jsx
import { StoreTheme } from './components/store-theme';

// Sample store data
const storeData = {
  name: "Fashion Store",
  logoUrl: "path/to/logo.png", // Optional, will use default if not provided
  isVerified: true,
  id: "fashion-store-123"
};

function StoreView() {
  return <StoreTheme storeData={storeData} />;
}
```

## Props

### StoreTheme

| Prop | Type | Description |
|------|------|-------------|
| storeData | Object | Store information |
| storeData.name | String | Name of the store |
| storeData.logoUrl | String | URL to the store logo (optional) |
| storeData.isVerified | Boolean | Whether the store is verified |
| storeData.id | String | Unique ID of the store |

## Styling

The theme uses a dedicated CSS file (`styles/StoreTheme.css`) that includes all the necessary styles for the WhatsApp-like appearance. The styles include:

- WhatsApp color scheme (green header, light green outgoing messages)
- Chat bubble styling with triangular corners
- Message status indicators
- WhatsApp doodle background pattern from the public folder
- Responsive layout for different screen sizes
- Clean white header with logo, store name and verification badge

### Background Image

The chat background uses the image `chat-background.jpg` from the public folder. This image is set directly in the ChatBody component using inline styles to ensure it loads properly.

The background image is configured with the following properties:
- `backgroundImage: url('/chat-background.jpg')` - Uses the image from the public folder
- `backgroundRepeat: repeat` - Ensures the pattern repeats to cover the entire chat area
- `backgroundSize: '250px auto'` - Sets a larger size for the doodle pattern to ensure clear visibility

For the background image to work properly:
1. Make sure the image file exists in the public folder (we've created a copy without spaces in the filename)
2. The image should be small enough to repeat efficiently as a pattern

## Demo

Two demos of the WhatsApp Store Theme are available:

1. `/store-theme-demo` - A basic demo of the store theme
2. `/store/:storeId` - A dynamic store page that loads store data based on the storeId parameter

Example: `/store/sample-store` will load a store with the ID "sample-store".
