import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

export default function Blog() {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens (same as Home.jsx)
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529'
  };

  // Sample blog posts data
  const blogPosts = [
    {
      id: 1,
      title: "How to Boost Your Sales with WhatsApp Marketing",
      excerpt: "Learn effective strategies to increase your sales and customer engagement through WhatsApp Business and automation tools.",
      imageUrl: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Marketing",
      author: "Sarah Johnson",
      date: "May 15, 2023",
      readTime: "5 min read"
    },
    {
      id: 2,
      title: "Setting Up Your First WhatsApp Store: A Complete Guide",
      excerpt: "A step-by-step guide to creating your first WhatsApp store and optimizing it for maximum conversions and customer satisfaction.",
      imageUrl: "https://images.unsplash.com/photo-1512428559087-560fa5ceab42?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Tutorial",
      author: "Michael Chen",
      date: "June 3, 2023",
      readTime: "8 min read"
    },
    {
      id: 3,
      title: "The Future of Social Commerce: WhatsApp vs Instagram",
      excerpt: "An in-depth comparison of WhatsApp and Instagram as e-commerce platforms, with insights on which might be better for your business.",
      imageUrl: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Trends",
      author: "David Rodriguez",
      date: "July 12, 2023",
      readTime: "6 min read"
    },
    {
      id: 4,
      title: "5 Ways to Automate Your Customer Service with Chat Flows",
      excerpt: "Learn how to set up automated chat flows that handle customer inquiries, provide product information, and streamline your support process.",
      imageUrl: "https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Automation",
      author: "Jessica Lee",
      date: "August 20, 2023",
      readTime: "7 min read"
    },
    {
      id: 5,
      title: "Success Story: How a Small Business Tripled Sales with WhaMart",
      excerpt: "Case study of a small business that transformed their customer engagement and sales using WhaMart's WhatsApp commerce platform.",
      imageUrl: "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Case Study",
      author: "Anita Patel",
      date: "September 5, 2023",
      readTime: "4 min read"
    },
    {
      id: 6,
      title: "WhatsApp Business API: What You Need to Know",
      excerpt: "A comprehensive guide to understanding and leveraging the WhatsApp Business API for your e-commerce and marketing efforts.",
      imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      category: "Technology",
      author: "Robert Williams",
      date: "October 18, 2023",
      readTime: "9 min read"
    }
  ];

  // Categories for filter
  const categories = ["All", "Marketing", "Tutorial", "Trends", "Automation", "Case Study", "Technology"];
  const [activeCategory, setActiveCategory] = useState("All");

  // Filtered posts based on active category
  const filteredPosts = activeCategory === "All" 
    ? blogPosts 
    : blogPosts.filter(post => post.category === activeCategory);

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      margin: 0,
      padding: 0,
      color: colors.dark,
      backgroundColor: colors.white
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: colors.white,
        color: colors.dark,
        padding: '15px 5%',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: isMobile ? '15px' : '0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/">
            <img
              src="/WhaMart_Logo.png"
              alt="WhaMart Logo"
              style={{
                height: '40px',
                marginRight: '10px'
              }}
            />
          </Link>
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '30px'
        }}>
          {!isMobile && (
            <nav>
              <ul style={{
                display: 'flex',
                listStyle: 'none',
                gap: '30px',
                margin: 0,
                padding: 0
              }}>
                <li><Link to="/#features" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Features</Link></li>
                <li><Link to="/#how-it-works" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>How It Works</Link></li>
                <li><Link to="/#pricing" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Pricing</Link></li>
                <li><Link to="/blog" style={{ color: colors.primary, textDecoration: 'none', fontWeight: 600, fontSize: '16px' }}>Blog</Link></li>
              </ul>
            </nav>
          )}

          <div style={{ display: 'flex', gap: '15px' }}>
            <Link to="/login" style={{
              padding: '10px 20px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              transition: 'all 0.3s ease'
            }}>
              Sign In
            </Link>
            <Link to="/register" style={{
              padding: '10px 20px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Blog Hero Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '60px 5%',
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '10%',
          right: '5%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{ position: 'relative', zIndex: 1 }}>
          <h1 style={{
            fontSize: isMobile ? '32px' : '42px',
            fontWeight: 700,
            marginBottom: '16px',
            color: colors.dark
          }}>
            WhaMart <span style={{ color: colors.primary }}>Blog</span>
          </h1>
          <p style={{
            fontSize: '18px',
            maxWidth: '700px',
            margin: '0 auto 32px',
            color: colors.gray,
            lineHeight: 1.6
          }}>
            Insights, tips, and strategies to help you build a successful WhatsApp-based business
          </p>

          {/* Category filters */}
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            gap: '12px',
            marginTop: '24px'
          }}>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: activeCategory === category ? colors.primary : 'transparent',
                  color: activeCategory === category ? colors.white : colors.gray,
                  border: activeCategory === category ? 'none' : `1px solid ${colors.gray}`,
                  borderRadius: '50px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 500,
                  transition: 'all 0.3s ease'
                }}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section style={{
        padding: '80px 5%',
        backgroundColor: colors.white
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(auto-fill, minmax(350px, 1fr))',
          gap: '30px',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {filteredPosts.map((post) => (
            <div key={post.id} style={{
              backgroundColor: colors.white,
              borderRadius: '16px',
              overflow: 'hidden',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.05)',
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              border: '1px solid rgba(0,0,0,0.05)',
              cursor: 'pointer',
              ':hover': {
                transform: 'translateY(-10px)',
                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.1)'
              }
            }}>
              {/* Post Image */}
              <div style={{
                width: '100%',
                height: '200px',
                overflow: 'hidden',
                position: 'relative'
              }}>
                <img
                  src={post.imageUrl}
                  alt={post.title}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    transition: 'transform 0.5s ease'
                  }}
                />
                <div style={{
                  position: 'absolute',
                  top: '16px',
                  left: '16px',
                  backgroundColor: colors.primary,
                  color: colors.white,
                  padding: '6px 12px',
                  borderRadius: '50px',
                  fontSize: '12px',
                  fontWeight: 600
                }}>
                  {post.category}
                </div>
              </div>
              
              {/* Post Content */}
              <div style={{ padding: '24px' }}>
                <h3 style={{
                  fontSize: '20px',
                  fontWeight: 700,
                  marginBottom: '12px',
                  lineHeight: 1.3
                }}>
                  {post.title}
                </h3>
                <p style={{
                  color: colors.gray,
                  fontSize: '15px',
                  lineHeight: 1.6,
                  marginBottom: '20px'
                }}>
                  {post.excerpt}
                </p>
                
                {/* Post Meta */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  borderTop: `1px solid ${colors.lightGray}`,
                  paddingTop: '16px',
                  marginTop: '16px',
                  fontSize: '13px',
                  color: colors.gray
                }}>
                  <div>By {post.author}</div>
                  <div style={{ display: 'flex', gap: '16px' }}>
                    <span>{post.date}</span>
                    <span>{post.readTime}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show more button */}
        <div style={{ textAlign: 'center', marginTop: '50px' }}>
          <button style={{
            padding: '14px 32px',
            backgroundColor: 'transparent',
            color: colors.primary,
            border: `1px solid ${colors.primary}`,
            borderRadius: '50px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 600,
            transition: 'all 0.3s ease'
          }}>
            Load More Articles
          </button>
        </div>
      </section>

      {/* Newsletter Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '80px 5%',
        textAlign: 'center'
      }}>
        <div style={{
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          <h2 style={{
            fontSize: '32px',
            fontWeight: 700,
            marginBottom: '16px',
            color: colors.dark
          }}>
            Subscribe to Our Newsletter
          </h2>
          <p style={{
            fontSize: '16px',
            color: colors.gray,
            marginBottom: '32px',
            lineHeight: 1.6
          }}>
            Get the latest articles, resources, and tips about WhatsApp marketing and e-commerce delivered to your inbox.
          </p>
          
          <div style={{
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            gap: '16px',
            width: '100%'
          }}>
            <input
              type="email"
              placeholder="Enter your email address"
              style={{
                flex: 1,
                padding: '16px 24px',
                borderRadius: '50px',
                border: '1px solid rgba(0,0,0,0.1)',
                fontSize: '16px',
                outline: 'none'
              }}
            />
            <button style={{
              padding: isMobile ? '16px 24px' : '16px 32px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 600,
              whiteSpace: 'nowrap',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Subscribe
            </button>
          </div>
          <p style={{
            fontSize: '13px',
            color: colors.gray,
            marginTop: '16px'
          }}>
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer id="footer" style={{
        backgroundColor: colors.dark,
        padding: '80px 5% 40px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '2fr 1fr 1fr 1fr',
          gap: '40px',
          marginBottom: '50px'
        }}>
          {/* Company Info */}
          <div>
            <h3 style={{
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              color: colors.white
            }}>
              Whamart
            </h3>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              marginBottom: '24px',
              fontSize: '15px',
              lineHeight: 1.6
            }}>
              Turn your WhatsApp Business into a powerful e-commerce platform with our innovative catalog and automated response solutions.
            </p>
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px'
            }}>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Product
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#features" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Features
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#how-it-works" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  How It Works
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/#pricing" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Company
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/about-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  About Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/blog" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Blog
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/press" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Support
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/help-center" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Help Center
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/contact-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Contact Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/terms-of-service" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div style={{
          borderTop: '1px solid rgba(255,255,255,0.1)',
          paddingTop: '30px',
          textAlign: 'center',
          color: 'rgba(255,255,255,0.5)',
          fontSize: '14px'
        }}>
          © {new Date().getFullYear()} Whamart. All rights reserved.
        </div>
      </footer>
    </div>
  );
}