import React from 'react';
import { XMarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

/**
 * DeleteConfirmationModal component for the Chat Flow Builder
 * Displays a confirmation dialog when deleting a message
 */
const DeleteConfirmationModal = ({ isOpen, onConfirm, onCancel }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" 
        onClick={onCancel}
      ></div>
      
      {/* Modal */}
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden relative z-10 transform transition-all">
        {/* Header */}
        <div className="bg-orange-50 px-6 py-4 flex items-center border-b border-orange-100">
          <ExclamationTriangleIcon className="h-6 w-6 text-orange-500 mr-3" />
          <h2 className="text-lg font-medium text-orange-700">Delete Message</h2>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4">
          <p className="text-gray-700">Are you sure you want to delete this message?</p>
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md text-sm font-medium transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md text-sm font-medium transition-colors"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
