<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Investor Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Instrument+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-green: #25D366;
            --secondary-green: #128C7E;
            --dark-green: #075E54;
            --light-green: #DCF8C6;
            --accent-blue: #007BFF;
            --accent-purple: #6F42C1;
            --accent-orange: #FD7E14;
            --accent-red: #DC3545;
            --gradient-1: linear-gradient(135deg, #25D366 0%, #128C7E 50%, #075E54 100%);
            --gradient-2: linear-gradient(135deg, #007BFF 0%, #6F42C1 100%);
            --gradient-3: linear-gradient(135deg, #FD7E14 0%, #DC3545 100%);
            --gradient-4: linear-gradient(135deg, #25D366 0%, #007BFF 100%);
            --text-white: #FFFFFF;
            --text-dark: #1a1a1a;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif;
            background: var(--gradient-1);
            overflow: hidden;
            position: relative;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        .presentation-container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Consistent slide design with full screen content */
        .slide {
            width: 100%;
            height: 100%;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 3rem;
            position: absolute;
            color: var(--text-white);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .slide.active {
            display: flex;
            opacity: 1;
            transform: translateX(0);
        }

        /* Slide-specific backgrounds */
        .slide:nth-child(odd) {
            background: var(--gradient-1);
        }

        .slide:nth-child(even) {
            background: var(--gradient-2);
        }

        .slide:nth-child(4n) {
            background: var(--gradient-3);
        }

        .slide:nth-child(4n+1) {
            background: var(--gradient-4);
        }

        /* Full screen content container */
        .slide-content {
            background: transparent;
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        /* Content wrapper for glassmorphism effect */
        .content-wrapper {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            border: 1px solid var(--glass-border);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 1400px;
            position: relative;
            overflow: hidden;
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-green), var(--accent-blue), var(--accent-purple), var(--accent-orange));
            border-radius: 2rem 2rem 0 0;
        }

        /* Typography */
        .slide h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #FFFFFF 0%, #DCF8C6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            color: var(--text-white);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-white);
        }

        .slide p {
            font-size: 1.2rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 1rem;
        }

        /* Badge/Tag styling */
        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--text-white);
        }

        /* Animations */
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        .slide-enter-right { animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .slide-enter-left { animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards; }
        .fade-in-up { animation: fadeInUp 0.8s ease-out forwards; }
        .scale-in { animation: scaleIn 0.6s ease-out forwards; }
        .bounce-in { animation: bounceIn 0.8s ease-out forwards; }

        /* Card Grid System */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
            width: 100%;
        }

        .card-grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .card-grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .card-grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }

        /* Modern Cards */
        .modern-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-green), var(--accent-blue));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-green), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-white);
        }

        .card-text {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div id="slide1" class="slide active">
            <div class="slide-content">
                <div class="content-wrapper scale-in">
                    <div class="slide-badge">Investor Presentation 2024</div>
                    <div style="margin-bottom: 2rem; text-align: center;">
                        <img src="WhaMart_Logo.png" alt="Whamart Logo" style="height: 120px; margin: 0 auto; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));">
                    </div>
                    <h1 data-key="title" style="text-align: center;">Whamart</h1>
                    <h2 data-key="subtitle" style="font-size: 1.8rem; margin-bottom: 2rem; text-align: center;">Empowering Bharat's Local Businesses Digitally</h2>
                    <p data-key="byline" style="font-size: 1.3rem; margin-bottom: 2rem; text-align: center;">Presented by Karan Solanki – Founder, Whamart</p>

                    <div style="display: flex; justify-content: space-around; gap: 2rem; margin: 2rem 0; flex-wrap: wrap;">
                        <div style="text-align: center; background: rgba(255, 255, 255, 0.1); padding: 2rem; border-radius: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2); min-width: 200px; transition: all 0.3s ease;" class="bounce-in" style="animation-delay: 0.2s;">
                            <span style="font-size: 3rem; font-weight: 800; background: linear-gradient(135deg, #FFFFFF, #DCF8C6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; display: block; margin-bottom: 0.5rem;">8Cr+</span>
                            <span style="font-size: 1rem; color: rgba(255, 255, 255, 0.8); font-weight: 500;">Target Businesses</span>
                        </div>
                        <div style="text-align: center; background: rgba(255, 255, 255, 0.1); padding: 2rem; border-radius: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2); min-width: 200px; transition: all 0.3s ease;" class="bounce-in" style="animation-delay: 0.4s;">
                            <span style="font-size: 3rem; font-weight: 800; background: linear-gradient(135deg, #FFFFFF, #DCF8C6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; display: block; margin-bottom: 0.5rem;">₹10L</span>
                            <span style="font-size: 1rem; color: rgba(255, 255, 255, 0.8); font-weight: 500;">Funding Goal</span>
                        </div>
                        <div style="text-align: center; background: rgba(255, 255, 255, 0.1); padding: 2rem; border-radius: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2); min-width: 200px; transition: all 0.3s ease;" class="bounce-in" style="animation-delay: 0.6s;">
                            <span style="font-size: 3rem; font-weight: 800; background: linear-gradient(135deg, #FFFFFF, #DCF8C6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; display: block; margin-bottom: 0.5rem;">30%+</span>
                            <span style="font-size: 1rem; color: rgba(255, 255, 255, 0.8); font-weight: 500;">Expected ROI</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Problem Statement -->
        <div id="slide2" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Problem Statement</div>
                    <h2 data-key="problemTitle" style="text-align: center;">The Problem We're Solving</h2>

                    <div class="card-grid card-grid-4">
                        <div class="modern-card fade-in-up" style="animation-delay: 0.1s;">
                            <i class="fas fa-store-slash card-icon"></i>
                            <div class="card-title">Offline Businesses</div>
                            <div class="card-text" data-key="problem1">8 Cr+ local businesses still offline in India</div>
                        </div>
                        <div class="modern-card fade-in-up" style="animation-delay: 0.2s;">
                            <i class="fas fa-puzzle-piece card-icon"></i>
                            <div class="card-title">Complex Tools</div>
                            <div class="card-text" data-key="problem2">Existing ecommerce tools are expensive or complicated</div>
                        </div>
                        <div class="modern-card fade-in-up" style="animation-delay: 0.3s;">
                            <i class="fab fa-whatsapp card-icon"></i>
                            <div class="card-title">WhatsApp API</div>
                            <div class="card-text" data-key="problem3">WhatsApp API is not affordable or beginner-friendly</div>
                        </div>
                        <div class="modern-card fade-in-up" style="animation-delay: 0.4s;">
                            <i class="fas fa-credit-card card-icon"></i>
                            <div class="card-title">Payment Setup</div>
                            <div class="card-text" data-key="problem4">Payment gateway setup is costly & takes time</div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <p style="font-size: 1.1rem; color: rgba(255, 255, 255, 0.9); text-align: center;">
                            <i class="fas fa-lightbulb" style="color: #FD7E14; margin-right: 0.5rem;"></i>
                            <strong>The Opportunity:</strong> Millions of small businesses need a simple, affordable way to go digital and reach customers through WhatsApp.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Our Solution -->
        <div id="slide3" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Our Solution</div>
                    <h2 data-key="featuresTitle" style="text-align: center;">Whamart – Simple, Powerful, Affordable</h2>

                <div class="card-grid card-grid-2">
                    <div class="modern-card fade-in-up" style="animation-delay: 0.1s;">
                        <h3 style="margin-bottom: 1.5rem; color: #25D366;">Core Features</h3>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #25D366, #007BFF); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-store"></i>
                                </div>
                                <span data-key="feature1" style="color: white; font-weight: 500;">Mini WhatsApp-style Store</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #25D366, #007BFF); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <span data-key="feature2" style="color: white; font-weight: 500;">Drag & Drop Chat Flow Builder</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #25D366, #007BFF); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <span data-key="feature3" style="color: white; font-weight: 500;">24x7 Automation</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #25D366, #007BFF); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-rupee-sign"></i>
                                </div>
                                <span data-key="feature4" style="color: white; font-weight: 500;">UPI-based Instant Payments</span>
                            </div>
                        </div>
                    </div>

                    <div class="modern-card fade-in-up" style="animation-delay: 0.2s;">
                        <h3 style="margin-bottom: 1.5rem; color: #007BFF;">Advanced Features</h3>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #007BFF, #6F42C1); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <span data-key="feature5" style="color: white; font-weight: 500;">Product & Service Catalog</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #007BFF, #6F42C1); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <span data-key="feature6" style="color: white; font-weight: 500;">Auto Bill PDF Generator</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #007BFF, #6F42C1); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <span data-key="feature7" style="color: white; font-weight: 500;">Verified Blue Tick</span>
                            </div>
                            <div style="display: flex; align-items: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.1); border-radius: 0.75rem;">
                                <div style="width: 2.5rem; height: 2.5rem; border-radius: 50%; background: linear-gradient(135deg, #007BFF, #6F42C1); display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white; font-size: 1rem;">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <span data-key="feature8" style="color: white; font-weight: 500;">No App Required</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Market & TAM -->
        <div id="slide4" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Market Analysis</div>
                    <h2 data-key="audienceTitle" style="text-align: center;">Our Target Audience & TAM</h2>

                <div style="display: flex; align-items: center; justify-content: space-between; gap: 3rem; margin: 2rem 0;">
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 1.5rem; padding: 2rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <canvas id="tamChart" style="max-height: 400px;"></canvas>
                    </div>

                    <div style="flex: 1;">
                        <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-store" style="font-size: 1.5rem; color: #25D366; margin-right: 1rem;"></i>
                                <div>
                                    <div style="font-weight: 600; color: white; margin-bottom: 0.25rem;">Kirana Stores</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">2.5 Cr+ neighborhood stores</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-user-tie" style="font-size: 1.5rem; color: #007BFF; margin-right: 1rem;"></i>
                                <div>
                                    <div style="font-weight: 600; color: white; margin-bottom: 0.25rem;">Freelancers</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">3 Cr+ service providers</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-cut" style="font-size: 1.5rem; color: #6F42C1; margin-right: 1rem;"></i>
                                <div>
                                    <div style="font-weight: 600; color: white; margin-bottom: 0.25rem;">Local Services</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">1.5 Cr+ tailors, salons, etc.</div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-tags" style="font-size: 1.5rem; color: #FD7E14; margin-right: 1rem;"></i>
                                <div>
                                    <div style="font-weight: 600; color: white; margin-bottom: 0.25rem;">Digital Sellers</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">1 Cr+ online sellers</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(37, 211, 102, 0.2); border-radius: 1rem; border: 1px solid rgba(37, 211, 102, 0.3);">
                    <p data-key="focusParagraph" style="font-size: 1.1rem; color: white; text-align: center;">
                        <i class="fas fa-map-marker-alt" style="color: #25D366; margin-right: 0.5rem;"></i>
                        Our early focus is on Surat, Tier 2/3 cities where WhatsApp is already the most used business tool.
                    </p>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Competition Analysis -->
        <div id="slide5" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Competitive Analysis</div>
                    <h2 data-key="competitionTitle" style="text-align: center;">How We're Different from the Competition</h2>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 1rem; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.2);">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: rgba(255, 255, 255, 0.2);">
                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: white; border-bottom: 1px solid rgba(255, 255, 255, 0.1);" data-key="competitor">Competitor</th>
                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: white; border-bottom: 1px solid rgba(255, 255, 255, 0.1);" data-key="strength">Strength</th>
                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: white; border-bottom: 1px solid rgba(255, 255, 255, 0.1);" data-key="weakness">Weakness</th>
                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: white; border-bottom: 1px solid rgba(255, 255, 255, 0.1);" data-key="advantage">Whamart Advantage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;" data-key="comp1">Shopify</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9);" data-key="str1">Robust platform</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.7);" data-key="weak1">High cost, complex setup</td>
                                <td style="padding: 1rem; color: #25D366; font-weight: 500;" data-key="adv1">Simple chat-based mini-store</td>
                            </tr>
                            <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;" data-key="comp2">Swiggy Mini</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9);" data-key="str2">Delivery focus</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.7);" data-key="weak2">Only food category</td>
                                <td style="padding: 1rem; color: #25D366; font-weight: 500;" data-key="adv2">Multi-category support</td>
                            </tr>
                            <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;" data-key="comp3">Dukaan</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9);" data-key="str3">Easy setup</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.7);" data-key="weak3">Needs payment gateway</td>
                                <td style="padding: 1rem; color: #25D366; font-weight: 500;" data-key="adv3">Zero-fee UPI integration</td>
                            </tr>
                            <tr style="border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9); font-weight: 500;" data-key="comp4">WhatsApp API</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.9);" data-key="str4">Official platform</td>
                                <td style="padding: 1rem; color: rgba(255, 255, 255, 0.7);" data-key="weak4">Expensive + KYC required</td>
                                <td style="padding: 1rem; color: #25D366; font-weight: 500;" data-key="adv4">Instant setup, no KYC</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 2rem; display: flex; justify-content: center; gap: 2rem;">
                    <div style="text-align: center; background: rgba(37, 211, 102, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(37, 211, 102, 0.3);">
                        <div style="font-size: 2rem; font-weight: 800; color: #25D366; margin-bottom: 0.5rem;">₹0</div>
                        <div style="color: white; font-weight: 500;">Setup Cost</div>
                    </div>
                    <div style="text-align: center; background: rgba(7, 123, 255, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(7, 123, 255, 0.3);">
                        <div style="font-size: 2rem; font-weight: 800; color: #007BFF; margin-bottom: 0.5rem;">5 Min</div>
                        <div style="color: white; font-weight: 500;">Go Live Time</div>
                    </div>
                    <div style="text-align: center; background: rgba(111, 66, 193, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(111, 66, 193, 0.3);">
                        <div style="font-size: 2rem; font-weight: 800; color: #6F42C1; margin-bottom: 0.5rem;">0%</div>
                        <div style="color: white; font-weight: 500;">Commission</div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Business Model -->
        <div id="slide6" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Revenue Model</div>
                    <h2 data-key="businessModelTitle" style="text-align: center;">How We Make Money</h2>

                <div class="card-grid card-grid-3">
                    <div class="modern-card bounce-in" style="animation-delay: 0.1s;">
                        <i class="fas fa-calendar-alt card-icon"></i>
                        <div class="card-title" data-key="model1_title">Annual Subscription</div>
                        <div style="font-size: 2rem; font-weight: 800; color: #25D366; margin: 1rem 0;">₹1,500 - ₹3,000</div>
                        <div class="card-text">Simple yearly plans based on features needed</div>
                    </div>

                    <div class="modern-card bounce-in" style="animation-delay: 0.2s;">
                        <i class="fas fa-plus-circle card-icon"></i>
                        <div class="card-title" data-key="model2_title">Add-on Services</div>
                        <div style="font-size: 2rem; font-weight: 800; color: #007BFF; margin: 1rem 0;">Phase 2</div>
                        <div class="card-text" data-key="model2_desc">AI Chatbot, Advanced CRM, Analytics</div>
                    </div>

                    <div class="modern-card bounce-in" style="animation-delay: 0.3s;">
                        <i class="fas fa-handshake card-icon"></i>
                        <div class="card-title" data-key="model3_title">Zero Commission</div>
                        <div style="font-size: 2rem; font-weight: 800; color: #6F42C1; margin: 1rem 0;">100%</div>
                        <div class="card-text" data-key="model3_desc">Vendors keep all their profits</div>
                    </div>
                </div>

                <div style="margin-top: 2rem; display: flex; align-items: center; gap: 3rem;">
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 1.5rem; padding: 2rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <canvas id="revenueChart" style="max-height: 300px;"></canvas>
                    </div>

                    <div style="flex: 1;">
                        <div style="background: rgba(37, 211, 102, 0.2); padding: 2rem; border-radius: 1.5rem; border: 1px solid rgba(37, 211, 102, 0.3); text-align: center;">
                            <h3 style="color: #25D366; margin-bottom: 1rem;">Revenue Projection</h3>
                            <div style="font-size: 3rem; font-weight: 800; color: white; margin-bottom: 0.5rem;">₹30L</div>
                            <div style="color: rgba(255, 255, 255, 0.9); margin-bottom: 1rem;">in 6 months</div>
                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">From 2000+ active users</div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Investment & Capital Use -->
        <div id="slide7" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Capital Allocation</div>
                    <h2 data-key="capitalTitle" style="text-align: center;">How We'll Use ₹10 Lakh Capital</h2>

                <div style="display: flex; align-items: center; gap: 3rem; margin: 2rem 0;">
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 1.5rem; padding: 2rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <canvas id="capitalChart" style="max-height: 400px;"></canvas>
                    </div>

                    <div style="flex: 1;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(253, 126, 20, 0.2); border-radius: 1rem; border: 1px solid rgba(253, 126, 20, 0.3);">
                                <div style="width: 3rem; height: 3rem; border-radius: 50%; background: #FD7E14; display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white;">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 600; color: white;">Marketing & Ads</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">₹4L - Meta, YouTube, Influencers</div>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(7, 123, 255, 0.2); border-radius: 1rem; border: 1px solid rgba(7, 123, 255, 0.3);">
                                <div style="width: 3rem; height: 3rem; border-radius: 50%; background: #007BFF; display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white;">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 600; color: white;">Tech Infrastructure</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">₹2L - Servers, Tools, Security</div>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(37, 211, 102, 0.2); border-radius: 1rem; border: 1px solid rgba(37, 211, 102, 0.3);">
                                <div style="width: 3rem; height: 3rem; border-radius: 50%; background: #25D366; display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white;">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 600; color: white;">Team & Operations</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">₹3L - Salaries, Office</div>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; padding: 1rem; background: rgba(111, 66, 193, 0.2); border-radius: 1rem; border: 1px solid rgba(111, 66, 193, 0.3);">
                                <div style="width: 3rem; height: 3rem; border-radius: 50%; background: #6F42C1; display: flex; align-items: center; justify-content: center; margin-right: 1rem; color: white;">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div>
                                    <div style="font-weight: 600; color: white;">Legal & Compliance</div>
                                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">₹1L - Legal, Licenses</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 2rem; background: linear-gradient(135deg, #25D366, #007BFF); padding: 2rem; border-radius: 1.5rem; text-align: center;">
                    <i class="fas fa-rocket" style="font-size: 2rem; color: white; margin-bottom: 1rem;"></i>
                    <h3 style="color: white; margin-bottom: 0.5rem;" data-key="launchHighlight">Launch in 1 week after funding!</h3>
                    <p style="color: rgba(255, 255, 255, 0.9); margin: 0;">Product is ready, team is prepared, go-to-market strategy is finalized</p>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Timeline & Readiness -->
        <div id="slide8" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Product Readiness</div>
                    <h2 data-key="timelineTitle" style="text-align: center;">Product is Ready to Launch</h2>

                <div style="position: relative; margin: 2rem 0;">
                    <div style="position: absolute; top: 0; left: 50%; transform: translateX(-50%); width: 4px; height: 100%; background: linear-gradient(180deg, #25D366, #007BFF); border-radius: 2px;"></div>

                    <div style="display: flex; flex-direction: column; gap: 2rem;">
                        <div style="display: flex; align-items: center; position: relative;">
                            <div style="width: 4rem; height: 4rem; border-radius: 50%; background: linear-gradient(135deg, #25D366, #007BFF); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; position: absolute; left: 50%; transform: translateX(-50%); z-index: 2; border: 4px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); width: 45%; margin-left: auto;">
                                <h4 style="color: #25D366; margin-bottom: 0.5rem;">MVP Ready ✅</h4>
                                <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem;">Complete product built and tested</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; position: relative; flex-direction: row-reverse;">
                            <div style="width: 4rem; height: 4rem; border-radius: 50%; background: linear-gradient(135deg, #007BFF, #6F42C1); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; position: absolute; left: 50%; transform: translateX(-50%); z-index: 2; border: 4px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); width: 45%; margin-right: auto;">
                                <h4 style="color: #007BFF; margin-bottom: 0.5rem;">7-day Trial Launched ✅</h4>
                                <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem;">Free trial system is live and working</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; position: relative;">
                            <div style="width: 4rem; height: 4rem; border-radius: 50%; background: linear-gradient(135deg, #6F42C1, #FD7E14); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; position: absolute; left: 50%; transform: translateX(-50%); z-index: 2; border: 4px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); width: 45%; margin-left: auto;">
                                <h4 style="color: #6F42C1; margin-bottom: 0.5rem;">Marketing Strategy Ready ✅</h4>
                                <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem;">Influencer list, ad campaigns prepared</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; position: relative; flex-direction: row-reverse;">
                            <div style="width: 4rem; height: 4rem; border-radius: 50%; background: linear-gradient(135deg, #FD7E14, #DC3545); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; position: absolute; left: 50%; transform: translateX(-50%); z-index: 2; border: 4px solid rgba(255, 255, 255, 0.2);">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div style="background: rgba(253, 126, 20, 0.2); padding: 1.5rem; border-radius: 1rem; border: 2px solid rgba(253, 126, 20, 0.4); width: 45%; margin-right: auto;">
                                <h4 style="color: #FD7E14; margin-bottom: 0.5rem;">Launch in 1 Week! 🚀</h4>
                                <p style="color: rgba(255, 255, 255, 0.9); margin: 0; font-size: 0.9rem; font-weight: 500;">Ready to go live immediately after funding</p>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Investment Offer -->
        <div id="slide9" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Investment Opportunity</div>
                    <h2 data-key="investmentTitle" style="text-align: center;">Investment Offer & Returns</h2>

                <div style="display: flex; align-items: center; gap: 3rem; margin: 2rem 0;">
                    <div style="flex: 1;">
                        <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                            <div style="background: rgba(37, 211, 102, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(37, 211, 102, 0.3);">
                                <h4 style="color: #25D366; margin-bottom: 0.5rem;">💰 Capital Raise</h4>
                                <p style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">₹10 lakh (₹5 lakh minimum entry)</p>
                            </div>

                            <div style="background: rgba(7, 123, 255, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(7, 123, 255, 0.3);">
                                <h4 style="color: #007BFF; margin-bottom: 0.5rem;">🔒 Lock-in Period</h4>
                                <p style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">3 months with monthly progress reports</p>
                            </div>

                            <div style="background: rgba(111, 66, 193, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(111, 66, 193, 0.3);">
                                <h4 style="color: #6F42C1; margin-bottom: 0.5rem;">📈 Expected ROI</h4>
                                <p style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">30%+ returns in 6 months</p>
                            </div>

                            <div style="background: rgba(253, 126, 20, 0.2); padding: 1.5rem; border-radius: 1rem; border: 1px solid rgba(253, 126, 20, 0.3);">
                                <h4 style="color: #FD7E14; margin-bottom: 0.5rem;">🎯 Revenue Target</h4>
                                <p style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">₹30L revenue in 6 months</p>
                            </div>
                        </div>
                    </div>

                    <div style="flex: 1; background: rgba(255, 255, 255, 0.1); border-radius: 1.5rem; padding: 2rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <canvas id="roiChart" style="max-height: 400px;"></canvas>
                    </div>
                </div>

                <div style="margin-top: 2rem; background: linear-gradient(135deg, #25D366, #007BFF); padding: 2rem; border-radius: 1.5rem; text-align: center;">
                    <h3 style="color: white; margin-bottom: 1rem;">Why Invest in Whamart?</h3>
                    <div style="display: flex; justify-content: space-around; gap: 2rem; flex-wrap: wrap;">
                        <div style="text-align: center;">
                            <i class="fas fa-users" style="font-size: 2rem; color: white; margin-bottom: 0.5rem;"></i>
                            <div style="color: white; font-weight: 500; font-size: 0.9rem;">8 Cr+ Target Market</div>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-rocket" style="font-size: 2rem; color: white; margin-bottom: 0.5rem;"></i>
                            <div style="color: white; font-weight: 500; font-size: 0.9rem;">Ready to Launch</div>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-chart-line" style="font-size: 2rem; color: white; margin-bottom: 0.5rem;"></i>
                            <div style="color: white; font-weight: 500; font-size: 0.9rem;">High ROI Potential</div>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-handshake" style="font-size: 2rem; color: white; margin-bottom: 0.5rem;"></i>
                            <div style="color: white; font-weight: 500; font-size: 0.9rem;">Founder-led Team</div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: Final Call to Action -->
        <div id="slide10" class="slide">
            <div class="slide-content">
                <div class="content-wrapper">
                    <div class="slide-badge">Let's Partner</div>
                    <h2 data-key="finalTitle" style="text-align: center;">Let's Build Bharat's Future Together</h2>

                <div style="text-align: center; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, #25D366, #007BFF); padding: 3rem; border-radius: 2rem; margin-bottom: 2rem;">
                        <h3 style="color: white; font-size: 2.5rem; margin-bottom: 1rem;">₹10 Lakh Investment</h3>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 1.2rem; margin-bottom: 1.5rem;">For 6 months to revolutionize local business digitization</p>
                        <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
                            <div style="background: rgba(255, 255, 255, 0.2); padding: 1rem; border-radius: 1rem;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: white;">30%+ ROI</div>
                                <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Expected Returns</div>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.2); padding: 1rem; border-radius: 1rem;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: white;">₹30L</div>
                                <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Revenue Target</div>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.2); padding: 1rem; border-radius: 1rem;">
                                <div style="font-size: 1.5rem; font-weight: 700; color: white;">1 Week</div>
                                <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">Launch Time</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: rgba(255, 255, 255, 0.1); padding: 2rem; border-radius: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                        <h3 style="color: white; margin-bottom: 1.5rem;">Contact Information</h3>
                        <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-phone" style="color: #25D366; font-size: 1.2rem;"></i>
                                <span style="color: white; font-size: 1.1rem;">+91 99999 99999</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fab fa-whatsapp" style="color: #25D366; font-size: 1.2rem;"></i>
                                <span style="color: white; font-size: 1.1rem;">+91 99999 99999</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-envelope" style="color: #25D366; font-size: 1.2rem;"></i>
                                <span style="color: white; font-size: 1.1rem;"><EMAIL></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <h1 style="font-size: 3rem; font-weight: 800; background: linear-gradient(135deg, #25D366, #007BFF); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Thank You!</h1>
                    <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem; margin-top: 1rem;">Ready to answer your questions</p>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div style="position: fixed; bottom: 2rem; left: 50%; transform: translateX(-50%); display: flex; align-items: center; gap: 1rem; z-index: 1000; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 1rem; border-radius: 50px; border: 1px solid rgba(255, 255, 255, 0.2);">
        <button id="prev-btn" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.3); color: white; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; font-size: 1.2rem;">
            <i class="fas fa-chevron-left"></i>
        </button>

        <select id="language-select" style="background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 25px; padding: 8px 16px; color: white; font-size: 14px; cursor: pointer; outline: none;">
            <option value="en" style="background: #333; color: white;">English</option>
            <option value="hi" style="background: #333; color: white;">Hindi</option>
            <option value="gu" style="background: #333; color: white;">Gujarati</option>
        </select>

        <button id="next-btn" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.3); color: white; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; font-size: 1.2rem;">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- Progress Indicator -->
    <div id="progress-indicator" style="position: fixed; bottom: 2rem; right: 2rem; display: flex; flex-direction: column; gap: 8px; z-index: 1000;"></div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        let isTransitioning = false;

        // Translation data
        const translations = {
            en: {
                title: 'Whamart',
                subtitle: 'Empowering Bharat\'s Local Businesses Digitally',
                byline: 'Presented by Karan Solanki – Founder, Whamart',
                problemTitle: 'The Problem We\'re Solving',
                problem1: '8 Cr+ local businesses still offline in India',
                problem2: 'Existing ecommerce tools are expensive or complicated',
                problem3: 'WhatsApp API is not affordable or beginner-friendly',
                problem4: 'Payment gateway setup is costly & takes time',
                featuresTitle: 'Whamart – Simple, Powerful, Affordable',
                feature1: 'Mini WhatsApp-style Store',
                feature2: 'Drag & Drop Chat Flow Builder',
                feature3: '24x7 Automation',
                feature4: 'UPI-based Instant Payments',
                feature5: 'Product & Service Catalog',
                feature6: 'Auto Bill PDF Generator',
                feature7: 'Verified Blue Tick',
                feature8: 'No App Required',
                audienceTitle: 'Our Target Audience & TAM',
                focusParagraph: 'Our early focus is on Surat, Tier 2/3 cities where WhatsApp is already the most used business tool.',
                competitionTitle: 'How We\'re Different from the Competition',
                competitor: 'Competitor',
                strength: 'Strength',
                weakness: 'Weakness',
                advantage: 'Whamart Advantage',
                comp1: 'Shopify', str1: 'Robust platform', weak1: 'High cost, complex setup', adv1: 'Simple chat-based mini-store',
                comp2: 'Swiggy Mini', str2: 'Delivery focus', weak2: 'Only food category', adv2: 'Multi-category support',
                comp3: 'Dukaan', str3: 'Easy setup', weak3: 'Needs payment gateway', adv3: 'Zero-fee UPI integration',
                comp4: 'WhatsApp API', str4: 'Official platform', weak4: 'Expensive + KYC required', adv4: 'Instant setup, no KYC',
                businessModelTitle: 'How We Make Money',
                model1_title: 'Annual Subscription',
                model2_title: 'Add-on Services',
                model2_desc: 'AI Chatbot, Advanced CRM, Analytics',
                model3_title: 'Zero Commission',
                model3_desc: 'Vendors keep all their profits',
                capitalTitle: 'How We\'ll Use ₹10 Lakh Capital',
                launchHighlight: 'Launch in 1 week after funding!',
                timelineTitle: 'Product is Ready to Launch',
                investmentTitle: 'Investment Offer & Returns',
                finalTitle: 'Let\'s Build Bharat\'s Future Together'
            },
            hi: {
                title: 'व्हामार्ट',
                subtitle: 'भारत के स्थानीय व्यवसायों को डिजिटल रूप से सशक्त बनाना',
                byline: 'करण सोलंकी द्वारा प्रस्तुत - संस्थापक, व्हामार्ट',
                problemTitle: 'हम जिस समस्या का समाधान कर रहे हैं',
                problem1: 'भारत में 8 करोड़+ स्थानीय व्यवसाय अभी भी ऑफ़लाइन हैं',
                problem2: 'मौजूदा ई-कॉमर्स उपकरण महंगे या जटिल हैं',
                problem3: 'व्हाट्सएप एपीआई सस्ती या शुरुआती-अनुकूल नहीं है',
                problem4: 'पेमेंट गेटवे सेटअप महंगा है और इसमें समय लगता है',
                featuresTitle: 'व्हामार्ट - सरल, शक्तिशाली, किफायती',
                feature1: 'मिनी व्हाट्सएप-स्टाइल स्टोर',
                feature2: 'ड्रैग एंड ड्रॉप चैट फ्लो बिल्डर',
                feature3: '24x7 ऑटोमेशन',
                feature4: 'UPI-आधारित तत्काल भुगतान',
                feature5: 'उत्पाद और सेवा कैटलॉग',
                feature6: 'ऑटो बिल पीडीएफ जेनरेटर',
                feature7: 'सत्यापित ब्लू टिक',
                feature8: 'किसी ऐप की आवश्यकता नहीं'
            },
            gu: {
                title: 'વ્હામર્ટ',
                subtitle: 'ભારતના સ્થાનિક વ્યવસાયોને ડિજિટલી રીતે સશક્ત બનાવવું',
                byline: 'કરણ સોલંકી દ્વારા પ્રસ્તુત - સ્થાપક, વ્હામર્ટ',
                problemTitle: 'અમે જે સમસ્યા હલ કરી રહ્યા છીએ',
                problem1: 'ભારતમાં 8 કરોડ+ સ્થાનિક વ્યવસાયો હજુ પણ ઑફલાઇન છે',
                problem2: 'હાલના ઈકોમર્સ સાધનો ખર્ચાળ અથવા જટિલ છે',
                problem3: 'વોટ્સએપ એપીઆઈ સસ્તું કે શિખાઉ માણસ માટે અનુકૂળ નથી',
                problem4: 'પેમેન્ટ ગેટવે સેટઅપ ખર્ચાળ છે અને સમય લે છે'
            }
        };

        // Show slide function with smooth transitions
        function showSlide(index, direction = 'next') {
            if (isTransitioning || index < 0 || index >= totalSlides) return;
            isTransitioning = true;

            const currentSlideEl = slides[currentSlide];
            const nextSlideEl = slides[index];

            // Remove active class from current slide
            if (currentSlideEl) {
                currentSlideEl.classList.remove('active');
                currentSlideEl.style.transform = direction === 'next' ? 'translateX(-100%)' : 'translateX(100%)';
            }

            // Add active class to next slide with animation
            if (nextSlideEl) {
                nextSlideEl.style.display = 'flex';
                nextSlideEl.style.transform = direction === 'next' ? 'translateX(100%)' : 'translateX(-100%)';

                setTimeout(() => {
                    nextSlideEl.classList.add('active');
                    nextSlideEl.style.transform = 'translateX(0)';
                }, 50);
            }

            // Clean up after animation
            setTimeout(() => {
                slides.forEach((slide, i) => {
                    if (i !== index) {
                        slide.style.display = 'none';
                        slide.classList.remove('active');
                    }
                });
                isTransitioning = false;
                updateProgressIndicator();
                renderCharts();
            }, 800);

            // Update navigation buttons
            document.getElementById('prev-btn').style.opacity = index === 0 ? '0.5' : '1';
            document.getElementById('next-btn').style.opacity = index === totalSlides - 1 ? '0.5' : '1';
        }

        // Create progress indicator
        function createProgressIndicator() {
            const progressContainer = document.getElementById('progress-indicator');
            progressContainer.innerHTML = '';

            for (let i = 0; i < totalSlides; i++) {
                const dot = document.createElement('div');
                dot.style.cssText = `
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.3);
                    cursor: pointer;
                    transition: all 0.3s ease;
                `;
                if (i === 0) {
                    dot.style.background = '#25D366';
                    dot.style.transform = 'scale(1.3)';
                    dot.style.boxShadow = '0 0 10px rgba(37, 211, 102, 0.6)';
                }

                dot.addEventListener('click', () => {
                    if (i !== currentSlide && !isTransitioning) {
                        const direction = i > currentSlide ? 'next' : 'prev';
                        currentSlide = i;
                        showSlide(currentSlide, direction);
                    }
                });

                progressContainer.appendChild(dot);
            }
        }

        function updateProgressIndicator() {
            const dots = document.querySelectorAll('#progress-indicator > div');
            dots.forEach((dot, i) => {
                if (i === currentSlide) {
                    dot.style.background = '#25D366';
                    dot.style.transform = 'scale(1.3)';
                    dot.style.boxShadow = '0 0 10px rgba(37, 211, 102, 0.6)';
                } else {
                    dot.style.background = 'rgba(255, 255, 255, 0.3)';
                    dot.style.transform = 'scale(1)';
                    dot.style.boxShadow = 'none';
                }
            });
        }

        // Language switching
        function changeLanguage(lang) {
            const elements = document.querySelectorAll('[data-key]');
            elements.forEach(element => {
                const key = element.getAttribute('data-key');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
        }

        // Chart rendering
        function renderCharts() {
            const activeSlideId = slides[currentSlide] ? slides[currentSlide].id : null;

            switch (activeSlideId) {
                case 'slide4':
                    renderTAMChart();
                    break;
                case 'slide6':
                    renderRevenueChart();
                    break;
                case 'slide7':
                    renderCapitalChart();
                    break;
                case 'slide9':
                    renderROIChart();
                    break;
            }
        }

        function renderTAMChart() {
            const ctx = document.getElementById('tamChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Kirana Stores', 'Freelancers', 'Local Services', 'Digital Sellers'],
                    datasets: [{
                        data: [2.5, 3, 1.5, 1],
                        backgroundColor: ['#25D366', '#007BFF', '#6F42C1', '#FD7E14'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function renderRevenueChart() {
            const ctx = document.getElementById('revenueChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Month 1', 'Month 2', 'Month 3', 'Month 4', 'Month 5', 'Month 6'],
                    datasets: [{
                        label: 'Revenue (₹ Lakhs)',
                        data: [2, 5, 8, 15, 22, 30],
                        borderColor: '#25D366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: 'white'
                            }
                        },
                        y: {
                            ticks: {
                                color: 'white'
                            }
                        }
                    }
                }
            });
        }

        function renderCapitalChart() {
            const ctx = document.getElementById('capitalChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Marketing & Ads', 'Tech Infrastructure', 'Team & Operations', 'Legal & Compliance'],
                    datasets: [{
                        data: [4, 2, 3, 1],
                        backgroundColor: ['#FD7E14', '#007BFF', '#25D366', '#6F42C1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function renderROIChart() {
            const ctx = document.getElementById('roiChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Investment', '3 Months', '6 Months'],
                    datasets: [{
                        label: 'Amount (₹ Lakhs)',
                        data: [10, 12, 13],
                        backgroundColor: ['#DC3545', '#FD7E14', '#25D366'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: 'white'
                            }
                        },
                        y: {
                            ticks: {
                                color: 'white'
                            }
                        }
                    }
                }
            });
        }

        // Event listeners
        document.getElementById('next-btn').addEventListener('click', () => {
            if (currentSlide < totalSlides - 1 && !isTransitioning) {
                currentSlide++;
                showSlide(currentSlide, 'next');
            }
        });

        document.getElementById('prev-btn').addEventListener('click', () => {
            if (currentSlide > 0 && !isTransitioning) {
                currentSlide--;
                showSlide(currentSlide, 'prev');
            }
        });

        document.getElementById('language-select').addEventListener('change', (e) => {
            changeLanguage(e.target.value);
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                if (currentSlide < totalSlides - 1 && !isTransitioning) {
                    currentSlide++;
                    showSlide(currentSlide, 'next');
                }
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                if (currentSlide > 0 && !isTransitioning) {
                    currentSlide--;
                    showSlide(currentSlide, 'prev');
                }
            }
        });

        // Button hover effects
        document.querySelectorAll('.nav-btn, #prev-btn, #next-btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.3)';
                btn.style.transform = 'scale(1.1)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.2)';
                btn.style.transform = 'scale(1)';
            });
        });

        // Initialize presentation
        document.addEventListener('DOMContentLoaded', () => {
            createProgressIndicator();
            changeLanguage('en');

            // Set initial button states
            document.getElementById('prev-btn').style.opacity = '0.5';

            // Add some delay before rendering charts to ensure DOM is ready
            setTimeout(() => {
                renderCharts();
            }, 500);
        });
    </script>
</body>
</html>
