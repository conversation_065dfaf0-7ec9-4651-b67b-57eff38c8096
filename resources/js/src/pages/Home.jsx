import { Link, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useSubscription } from '../contexts/SubscriptionContext';

export default function Home() {
  const navigate = useNavigate();
  const { selectPlan } = useSubscription();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modern color palette - brighter greens
  const colors = {
    primary: '#25D366',    // Bright WhatsApp green - 60%
    secondary: '#E9F7EF',  // Light green background - 30%
    accent: '#075E54',     // Dark green accent - 10%
    white: '#FFFFFF',
    lightGray: '#F8F9FA',
    gray: '#6C757D',
    dark: '#212529'
  };

  return (
    <div style={{
      fontFamily: "'Poppins', 'Segoe UI', sans-serif",
      margin: 0,
      padding: 0,
      color: colors.dark,
      backgroundColor: colors.white
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: colors.white,
        color: colors.dark,
        padding: '15px 5%',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: isMobile ? '15px' : '0'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src="/WhaMart_Logo.png"
            alt="WhaMart Logo"
            style={{
              height: '40px',
              marginRight: '10px'
            }}
          />
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '30px'
        }}>
          {!isMobile && (
            <nav>
              <ul style={{
                display: 'flex',
                listStyle: 'none',
                gap: '30px',
                margin: 0,
                padding: 0
              }}>
                <li><a href="#features" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Features</a></li>
                <li><a href="#how-it-works" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>How It Works</a></li>
                <li><a href="#pricing" style={{ color: colors.dark, textDecoration: 'none', fontWeight: 500, fontSize: '16px' }}>Pricing</a></li>
              </ul>
            </nav>
          )}

          <div style={{ display: 'flex', gap: '15px' }}>
            <Link to="/login" style={{
              padding: '10px 20px',
              backgroundColor: 'transparent',
              color: colors.primary,
              border: `1px solid ${colors.primary}`,
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              transition: 'all 0.3s ease'
            }}>
              Sign In
            </Link>
            <Link to="/register" style={{
              padding: '10px 20px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '15px',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section style={{
        backgroundColor: colors.secondary,
        padding: '80px 5%',
        marginBottom: '0',
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        alignItems: 'center',
        gap: '60px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '10%',
          right: '5%',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          flex: 1,
          position: 'relative',
          zIndex: 1,
          padding: isMobile ? '0' : '0 20px 0 0'
        }}>
          <div style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: 'rgba(37, 211, 102, 0.1)',
            color: colors.primary,
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: 600,
            marginBottom: '20px'
          }}>
            WhatsApp-Style E-commerce Platform
          </div>
          <h2 style={{
            fontSize: isMobile ? '36px' : '48px',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '24px',
            color: colors.dark
          }}>
            Create Your WhatsApp Store in <span style={{ color: colors.primary }}>Minutes</span>
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            marginBottom: '32px',
            maxWidth: '540px'
          }}>
            Connect with customers through automated chat flows and boost your sales with our WhatsApp-style e-commerce platform. Perfect for small shop owners looking to expand their online presence.
          </p>
          <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            <Link to="/register" style={{
              padding: '14px 32px',
              backgroundColor: colors.primary,
              color: colors.white,
              border: 'none',
              borderRadius: '50px',
              textDecoration: 'none',
              fontWeight: 600,
              fontSize: '16px',
              display: 'inline-block',
              boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
              transition: 'all 0.3s ease'
            }}>
              Get Started Free
            </Link>
            <a href="#how-it-works" style={{
              display: 'flex',
              alignItems: 'center',
              color: colors.dark,
              textDecoration: 'none',
              fontWeight: 500,
              fontSize: '16px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: 'rgba(37, 211, 102, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                  <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
                </svg>
              </div>
              See how it works
            </a>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginTop: '48px',
            gap: '24px'
          }}>
            <div style={{ display: 'flex' }}>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: '#E1E8ED',
                border: `2px solid ${colors.white}`,
                backgroundImage: 'url(https://randomuser.me/api/portraits/women/44.jpg)',
                backgroundSize: 'cover',
                marginRight: '-10px'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: '#E1E8ED',
                border: `2px solid ${colors.white}`,
                backgroundImage: 'url(https://randomuser.me/api/portraits/men/32.jpg)',
                backgroundSize: 'cover',
                marginRight: '-10px'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: '#E1E8ED',
                border: `2px solid ${colors.white}`,
                backgroundImage: 'url(https://randomuser.me/api/portraits/women/68.jpg)',
                backgroundSize: 'cover',
                marginRight: '-10px'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: colors.primary,
                border: `2px solid ${colors.white}`,
                color: colors.white,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                fontWeight: 600
              }}>+2k</div>
            </div>
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                <div style={{ display: 'flex', marginRight: '8px' }}>
                  {[1, 2, 3, 4, 5].map((_, index) => (
                    <svg key={index} xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  ))}
                </div>
                <span style={{ fontWeight: 600 }}>4.9/5</span>
              </div>
              <div style={{ fontSize: '14px', color: colors.gray }}>from over 2,000 reviews</div>
            </div>
          </div>
        </div>

        <div style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            width: '320px',
            height: '640px',
            backgroundColor: colors.white,
            borderRadius: '40px',
            boxShadow: '0 30px 60px rgba(0, 0, 0, 0.12)',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative'
          }}>
            {/* Phone notch */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '150px',
              height: '30px',
              backgroundColor: colors.dark,
              borderBottomLeftRadius: '14px',
              borderBottomRightRadius: '14px',
              zIndex: 10
            }}></div>

            {/* Phone header */}
            <div style={{
              backgroundColor: colors.primary,
              color: colors.white,
              padding: '16px',
              paddingTop: '45px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: '50%',
                  marginRight: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zM8 1.918l-.797.161A4.002 4.002 0 0 0 4 6c0 .628-.134 2.197-.459 3.742-.16.767-.376 1.566-.663 2.258h10.244c-.287-.692-.502-1.49-.663-2.258C12.134 8.197 12 6.628 12 6a4.002 4.002 0 0 0-3.203-3.92L8 1.917zM14.22 12c.223.447.481.801.78 1H1c.299-.199.557-.553.78-1C2.68 10.2 3 6.88 3 6c0-2.42 1.72-4.44 4.005-4.901a1 1 0 1 1 1.99 0A5.002 5.002 0 0 1 13 6c0 .88.32 4.2 1.22 6z"/>
                  </svg>
                </div>
                <div>
                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>Whamart Store</div>
                  <div style={{ fontSize: '13px', opacity: 0.8 }}>Online</div>
                </div>
              </div>
            </div>

            {/* Chat area */}
            <div style={{
              flex: 1,
              backgroundColor: '#F8F9FA',
              backgroundImage: 'url("https://web.whatsapp.com/img/bg-chat-tile-light_04fcacde539c58cca6745483d4858c52.png")',
              backgroundRepeat: 'repeat',
              padding: '16px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '16px'
            }}>
              {/* Bot message */}
              <div style={{ alignSelf: 'flex-start', maxWidth: '80%' }}>
                <div style={{
                  backgroundColor: colors.white,
                  padding: '12px 16px',
                  borderRadius: '12px',
                  borderTopLeftRadius: '4px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <p style={{ margin: 0, fontSize: '14px', lineHeight: 1.5 }}>👋 Welcome to Whamart Store! How can I help you today?</p>
                  <p style={{ margin: 0, fontSize: '11px', color: colors.gray, textAlign: 'right', marginTop: '4px' }}>10:30 AM</p>
                </div>
              </div>

              {/* User message */}
              <div style={{ alignSelf: 'flex-end', maxWidth: '80%' }}>
                <div style={{
                  backgroundColor: '#DCF8C6',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  borderTopRightRadius: '4px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <p style={{ margin: 0, fontSize: '14px', lineHeight: 1.5 }}>I'd like to see your latest products</p>
                  <p style={{ margin: 0, fontSize: '11px', color: colors.gray, textAlign: 'right', marginTop: '4px' }}>10:31 AM</p>
                </div>
              </div>

              {/* Bot message with products */}
              <div style={{ alignSelf: 'flex-start', maxWidth: '80%' }}>
                <div style={{
                  backgroundColor: colors.white,
                  padding: '12px 16px',
                  borderRadius: '12px',
                  borderTopLeftRadius: '4px',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}>
                  <p style={{ margin: 0, fontSize: '14px', lineHeight: 1.5, marginBottom: '12px' }}>Here are our latest products:</p>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                    <div style={{
                      backgroundColor: colors.secondary,
                      padding: '8px',
                      borderRadius: '8px',
                      textAlign: 'center',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>Product 1</div>
                    <div style={{
                      backgroundColor: colors.secondary,
                      padding: '8px',
                      borderRadius: '8px',
                      textAlign: 'center',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>Product 2</div>
                    <div style={{
                      backgroundColor: colors.secondary,
                      padding: '8px',
                      borderRadius: '8px',
                      textAlign: 'center',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>Product 3</div>
                    <div style={{
                      backgroundColor: colors.secondary,
                      padding: '8px',
                      borderRadius: '8px',
                      textAlign: 'center',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>Product 4</div>
                  </div>
                  <p style={{ margin: 0, fontSize: '11px', color: colors.gray, textAlign: 'right', marginTop: '8px' }}>10:32 AM</p>
                </div>
              </div>
            </div>

            {/* Input area */}
            <div style={{ backgroundColor: colors.white, padding: '16px', borderTop: '1px solid rgba(0,0,0,0.05)' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: colors.lightGray,
                borderRadius: '24px',
                padding: '8px 16px'
              }}>
                <input
                  type="text"
                  placeholder="Type a message"
                  style={{
                    flex: 1,
                    border: 'none',
                    outline: 'none',
                    padding: '8px 0',
                    backgroundColor: 'transparent',
                    fontSize: '14px'
                  }}
                />
                <button style={{
                  backgroundColor: colors.primary,
                  color: colors.white,
                  border: 'none',
                  width: '36px',
                  height: '36px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M15.964.686a.5.5 0 0 0-.65-.65L.767 5.855H.766l-.452.18a.5.5 0 0 0-.082.887l.41.26.001.002 4.995 3.178 3.178 4.995.002.002.26.41a.5.5 0 0 0 .886-.083l6-15Zm-1.833 1.89L6.637 10.07l-.215-.338a.5.5 0 0 0-.154-.154l-.338-.215 7.494-7.494 1.178-.471-.47 1.178Z"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" style={{
        backgroundColor: colors.white,
        padding: '100px 5%',
        position: 'relative'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '15%',
          right: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.05) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          textAlign: 'center',
          maxWidth: '700px',
          margin: '0 auto 60px',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: 'rgba(37, 211, 102, 0.1)',
            color: colors.primary,
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: 600,
            marginBottom: '16px'
          }}>
            Powerful Features
          </div>
          <h2 style={{
            fontSize: isMobile ? '32px' : '40px',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '20px',
            color: colors.dark
          }}>
            Everything you need to <span style={{ color: colors.primary }}>succeed</span>
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Our platform provides all the tools you need to create a successful WhatsApp-style store.
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
          gap: '30px',
          position: 'relative',
          zIndex: 1
        }}>
          {/* Feature 1 */}
          <div style={{
            backgroundColor: colors.white,
            padding: '40px 30px',
            borderRadius: '16px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            border: '1px solid rgba(0,0,0,0.03)'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '28px', height: '28px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              WhatsApp-Style Chat Interface
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px'
            }}>
              Create automated chat flows that guide customers through your products and services with a familiar WhatsApp-like experience.
            </p>
          </div>

          {/* Feature 2 */}
          <div style={{
            backgroundColor: colors.white,
            padding: '40px 30px',
            borderRadius: '16px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            border: '1px solid rgba(0,0,0,0.03)'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '28px', height: '28px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Easy Store Management
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px'
            }}>
              Manage your products, orders, and customers all in one place with our intuitive dashboard designed for small businesses.
            </p>
          </div>

          {/* Feature 3 */}
          <div style={{
            backgroundColor: colors.white,
            padding: '40px 30px',
            borderRadius: '16px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            border: '1px solid rgba(0,0,0,0.03)'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              backgroundColor: 'rgba(37, 211, 102, 0.1)',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '24px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke={colors.primary} style={{ width: '28px', height: '28px' }}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z" />
              </svg>
            </div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Influencer Collaboration
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px'
            }}>
              Connect with influencers to promote your products and reach a wider audience through our built-in influencer marketplace.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" style={{
        backgroundColor: colors.secondary,
        padding: '100px 5%',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          bottom: '-100px',
          left: '-100px',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          textAlign: 'center',
          maxWidth: '700px',
          margin: '0 auto 60px',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: 'rgba(37, 211, 102, 0.1)',
            color: colors.primary,
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: 600,
            marginBottom: '16px'
          }}>
            Simple Process
          </div>
          <h2 style={{
            fontSize: isMobile ? '32px' : '40px',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '20px',
            color: colors.dark
          }}>
            Start selling in <span style={{ color: colors.primary }}>minutes</span>
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Our simple 4-step process gets you up and running quickly with your WhatsApp store.
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(4, 1fr)',
          gap: isMobile ? '40px' : '20px',
          position: 'relative',
          zIndex: 1,
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Step 1 */}
          <div style={{
            textAlign: 'center',
            position: 'relative'
          }}>
            {/* Connector line */}
            {!isMobile && (
              <div style={{
                position: 'absolute',
                top: '40px',
                right: '-10%',
                width: '70%',
                height: '2px',
                backgroundColor: 'rgba(37, 211, 102, 0.3)',
                zIndex: 0
              }}></div>
            )}

            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: colors.white,
              color: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              fontSize: '28px',
              fontWeight: 'bold',
              boxShadow: '0 10px 20px rgba(37, 211, 102, 0.15)',
              border: `2px solid ${colors.primary}`,
              position: 'relative',
              zIndex: 1
            }}>1</div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Sign Up
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px',
              maxWidth: '250px',
              margin: '0 auto'
            }}>
              Create your account and choose your subscription plan.
            </p>
          </div>

          {/* Step 2 */}
          <div style={{
            textAlign: 'center',
            position: 'relative'
          }}>
            {/* Connector line */}
            {!isMobile && (
              <div style={{
                position: 'absolute',
                top: '40px',
                right: '-10%',
                width: '70%',
                height: '2px',
                backgroundColor: 'rgba(37, 211, 102, 0.3)',
                zIndex: 0
              }}></div>
            )}

            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: colors.white,
              color: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              fontSize: '28px',
              fontWeight: 'bold',
              boxShadow: '0 10px 20px rgba(37, 211, 102, 0.15)',
              border: `2px solid ${colors.primary}`,
              position: 'relative',
              zIndex: 1
            }}>2</div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Set Up Your Store
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px',
              maxWidth: '250px',
              margin: '0 auto'
            }}>
              Add your products, customize your chat flows, and configure your payment methods.
            </p>
          </div>

          {/* Step 3 */}
          <div style={{
            textAlign: 'center',
            position: 'relative'
          }}>
            {/* Connector line */}
            {!isMobile && (
              <div style={{
                position: 'absolute',
                top: '40px',
                right: '-10%',
                width: '70%',
                height: '2px',
                backgroundColor: 'rgba(37, 211, 102, 0.3)',
                zIndex: 0
              }}></div>
            )}

            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: colors.white,
              color: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              fontSize: '28px',
              fontWeight: 'bold',
              boxShadow: '0 10px 20px rgba(37, 211, 102, 0.15)',
              border: `2px solid ${colors.primary}`,
              position: 'relative',
              zIndex: 1
            }}>3</div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Share Your Store
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px',
              maxWidth: '250px',
              margin: '0 auto'
            }}>
              Share your unique WhatsApp store link with your customers through social media or direct messages.
            </p>
          </div>

          {/* Step 4 */}
          <div style={{
            textAlign: 'center',
            position: 'relative'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: colors.primary,
              color: colors.white,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              fontSize: '28px',
              fontWeight: 'bold',
              boxShadow: '0 10px 20px rgba(37, 211, 102, 0.3)',
              position: 'relative',
              zIndex: 1
            }}>4</div>
            <h3 style={{
              fontSize: '22px',
              fontWeight: 600,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Start Selling
            </h3>
            <p style={{
              color: colors.gray,
              lineHeight: 1.6,
              fontSize: '16px',
              maxWidth: '250px',
              margin: '0 auto'
            }}>
              Customers can browse and purchase products through a familiar WhatsApp-like chat interface.
            </p>

            <div style={{
              marginTop: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              color: colors.primary,
              fontWeight: 600,
              fontSize: '14px'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
              </svg>
              Start your 14-day free trial
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Table Section */}
      <section id="comparison" style={{
        backgroundColor: colors.white,
        padding: '100px 5%',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '15%',
          left: '5%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.05) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          textAlign: 'center',
          maxWidth: '700px',
          margin: '0 auto 60px',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: 'rgba(37, 211, 102, 0.1)',
            color: colors.primary,
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: 600,
            marginBottom: '16px'
          }}>
            Detailed Comparison
          </div>
          <h2 style={{
            fontSize: isMobile ? '32px' : '40px',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '20px',
            color: colors.dark
          }}>
            WhatsApp Business API vs <span style={{ color: colors.primary }}>WhaMart</span>
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            See why WhaMart is the better choice for small businesses looking to create a WhatsApp-style store.
          </p>
        </div>

        {/* Comparison Table */}
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          overflowX: 'auto',
          position: 'relative',
          zIndex: 1,
          boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
          borderRadius: '16px',
          border: '1px solid rgba(0,0,0,0.03)'
        }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            backgroundColor: colors.white,
            fontSize: isMobile ? '14px' : '16px'
          }}>
            <thead>
              <tr>
                <th style={{
                  padding: '20px',
                  textAlign: 'left',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  backgroundColor: colors.lightGray,
                  fontWeight: 600,
                  color: colors.dark,
                  width: '30%'
                }}>
                  Feature
                </th>
                <th style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  backgroundColor: colors.lightGray,
                  fontWeight: 600,
                  color: colors.dark,
                  width: '35%'
                }}>
                  WhatsApp Business API
                </th>
                <th style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  backgroundColor: 'rgba(37, 211, 102, 0.1)',
                  fontWeight: 600,
                  color: colors.primary,
                  width: '35%'
                }}>
                  WhaMart
                </th>
              </tr>
            </thead>
            <tbody>
              {/* Messaging Capabilities */}
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Messaging Limits
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Limited to 1,000 messages per day initially, with tiered increases based on usage and quality rating
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Unlimited automated messages without extra costs
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Automated Chat Flows
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Limited to pre-approved message templates with minimal customization
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Fully customizable drag-and-drop chat flow builder with multiple message types
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Group Chat
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Limited functionality, businesses can only respond to messages in groups
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Customer segmentation with broadcast capabilities to specific groups
                  </span>
                </td>
              </tr>

              {/* Business Tools */}
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Product Catalog
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Basic catalog with limited customization options
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Advanced catalog with categories, images, descriptions, and integrated ordering
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Dashboard & Analytics
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Limited metrics available, detailed analytics require third-party solutions
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Comprehensive dashboard with sales, customer, and engagement analytics
                  </span>
                </td>
              </tr>

              {/* Payment Solutions */}
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Payment Processing
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Requires integration with third-party payment gateways with additional fees
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Direct payments through personal UPI IDs and QR codes without payment gateway fees
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Order Management
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Basic order tracking, requires manual management or third-party integration
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Complete order management system with status tracking and customer notifications
                  </span>
                </td>
              </tr>

              {/* Setup & Approval */}
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Setup Process
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Complex setup requiring Facebook Business verification, API integration, and technical knowledge
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Simple signup and setup process with guided store creation, ready in minutes
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Business Verification
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Strict verification process requiring business documentation and approval
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Simple verification process accessible to small businesses and individual entrepreneurs
                  </span>
                </td>
              </tr>

              {/* Pricing & Costs */}
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Messaging Costs
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Per-message charges after 24-hour customer service window, with rates varying by country
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    Unlimited messaging included in subscription with no per-message fees
                  </span>
                </td>
              </tr>
              <tr>
                <td style={{
                  padding: '20px',
                  fontWeight: 600,
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.dark
                }}>
                  Additional Costs
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.gray
                }}>
                  Additional costs for BSP services, payment gateway fees, and third-party integrations
                </td>
                <td style={{
                  padding: '20px',
                  textAlign: 'center',
                  borderBottom: '1px solid rgba(0,0,0,0.05)',
                  color: colors.accent,
                  fontWeight: 500
                }}>
                  <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16">
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                    No hidden fees, all features included in subscription with direct UPI payments
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" style={{
        backgroundColor: colors.secondary,
        padding: '100px 5%',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          bottom: '-100px',
          right: '-100px',
          width: '300px',
          height: '300px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(37,211,102,0.1) 0%, rgba(255,255,255,0) 70%)',
          zIndex: 0
        }}></div>

        <div style={{
          textAlign: 'center',
          maxWidth: '700px',
          margin: '0 auto 60px',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            display: 'inline-block',
            padding: '8px 16px',
            backgroundColor: 'rgba(37, 211, 102, 0.1)',
            color: colors.primary,
            borderRadius: '50px',
            fontSize: '14px',
            fontWeight: 600,
            marginBottom: '16px'
          }}>
            Subscription Plans
          </div>
          <h2 style={{
            fontSize: isMobile ? '32px' : '40px',
            fontWeight: 700,
            lineHeight: 1.2,
            marginBottom: '20px',
            color: colors.dark
          }}>
            Choose the plan that fits your <span style={{ color: colors.primary }}>business</span>
          </h2>
          <p style={{
            fontSize: '18px',
            lineHeight: 1.6,
            color: colors.gray,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            All plans include a 14-day free trial. No credit card required to start.
          </p>
        </div>

        {/* Pricing Cards Container */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
          gap: '30px',
          maxWidth: '1200px',
          margin: '0 auto',
          position: 'relative',
          zIndex: 1
        }}>
          {/* Standard Plan */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(0,0,0,0.03)',
            height: '100%'
          }}>
            <div style={{
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 700,
                marginBottom: '8px',
                color: colors.dark
              }}>
                Standard
              </h3>
              <p style={{
                fontSize: '16px',
                color: colors.gray,
                marginBottom: '24px'
              }}>
                Perfect for small businesses just getting started
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'baseline',
                marginBottom: '8px'
              }}>
                <span style={{
                  fontSize: '14px',
                  color: colors.gray,
                  textDecoration: 'line-through',
                  marginRight: '8px'
                }}>
                  ₹1,999/year
                </span>
                <span style={{
                  fontSize: '14px',
                  color: colors.primary,
                  fontWeight: 600,
                  backgroundColor: 'rgba(37, 211, 102, 0.1)',
                  padding: '4px 8px',
                  borderRadius: '4px'
                }}>
                  Save 25%
                </span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'baseline'
              }}>
                <span style={{
                  fontSize: '36px',
                  fontWeight: 700,
                  color: colors.dark
                }}>
                  ₹1,499
                </span>
                <span style={{
                  fontSize: '16px',
                  color: colors.gray,
                  marginLeft: '4px'
                }}>
                  /year
                </span>
              </div>
            </div>

            <div style={{
              flex: 1
            }}>
              <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: '0 0 32px 0'
              }}>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Up to <strong>20 products</strong> in catalog</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>1 chat flow</strong> with up to 10 messages</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>Unlimited</strong> daily visitors</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Basic analytics dashboard</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>UPI payment integration</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Email support</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#1DA1F2" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm3.54 6.46l-4.12 4.1-1.95-1.95a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l4.66-4.66a.75.75 0 0 0-1.06-1.06l-.03.01z"/>
                </svg>
                  <span>Verified blue tick badge</span>
                </li>
              </ul>
            </div>

            <button
              onClick={() => {
                selectPlan({
                  name: 'Standard',
                  price: '1,499',
                  features: [
                    'Up to 20 products in catalog',
                    '1 chat flow with up to 10 messages',
                    'Unlimited daily visitors',
                    'Basic analytics dashboard',
                    'UPI payment integration',
                    'Email support',
                    'Verified blue tick badge'
                  ]
                });
                navigate('/register');
              }}
              style={{
                padding: '14px 0',
                backgroundColor: colors.white,
                color: colors.primary,
                border: `1px solid ${colors.primary}`,
                borderRadius: '50px',
                textDecoration: 'none',
                fontWeight: 600,
                fontSize: '16px',
                display: 'block',
                textAlign: 'center',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                width: '100%'
              }}>
              Get Started
            </button>
          </div>

          {/* Gold Plan */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            boxShadow: '0 15px 40px rgba(37, 211, 102, 0.15)',
            display: 'flex',
            flexDirection: 'column',
            border: `2px solid ${colors.primary}`,
            height: '100%',
            position: 'relative',
            transform: isMobile ? 'scale(1)' : 'scale(1.05)',
            zIndex: 2
          }}>
            {/* Popular tag */}
            <div style={{
              position: 'absolute',
              top: '0',
              right: '30px',
              transform: 'translateY(-50%)',
              backgroundColor: colors.primary,
              color: colors.white,
              padding: '8px 16px',
              borderRadius: '50px',
              fontSize: '14px',
              fontWeight: 600
            }}>
              Most Popular
            </div>

            <div style={{
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 700,
                marginBottom: '8px',
                color: colors.dark
              }}>
                Gold
              </h3>
              <p style={{
                fontSize: '16px',
                color: colors.gray,
                marginBottom: '24px'
              }}>
                Ideal for growing businesses with more products
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'baseline',
                marginBottom: '8px'
              }}>
                <span style={{
                  fontSize: '14px',
                  color: colors.gray,
                  textDecoration: 'line-through',
                  marginRight: '8px'
                }}>
                  ₹3,999/year
                </span>
                <span style={{
                  fontSize: '14px',
                  color: colors.primary,
                  fontWeight: 600,
                  backgroundColor: 'rgba(37, 211, 102, 0.1)',
                  padding: '4px 8px',
                  borderRadius: '4px'
                }}>
                  Save 25%
                </span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'baseline'
              }}>
                <span style={{
                  fontSize: '36px',
                  fontWeight: 700,
                  color: colors.dark
                }}>
                  ₹3,000
                </span>
                <span style={{
                  fontSize: '16px',
                  color: colors.gray,
                  marginLeft: '4px'
                }}>
                  /year
                </span>
              </div>
            </div>

            <div style={{
              flex: 1
            }}>
              <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: '0 0 32px 0'
              }}>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Up to <strong>50 products</strong> in catalog</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>5 chat flows</strong> with up to 15 messages each</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>Unlimited</strong> daily visitors</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Advanced analytics dashboard</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>UPI payment integration</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Priority email support</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#1DA1F2" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm3.54 6.46l-4.12 4.1-1.95-1.95a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l4.66-4.66a.75.75 0 0 0-1.06-1.06l-.03.01z"/>
                </svg>
                  <span>Verified blue tick badge</span>
                </li>
              </ul>
            </div>

            <button
              onClick={() => {
                selectPlan({
                  name: 'Gold',
                  price: '3,000',
                  features: [
                    'Up to 50 products in catalog',
                    '5 chat flows with up to 15 messages each',
                    'Unlimited daily visitors',
                    'Advanced analytics dashboard',
                    'UPI payment integration',
                    'Priority email support',
                    'Verified blue tick badge'
                  ]
                });
                navigate('/register');
              }}
              style={{
                padding: '14px 0',
                backgroundColor: colors.white,
                color: colors.primary,
                border: `1px solid ${colors.primary}`,
                borderRadius: '50px',
                textDecoration: 'none',
                fontWeight: 600,
                fontSize: '16px',
                display: 'block',
                textAlign: 'center',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                width: '100%'
              }}>
              Get Started
            </button>
          </div>

          {/* Premium Plan */}
          <div style={{
            backgroundColor: colors.white,
            borderRadius: '16px',
            padding: '40px 30px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(0,0,0,0.03)',
            height: '100%'
          }}>
            <div style={{
              marginBottom: '24px'
            }}>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 700,
                marginBottom: '8px',
                color: colors.dark
              }}>
                Premium
              </h3>
              <p style={{
                fontSize: '16px',
                color: colors.gray,
                marginBottom: '24px'
              }}>
                For established businesses needing advanced features
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'baseline',
                marginBottom: '8px'
              }}>
                <span style={{
                  fontSize: '14px',
                  color: colors.gray,
                  textDecoration: 'line-through',
                  marginRight: '8px'
                }}>
                  ₹6,666/year
                </span>
                <span style={{
                  fontSize: '14px',
                  color: colors.primary,
                  fontWeight: 600,
                  backgroundColor: 'rgba(37, 211, 102, 0.1)',
                  padding: '4px 8px',
                  borderRadius: '4px'
                }}>
                  Save 25%
                </span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'baseline'
              }}>
                <span style={{
                  fontSize: '36px',
                  fontWeight: 700,
                  color: colors.dark
                }}>
                  ₹4,999
                </span>
                <span style={{
                  fontSize: '16px',
                  color: colors.gray,
                  marginLeft: '4px'
                }}>
                  /year
                </span>
              </div>
            </div>

            <div style={{
              flex: 1
            }}>
              <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: '0 0 32px 0'
              }}>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Up to <strong>100 products</strong> in catalog</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>15 chat flows</strong> with up to 25 messages each</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span><strong>Unlimited</strong> daily visitors</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Comprehensive analytics & reports</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>UPI & payment gateway integration</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Priority phone & email support</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill={colors.primary} viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                  </svg>
                  <span>Third-party integrations</span>
                </li>
                <li style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '16px',
                  fontSize: '15px',
                  color: colors.dark
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#1DA1F2" viewBox="0 0 16 16" style={{ marginRight: '12px', flexShrink: 0 }}>
                    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm3.54 6.46l-4.12 4.1-1.95-1.95a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l4.66-4.66a.75.75 0 0 0-1.06-1.06l-.03.01z"/>
                </svg>
                  <span>Verified blue tick badge</span>
                </li>
              </ul>
            </div>

            <button
              onClick={() => {
                selectPlan({
                  name: 'Premium',
                  price: '4,999',
                  features: [
                    'Up to 100 products in catalog',
                    '15 chat flows with up to 25 messages each',
                    'Unlimited daily visitors',
                    'Comprehensive analytics & reports',
                    'UPI & payment gateway integration',
                    'Priority phone & email support',
                    'Third-party integrations',
                    'Verified blue tick badge'
                  ]
                });
                navigate('/register');
              }}
              style={{
                padding: '14px 0',
                backgroundColor: colors.white,
                color: colors.primary,
                border: `1px solid ${colors.primary}`,
                borderRadius: '50px',
                textDecoration: 'none',
                fontWeight: 600,
                fontSize: '16px',
                display: 'block',
                textAlign: 'center',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                width: '100%'
              }}>
              Get Started
            </button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section style={{
        backgroundColor: colors.white,
        padding: '100px 5%',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '100%',
          background: `linear-gradient(90deg, ${colors.primary}05 0%, ${colors.white} 50%, ${colors.primary}05 100%)`,
          zIndex: 0
        }}></div>

        <div style={{
          maxWidth: '900px',
          margin: '0 auto',
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '40px',
          backgroundColor: colors.white,
          padding: '60px',
          borderRadius: '24px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.05)',
          position: 'relative',
          zIndex: 1,
          border: '1px solid rgba(0,0,0,0.03)'
        }}>
          <div style={{ flex: 1, textAlign: isMobile ? 'center' : 'left' }}>
            <h2 style={{
              fontSize: isMobile ? '28px' : '36px',
              fontWeight: 700,
              lineHeight: 1.2,
              marginBottom: '16px',
              color: colors.dark
            }}>
              Ready to get started?
            </h2>
            <p style={{
              fontSize: '18px',
              lineHeight: 1.6,
              color: colors.gray,
              marginBottom: '0'
            }}>
              Create your WhatsApp store today and start selling in minutes.
            </p>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <button
              onClick={() => {
                selectPlan({
                  name: 'Standard',
                  price: '1,499',
                  features: [
                    'Up to 20 products in catalog',
                    '1 chat flow with up to 10 messages',
                    'Unlimited daily visitors',
                    'Basic analytics dashboard',
                    'UPI payment integration',
                    'Email support',
                    'Verified blue tick badge'
                  ]
                });
                navigate('/register');
              }}
              style={{
                padding: '16px 32px',
                backgroundColor: colors.primary,
                color: colors.white,
                border: 'none',
                borderRadius: '50px',
                textDecoration: 'none',
                fontWeight: 600,
                fontSize: '16px',
                display: 'inline-block',
                boxShadow: '0 4px 14px rgba(37, 211, 102, 0.3)',
                transition: 'all 0.3s ease',
                textAlign: 'center',
                cursor: 'pointer'
              }}>
              Get started for free
            </button>
            <div style={{
              fontSize: '14px',
              color: colors.gray,
              textAlign: 'center'
            }}>
              No credit card required
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="footer" style={{
        backgroundColor: colors.dark,
        padding: '80px 5% 40px'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '2fr 1fr 1fr 1fr',
          gap: '40px',
          marginBottom: '50px'
        }}>
          {/* Company Info */}
          <div>
            <h3 style={{
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              color: colors.white
            }}>
              Whamart
            </h3>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              marginBottom: '24px',
              fontSize: '15px',
              lineHeight: 1.6
            }}>
              Turn your WhatsApp Business into a powerful e-commerce platform with our innovative catalog and automated response solutions.
            </p>
            <div style={{
              display: 'flex',
              gap: '16px',
              marginBottom: '24px'
            }}>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: colors.white,
                transition: 'all 0.3s ease'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Product
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <a href="#features" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Features
                </a>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <a href="#how-it-works" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  How It Works
                </a>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <a href="#pricing" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Pricing
                </a>
              </li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Company
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/about-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  About Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/blog" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Blog
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/press" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 600,
              marginBottom: '24px',
              color: colors.white
            }}>
              Support
            </h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/help-center" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Help Center
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/contact-us" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Contact Us
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/terms-of-service" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Terms of Service
                </Link>
              </li>
              <li style={{ marginBottom: '12px' }}>
                <Link to="/privacy-policy" style={{
                  color: 'rgba(255,255,255,0.7)',
                  textDecoration: 'none',
                  fontSize: '15px',
                  transition: 'all 0.3s ease'
                }}>
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div style={{
          borderTop: '1px solid rgba(255,255,255,0.1)',
          paddingTop: '30px',
          textAlign: 'center',
          color: 'rgba(255,255,255,0.5)',
          fontSize: '14px'
        }}>
          © {new Date().getFullYear()} Whamart. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
