<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use Illuminate\Http\Request;

class StoreController extends Controller
{
    /**
     * Display a listing of all stores.
     */
    public function index()
    {
        return Store::with('user')->paginate(10);
    }

    /**
     * Verify a specific store.
     */
    public function verifyStore(Store $store)
    {
        $store->isVerified = true;
        $store->save();

        return response()->json([
            'message' => 'Store verified successfully.',
            'store' => $store
        ]);
    }
}
