import React, { useEffect } from 'react';
import StoreTheme from '../components/store-theme/StoreTheme';

/**
 * StoreThemeDemo page
 * Demonstrates the WhatsApp store theme as a full-page experience
 */
const StoreThemeDemo = () => {
  // Sample store data
  const storeData = {
    name: "Fashion Store",
    logoUrl: null, // Will use default logo
    isVerified: true,
    id: "fashion-store-123"
  };

  // Set body styles to ensure full-page experience
  useEffect(() => {
    // Save original styles
    const originalOverflow = document.body.style.overflow;
    const originalMargin = document.body.style.margin;
    const originalPadding = document.body.style.padding;

    // Apply full-page styles
    document.body.style.overflow = 'hidden';
    document.body.style.margin = '0';
    document.body.style.padding = '0';

    // Restore original styles on unmount
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.margin = originalMargin;
      document.body.style.padding = originalPadding;
    };
  }, []);

  return (
    <StoreTheme storeData={storeData} />
  );
};

export default StoreThemeDemo;
