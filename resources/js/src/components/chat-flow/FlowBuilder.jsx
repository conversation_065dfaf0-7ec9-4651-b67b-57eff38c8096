import React, { useState, useCallback, useRef, useEffect } from 'react';
import ReactFlow, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  MarkerType
} from 'reactflow';
import { EyeIcon } from '@heroicons/react/24/outline';
import 'reactflow/dist/style.css';
import './FlowBuilder.css';

// Import custom components
import MessageNode from './nodes/MessageNode';
import ProductCatalogNode from './nodes/ProductCatalogNode';
import ServiceCatalogNode from './nodes/ServiceCatalogNode';
import ButtonEdge from './edges/ButtonEdge';
import MessageEditor from './MessageEditor';
import ProductCatalogEditor from './ProductCatalogEditor';
import TemplateMarketplace from './TemplateMarketplace';
import FlowSidebar from './FlowSidebar';
import ErrorModal from './ErrorModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import ChatPreviewModal from './ChatPreviewModal';

// Define node types
const nodeTypes = {
  message: MessageNode,
  product_catalog: ProductCatalogNode,
  service_catalog: ServiceCatalogNode
};

// Define edge types
const edgeTypes = {
  button: ButtonEdge
};

/**
 * FlowBuilder component for the Chat Flow Builder
 * Provides a canvas for creating and editing chat flows with smooth drag-and-drop functionality
 */
const FlowBuilder = ({
  initialNodes = [],
  initialEdges = [],
  initialFlowName = 'New Chat Flow',
  initialFlowDescription = '',
  onSave
}) => {
  // Reference to the ReactFlow instance
  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useRef(null);

  // State for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // State for connection operation
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState(null);

  // State for flow name and description
  const [flowName, setFlowName] = useState(initialFlowName);
  const [flowDescription, setFlowDescription] = useState(initialFlowDescription);

  // State for message editor
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingMessage, setEditingMessage] = useState(null);

  // State for product catalog editor
  const [isCatalogEditorOpen, setIsCatalogEditorOpen] = useState(false);
  const [editingCatalog, setEditingCatalog] = useState(null);

  // State for template marketplace
  const [isTemplateMarketplaceOpen, setIsTemplateMarketplaceOpen] = useState(false);
  const [templateType, setTemplateType] = useState('message'); // 'message' or 'product_catalog'

  // State for sidebar
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // State for error modal
  const [errorModal, setErrorModal] = useState({
    isOpen: false,
    message: ''
  });

  // State for delete confirmation modal
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    nodeId: null
  });

  // State for chat preview modal
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Show error modal
  const showError = useCallback((message) => {
    setErrorModal({
      isOpen: true,
      message
    });
  }, []);

  // Close error modal
  const closeErrorModal = useCallback(() => {
    setErrorModal({
      isOpen: false,
      message: ''
    });
  }, []);

  // Show delete confirmation modal
  const showDeleteConfirmation = useCallback((nodeId) => {
    setDeleteModal({
      isOpen: true,
      nodeId
    });
  }, []);

  // Close delete confirmation modal
  const closeDeleteConfirmation = useCallback(() => {
    setDeleteModal({
      isOpen: false,
      nodeId: null
    });
  }, []);

  // Handle message edit
  const handleEditMessage = useCallback((message) => {
    // Check if it's a product catalog node
    if (message.type === 'product_catalog') {
      setEditingCatalog(message);
      setIsCatalogEditorOpen(true);
    } else {
      // Regular message node
      setEditingMessage(message);
      setIsEditorOpen(true);
    }
  }, []);

  // Handle message delete
  const handleDeleteMessage = useCallback((nodeId) => {
    // Show delete confirmation modal instead of immediately deleting
    showDeleteConfirmation(nodeId);
  }, [showDeleteConfirmation]);

  // Handle confirmed message delete
  const handleConfirmDelete = useCallback(() => {
    if (deleteModal.nodeId) {
      // Convert nodeId to string to ensure consistent comparison
      const nodeIdStr = String(deleteModal.nodeId);

      // Remove the node
      setNodes((nds) => nds.filter((node) => node.id !== nodeIdStr));

      // Remove any connected edges
      setEdges((eds) => eds.filter((edge) =>
        edge.source !== nodeIdStr && edge.target !== nodeIdStr
      ));

      // Close the confirmation modal
      closeDeleteConfirmation();
    }
  }, [deleteModal.nodeId, setNodes, setEdges, closeDeleteConfirmation]);

  // Add edit and delete handlers to initial nodes
  useEffect(() => {
//     // Removed console.log
//     // Removed console.log

    if (initialNodes.length > 0) {
      // Reset nodes completely with the initial nodes to avoid duplicates
      setNodes(
        initialNodes.map((node) => ({
          ...node,
          data: {
            ...node.data,
            onEdit: handleEditMessage,
            onDelete: handleDeleteMessage
          }
        }))
      );

      // Reset edges completely with the initial edges
      setEdges(initialEdges);

//       // Removed console.log
//       // Removed console.log
    }
  }, [initialNodes, initialEdges, setNodes, setEdges, handleEditMessage, handleDeleteMessage]);

  // Update flow name and description when props change
  useEffect(() => {
    setFlowName(initialFlowName);
    setFlowDescription(initialFlowDescription);
  }, [initialFlowName, initialFlowDescription]);

  // Handle connection start
  const onConnectStart = useCallback((_, { nodeId, handleType }) => {
    // Only allow connections from source handles
    if (handleType === 'source') {
      // Find the source node
      const sourceNode = nodes.find((node) => node.id === nodeId);

      if (sourceNode?.data?.buttons) {
        // Count how many quick reply buttons are in this message
        const quickReplyButtons = sourceNode.data.buttons.filter(button => button.type === 'quick_reply');

        // Count how many connections already exist from this node
        const existingConnections = edges.filter(edge => edge.source === nodeId);

        // If all quick reply buttons are already connected, show warning and prevent connection
        if (quickReplyButtons.length <= existingConnections.length) {
          showError('Cannot create connection: All Quick Reply buttons in this message are already connected to other messages. Each button can only connect to one message.');
          return;
        }

        // If there are no quick reply buttons at all, show warning and prevent connection
        if (quickReplyButtons.length === 0) {
          showError('Cannot create connection: This message has no Quick Reply buttons. Only Quick Reply buttons can be connected to other messages. URL and Call buttons redirect users outside the flow.');
          return;
        }
      }

      // If we get here, allow the connection
      setIsConnecting(true);
      setConnectionSource({ nodeId, handleType });
    } else {
      // For target handles, just allow the connection
      setIsConnecting(true);
      setConnectionSource({ nodeId, handleType });
    }
  }, [nodes, edges, showError]);

  // Handle connection end
  const onConnectEnd = useCallback(
    (event) => {
      if (!isConnecting || !connectionSource) return;

      const targetElement = event.target;
      const targetNodeId = targetElement.closest('.react-flow__node')?.dataset.id;

      if (targetNodeId && connectionSource.nodeId !== targetNodeId) {
        // Find source node
        const sourceNode = nodes.find((node) => node.id === connectionSource.nodeId);

        // Only allow connections from source to target
        if (connectionSource.handleType === 'source') {
          // Check if source node has buttons
          if (sourceNode?.data?.buttons && sourceNode.data.buttons.length > 0) {
            // Get all quick reply buttons
            const quickReplyButtons = sourceNode.data.buttons
              .map((button, index) => ({ button, index }))
              .filter(({ button }) => button.type === 'quick_reply');

            // Find available quick reply buttons that aren't already connected
            const availableButtons = quickReplyButtons
              .filter(({ index }) => !edges.some(
                edge => edge.source === connectionSource.nodeId && edge.data?.buttonIndex === index
              ));

            // If there are available buttons, use the first one
            if (availableButtons.length > 0) {
              const { button, index: buttonIndex } = availableButtons[0];

              // Create a new edge with button data
              const newEdge = {
                id: `e${connectionSource.nodeId}-${targetNodeId}-${buttonIndex}`,
                source: connectionSource.nodeId,
                target: targetNodeId,
                type: 'button',
                markerEnd: {
                  type: MarkerType.ArrowClosed,
                  color: '#25D366',
                },
                animated: true,
                data: {
                  buttonText: button.text,
                  buttonIndex,
                  onDelete: (edgeId) => {
                    setEdges((eds) => eds.filter((e) => e.id !== edgeId));
                  }
                }
              };

              setEdges((eds) => addEdge(newEdge, eds));

              // Show success feedback
              const sourceEl = document.querySelector(`[data-id="${connectionSource.nodeId}"]`);
              if (sourceEl) {
                sourceEl.classList.add('connection-success');
                setTimeout(() => {
                  sourceEl.classList.remove('connection-success');
                }, 500);
              }

              // Show success message with remaining connections info
              const remainingConnections = quickReplyButtons.length - (edges.filter(edge => edge.source === connectionSource.nodeId).length + 1);
              if (remainingConnections > 0) {
//                 // Removed console.log
              } else {
//                 // Removed console.log
              }
            }
            // We don't need the else block here because we're already checking in onConnectStart
          }
        }
      }

      setIsConnecting(false);
      setConnectionSource(null);
    },
    [isConnecting, connectionSource, nodes, edges, setEdges]
  );

  // Handle flow initialization
  const onInit = useCallback((instance) => {
//     // Removed console.log
    reactFlowInstance.current = instance;

    // Fit view after a short delay to ensure nodes are properly rendered
    setTimeout(() => {
      if (reactFlowInstance.current) {
//         // Removed console.log
        reactFlowInstance.current.fitView({ padding: 0.2 });
      }
    }, 200);
  }, []);

  // Handle node drag
  const onNodeDragStop = useCallback(
    (event, node) => {
      // Update node position
      setNodes((nds) =>
        nds.map((n) => {
          if (n.id === node.id) {
            return {
              ...n,
              position: node.position
            };
          }
          return n;
        })
      );
    },
    [setNodes]
  );

  // Handle message save after editing
  const handleSaveMessage = useCallback((updatedMessage) => {
    // Convert message ID to string to ensure consistent comparison
    const messageIdStr = String(updatedMessage.id);

    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === messageIdStr) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updatedMessage,
              onEdit: handleEditMessage,
              onDelete: handleDeleteMessage
            }
          };
        }
        return node;
      })
    );

    // Close the appropriate editor panel
    setIsEditorOpen(false);
    setIsCatalogEditorOpen(false);
  }, [setNodes, handleEditMessage, handleDeleteMessage]);

  // Add a new message node
  const handleAddMessage = useCallback(() => {
    setTemplateType('message');
    setIsTemplateMarketplaceOpen(true);
  }, []);

  // Add a new product catalog node
  const handleAddProductCatalog = useCallback(() => {
    if (!reactFlowInstance.current) return;

    // Get viewport center
    const { x, y, zoom } = reactFlowInstance.current.getViewport();
    const centerX = (window.innerWidth / 2 - x) / zoom;
    const centerY = (window.innerHeight / 2 - y) / zoom;

    // Generate a unique ID for the new node
    const newNodeId = `catalog-${Date.now()}`;

    // Create new product catalog node
    const newNode = {
      id: newNodeId,
      type: 'product_catalog',
      position: { x: centerX, y: centerY },
      data: {
        id: newNodeId,
        type: 'product_catalog',
        content: 'Browse our catalog. Select a category to view items.',
        // Button to return to the welcome message
        buttons: [
          { type: 'quick_reply', text: 'Home', value: 'home' }
        ],
        header: 'Catalog',
        footer: 'Select an item to view details',
        categoryFilter: 'all',
        isProductCatalog: true, // Flag to identify this as a product catalog for preview
        onEdit: handleEditMessage,
        onDelete: handleDeleteMessage,
        isFirst: nodes.length === 0
      }
    };

    setNodes((nds) => [...nds, newNode]);
  }, [nodes, setNodes, reactFlowInstance, handleEditMessage, handleDeleteMessage]);

  // Add a new service catalog node
  const handleAddServiceCatalog = useCallback(() => {
    if (!reactFlowInstance.current) return;

    // Get viewport center
    const { x, y, zoom } = reactFlowInstance.current.getViewport();
    const centerX = (window.innerWidth / 2 - x) / zoom;
    const centerY = (window.innerHeight / 2 - y) / zoom;

    // Generate a unique ID for the new node
    const newNodeId = `service-catalog-${Date.now()}`;

    // Create new service catalog node
    const newNode = {
      id: newNodeId,
      type: 'service_catalog',
      position: { x: centerX, y: centerY },
      data: {
        id: newNodeId,
        type: 'service_catalog',
        content: 'Browse our services. Select a category to view available services.',
        // Button to return to the welcome message
        buttons: [
          { type: 'quick_reply', text: 'Home', value: 'home' }
        ],
        header: 'Services',
        footer: 'Select a service to view details',
        categoryFilter: 'all',
        isServiceCatalog: true, // Flag to identify this as a service catalog for preview
        onEdit: handleEditMessage,
        onDelete: handleDeleteMessage,
        isFirst: nodes.length === 0
      }
    };

    setNodes((nds) => [...nds, newNode]);
  }, [nodes, setNodes, reactFlowInstance, handleEditMessage, handleDeleteMessage]);

  // Handle adding a template or blank message
  const handleAddTemplate = useCallback((template = null) => {
    if (!reactFlowInstance.current) return;

    // Get viewport center
    const { x, y, zoom } = reactFlowInstance.current.getViewport();
    const centerX = (window.innerWidth / 2 - x) / zoom;
    const centerY = (window.innerHeight / 2 - y) / zoom;

    // Generate a unique ID for the new node
    const newNodeId = `message-${Date.now()}`;

    // Check if this is a product details template
    const isProductDetails = template && template.handleProductSelection;

    // Create new node with template data or default data
    const newNode = {
      id: newNodeId,
      type: 'message',
      position: { x: centerX, y: centerY },
      data: {
        id: newNodeId,
        content: template ? template.content : 'New message content',
        buttons: template ? template.buttons : [
          { type: 'quick_reply', text: 'Reply', value: 'reply_value' }
        ],
        header: template ? template.header : '',
        footer: template ? template.footer : '',
        image: template ? template.image : null,
        // Add product selection handling if this is a product details template
        handleProductSelection: isProductDetails,
        onEdit: handleEditMessage,
        onDelete: handleDeleteMessage,
        isFirst: nodes.length === 0
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setIsTemplateMarketplaceOpen(false);
  }, [nodes, setNodes, handleEditMessage, handleDeleteMessage]);

  // Save the flow
  const handleSaveFlow = useCallback(() => {
    if (onSave) {
      // Process nodes to add nextNodeId to buttons based on edges
      const processedNodes = nodes.map(node => {
        // Clone the node to avoid modifying the original
        const processedNode = { ...node };

        // Ensure node type is properly set
        if (processedNode.type === 'product_catalog') {
          processedNode.data = {
            ...processedNode.data,
            type: 'product_catalog',
            showProductCatalog: true
          };
        } else if (processedNode.type === 'service_catalog') {
          processedNode.data = {
            ...processedNode.data,
            type: 'service_catalog',
            showServiceCatalog: true
          };
        }

        // Process image data to ensure it's properly serialized
        if (processedNode.data && processedNode.data.image) {
          // Ensure image is saved as a string
          let processedImage = processedNode.data.image;

          // If image is null or undefined, keep it that way
          if (!processedImage) {
            processedImage = null;
          }
          // If image is already a string and starts with data:image/, it's good to go
          else if (typeof processedImage === 'string' && processedImage.startsWith('data:image/')) {
            // Valid data URL, keep as is
          }
          // If image is an object, try to convert it
          else if (typeof processedImage === 'object') {
            try {
              // Try to extract from common object properties
              if (processedImage.data && typeof processedImage.data === 'string' &&
                  processedImage.data.startsWith('data:image/')) {
                processedImage = processedImage.data;
              }
              else if (processedImage.src && typeof processedImage.src === 'string' &&
                       processedImage.src.startsWith('data:image/')) {
                processedImage = processedImage.src;
              }
              else if (processedImage.url && typeof processedImage.url === 'string' &&
                       processedImage.url.startsWith('data:image/')) {
                processedImage = processedImage.url;
              }
              // Try toString if it's not the default Object.toString
              else if (processedImage.toString && processedImage.toString() !== '[object Object]' &&
                       processedImage.toString().startsWith('data:image/')) {
                processedImage = processedImage.toString();
              }
              // Try to extract from JSON string
              else {
                const jsonString = JSON.stringify(processedImage);

                const dataUrlMatch = jsonString.match(/"(data:image\/[^"]+)"/);
                if (dataUrlMatch && dataUrlMatch[1]) {
                  processedImage = dataUrlMatch[1];
                }
                // Try a more flexible regex
                else {
                  const flexMatch = jsonString.match(/data:image\/[^"',}\]]+/);
                  if (flexMatch && flexMatch[0]) {
                    processedImage = flexMatch[0];
                  }
                  // If we still don't have a valid image, set to null
                  else {
                    processedImage = null;
                  }
                }
              }
            } catch (err) {
              processedImage = null;
            }
          }
          // If image is a string but not a data URL, set to null
          else if (typeof processedImage === 'string' && !processedImage.startsWith('data:image/')) {
            processedImage = null;
          }

          // Final validation - ensure it's either null or a valid data URL string
          if (processedImage !== null &&
              (typeof processedImage !== 'string' || !processedImage.startsWith('data:image/'))) {
            processedImage = null;
          }

          // Update the node data with the processed image
          processedNode.data = {
            ...processedNode.data,
            image: processedImage
          };
        }

        if (processedNode.data && processedNode.data.buttons) {
          // Get all edges that start from this node
          const nodeEdges = edges.filter(edge => edge.source === node.id);

          // Update buttons with nextNodeId based on edges
          if (nodeEdges.length > 0 && processedNode.data.buttons.length > 0) {
            const updatedButtons = processedNode.data.buttons.map((button, index) => {
              // Find edge that corresponds to this button
              const matchingEdge = nodeEdges.find(edge => edge.data && edge.data.buttonIndex === index);

              if (matchingEdge && button.type === 'quick_reply') {
                // Add nextNodeId to the button
                return {
                  ...button,
                  nextNodeId: matchingEdge.target
                };
              }
              return button;
            });

            // Update the node data with the updated buttons
            processedNode.data = {
              ...processedNode.data,
              buttons: updatedButtons
            };
          }
        }

        return processedNode;
      });

      // Prepare flow data for saving with processed nodes
      const flowData = {
        name: flowName,
        description: flowDescription,
        flowData: {
          nodes: processedNodes,
          edges: edges
        }
      };

      // Validate the flow data before saving
      try {
        // Test if the flow data can be properly serialized
        const serialized = JSON.stringify(flowData);
        const deserialized = JSON.parse(serialized);

        // Check if all nodes were properly serialized
        const originalNodeCount = processedNodes.length;
        const serializedNodeCount = deserialized.flowData.nodes.length;

        if (originalNodeCount !== serializedNodeCount) {
          alert('There was an error preparing the flow data for saving. Please try again or contact support.');
          return;
        }

        onSave(flowData);
      } catch (error) {
        alert('There was an error preparing the flow data for saving. Please try again or contact support.');
      }
    }
  }, [flowName, flowDescription, nodes, edges, onSave]);

  // Toggle sidebar
  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(!isSidebarOpen);
  }, [isSidebarOpen]);

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <FlowSidebar
        isOpen={isSidebarOpen}
        onAddMessage={handleAddMessage}
        onAddProductCatalog={handleAddProductCatalog}
        onAddServiceCatalog={handleAddServiceCatalog}
        flowName={flowName}
        setFlowName={setFlowName}
        flowDescription={flowDescription}
        setFlowDescription={setFlowDescription}
        onSave={handleSaveFlow}
      />

      {/* Main Flow Canvas */}
      <div className="flex-1 h-full" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onNodeDragStop={onNodeDragStop}
          onConnectStart={onConnectStart}
          onConnectEnd={onConnectEnd}
          onInit={onInit}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          fitViewOptions={{ padding: 0.2 }}
          attributionPosition="bottom-right"
          minZoom={0.2}
          maxZoom={1.5}
          defaultEdgeOptions={{
            type: 'button',
            animated: true, // Enable animation for smoother appearance
            style: { strokeWidth: 2, stroke: '#25D366' }
          }}
          key={`flow-${nodes.length}-${edges.length}`} // Force re-render when nodes/edges change
        >
          {/* Background */}
          <Background color="#aaa" gap={16} />

          {/* Controls */}
          <Controls />

          {/* Toggle Sidebar Button */}
          <Panel position="top-left" className="ml-2 mt-2">
            <button
              onClick={toggleSidebar}
              className="bg-white p-2 rounded-md shadow-md hover:bg-gray-50"
            >
              {isSidebarOpen ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </Panel>

          {/* Preview Button */}
          <Panel position="top-right" className="mr-2 mt-2">
            <button
              onClick={() => setIsPreviewOpen(true)}
              className="bg-green-500 text-white p-2 rounded-md shadow-md hover:bg-green-600 flex items-center"
              title="Preview Chat Flow"
            >
              <EyeIcon className="h-5 w-5 mr-1" />
              <span>Preview</span>
            </button>
          </Panel>
        </ReactFlow>
      </div>

      {/* Message Editor Side Panel */}
      <MessageEditor
        message={editingMessage}
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSaveMessage}
      />

      {/* Product Catalog Editor Side Panel */}
      <ProductCatalogEditor
        message={editingCatalog}
        isOpen={isCatalogEditorOpen}
        onClose={() => setIsCatalogEditorOpen(false)}
        onSave={handleSaveMessage}
      />

      {/* Template Marketplace Modal */}
      <TemplateMarketplace
        isOpen={isTemplateMarketplaceOpen}
        onClose={() => setIsTemplateMarketplaceOpen(false)}
        onSelectTemplate={handleAddTemplate}
        templateType={templateType}
      />

      {/* Error Modal */}
      <ErrorModal
        isOpen={errorModal.isOpen}
        message={errorModal.message}
        onClose={closeErrorModal}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onConfirm={handleConfirmDelete}
        onCancel={closeDeleteConfirmation}
      />

      {/* Chat Preview Modal */}
      <ChatPreviewModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        nodes={nodes}
        edges={edges}
        storeName={flowName}
      />
    </div>
  );
};

export default FlowBuilder;
