import React, { useState, useEffect } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useStore } from '../../contexts/StoreContext';
import '../../styles/dashboard.css';
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  HomeIcon,
  ChatBubbleLeftRightIcon,
  ShoppingBagIcon,
  UsersIcon,
  ChartBarIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  QuestionMarkCircleIcon,
  BuildingStorefrontIcon,
  AcademicCapIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';

export default function VendorDashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true); // For desktop sidebar visibility
  const { currentUser, logout } = useAuth();
  const { storeData, loading: storeLoading } = useStore();
  const navigate = useNavigate();
  const location = useLocation();

  // Force a check of the store data
  useEffect(() => {
    // Store data changed
  }, [storeData]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  }, [location.pathname]);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    if (window.innerWidth < 768) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarVisible(!sidebarVisible);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      // Handle logout error silently
    }
  };

  // Define navigation items for vendor
  const getNavItems = () => {
    // Determine if the business is service-based
    const isServiceBased = storeData?.businessType === 'service';

    // Store data is used to determine navigation items

    // Main navigation items
    const mainItems = [
      { name: 'Dashboard', href: '/vendor', icon: HomeIcon },
      // Show either Products or Services based on business type
      ...(storeData && storeData.businessType === 'service'
        ? [{ name: 'Services', href: '/vendor/services', icon: BriefcaseIcon }]
        : [{ name: 'Products', href: '/vendor/products', icon: ShoppingBagIcon }]
      ),
      { name: 'Orders', href: '/vendor/orders', icon: ChatBubbleLeftRightIcon },
      { name: 'Customers', href: '/vendor/customers', icon: UsersIcon },
      { name: 'Analytics', href: '/vendor/analytics', icon: ChartBarIcon },
    ];

    // Store related items
    const storeItems = [
      { name: 'Manage Store', href: '/vendor/store', icon: BuildingStorefrontIcon },
      {
        name: 'Chat Flow Builder',
        href: '/vendor/chat-flow',
        icon: ChatBubbleOvalLeftEllipsisIcon
      },
      { name: 'Whamart Academy', href: '/vendor/academy', icon: AcademicCapIcon },
    ];

    // Secondary navigation items
    const secondaryItems = [
      { name: 'Settings', href: '/vendor/settings', icon: CogIcon },
      { name: 'Help & Support', href: '/vendor/support', icon: QuestionMarkCircleIcon },
    ];

    return {
      main: mainItems,
      store: storeItems,
      secondary: secondaryItems
    };
  };

  // Determine the active route
  const isActiveRoute = (href) => {
    const currentPath = window.location.pathname;
    return currentPath === href || (href !== '/vendor' && currentPath.startsWith(href));
  };

  // Use useMemo to recalculate navItems when storeData changes
  const navItems = React.useMemo(() => getNavItems(), [storeData]);

  return (
    <div className="dashboard-layout">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div className={`dashboard-sidebar md:hidden z-40 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button
            type="button"
            className="flex items-center justify-center h-8 w-8 rounded-full bg-white hover:bg-opacity-90 transition-colors"
            onClick={() => setSidebarOpen(false)}
          >
            <span className="sr-only">Close sidebar</span>
            <XMarkIcon className="h-5 w-5 text-primary" aria-hidden="true" />
          </button>
        </div>

        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Store</div>
            {navItems.store.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Help & Settings</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className={`hidden md:block dashboard-sidebar ${!sidebarVisible ? 'sidebar-collapsed' : ''}`}>
        {/* Logo in sidebar with white background for better visibility */}
        <div className="dashboard-sidebar-logo">
          <img src="/WhaMart_Logo.png" alt="WhaMart Logo" className="dashboard-sidebar-logo-image" />
        </div>

        <div className="dashboard-nav-container">
          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Main Menu</div>
            {navItems.main.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Store</div>
            {navItems.store.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>

          <div className="dashboard-nav-section">
            <div className="dashboard-nav-section-title">Help & Settings</div>
            {navItems.secondary.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`dashboard-nav-item ${isActiveRoute(item.href) ? 'active' : ''}`}
              >
                <item.icon className="dashboard-nav-icon" aria-hidden="true" />
                {item.name}
              </Link>
            ))}
          </div>
        </div>

        <div className="dashboard-user-section">
          <button
            onClick={handleLogout}
            className="dashboard-logout-button"
            title="Logout"
          >
            <span>Logout</span>
            <ArrowRightOnRectangleIcon className="dashboard-logout-icon" />
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className={`dashboard-main-content ${!sidebarVisible ? 'sidebar-collapsed-content' : ''}`}>
        {/* Mobile header */}
        <div className="dashboard-mobile-header">
          <button
            type="button"
            className="dashboard-mobile-menu-button"
            onClick={toggleSidebar}
          >
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
          <div className="dashboard-mobile-brand">
            <span className="text-lg font-semibold">Vendor Dashboard</span>
          </div>
          <div className="w-6"></div> {/* Empty div for flex spacing */}
        </div>



        {/* Desktop header */}
        <div className="dashboard-header">
          <div className="dashboard-header-content">
            <div className="dashboard-header-left">
              <button
                type="button"
                className="dashboard-header-menu-button"
                onClick={toggleSidebar}
                title={sidebarVisible ? "Hide Sidebar" : "Show Sidebar"}
              >
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
              <h1 className="dashboard-header-title">Vendor Dashboard</h1>
            </div>
            <div className="dashboard-header-actions">
              <div className="dashboard-header-search">
                <input type="text" placeholder="Search..." className="dashboard-search-input" />
              </div>
              <div className="dashboard-header-user">
                <div className="dashboard-header-user-info">
                  <p className="dashboard-header-user-name">{currentUser?.name || 'User'}</p>
                  <p className="dashboard-header-user-role">Vendor</p>
                </div>
                <div className="dashboard-header-user-avatar">
                  <UserCircleIcon className="h-8 w-8 text-gray-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-content-wrapper">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
