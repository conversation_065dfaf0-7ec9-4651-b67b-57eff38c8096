import { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  CurrencyRupeeIcon,
  ChartBarIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function InvestorRevenueAnalytics() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month');
  const [revenueData, setRevenueData] = useState({
    revenueMetrics: {
      totalRevenue: 0,
      monthlyRevenue: 0,
      revenueGrowthRate: 0,
      projectedAnnualRevenue: 0,
      averageRevenuePerVendor: 0
    },
    revenueByPlan: [],
    revenueHistory: {
      monthly: [],
      quarterly: [],
      yearly: []
    },
    revenueProjections: {
      monthly: [],
      fiveYear: []
    },
    revenueByLocation: [],
    expenseBreakdown: [],
    profitMargin: {
      current: 0,
      projected: {
        oneYear: 0,
        threeYear: 0,
        fiveYear: 0
      }
    }
  });

  // Fetch revenue analytics data
  useEffect(() => {
    const fetchRevenueData = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockData = {
          revenueMetrics: {
            totalRevenue: 1450000,
            monthlyRevenue: 125000,
            revenueGrowthRate: 8.5,
            projectedAnnualRevenue: 1500000,
            averageRevenuePerVendor: 976
          },
          revenueByPlan: [
            { plan: 'Standard', value: 58500, percentage: 46.8 },
            { plan: 'Gold', value: 42000, percentage: 33.6 },
            { plan: 'Premium', value: 24500, percentage: 19.6 }
          ],
          revenueHistory: {
            monthly: [
              { month: 'Jan', value: 85000 },
              { month: 'Feb', value: 92000 },
              { month: 'Mar', value: 98000 },
              { month: 'Apr', value: 105000 },
              { month: 'May', value: 112000 },
              { month: 'Jun', value: 118000 },
              { month: 'Jul', value: 125000 }
            ],
            quarterly: [
              { quarter: 'Q1 2023', value: 275000 },
              { quarter: 'Q2 2023', value: 335000 },
              { quarter: 'Q3 2023 (Projected)', value: 390000 },
              { quarter: 'Q4 2023 (Projected)', value: 450000 }
            ],
            yearly: [
              { year: '2023', value: 1450000, projected: true },
              { year: '2024', value: 2175000, projected: true },
              { year: '2025', value: 3262500, projected: true },
              { year: '2026', value: 4893750, projected: true },
              { year: '2027', value: 7340625, projected: true }
            ]
          },
          revenueProjections: {
            monthly: [
              { month: 'Aug', value: 135000 },
              { month: 'Sep', value: 145000 },
              { month: 'Oct', value: 155000 },
              { month: 'Nov', value: 165000 },
              { month: 'Dec', value: 175000 }
            ],
            fiveYear: [
              { year: '2023', value: 1450000 },
              { year: '2024', value: 2175000 },
              { year: '2025', value: 3262500 },
              { year: '2026', value: 4893750 },
              { year: '2027', value: 7340625 }
            ]
          },
          revenueByLocation: [
            { city: 'Mumbai', value: 31250 },
            { city: 'Delhi', value: 27500 },
            { city: 'Bangalore', value: 21875 },
            { city: 'Hyderabad', value: 15000 },
            { city: 'Chennai', value: 12500 },
            { city: 'Kolkata', value: 10000 },
            { city: 'Pune', value: 6875 }
          ],
          expenseBreakdown: [
            { category: 'Server & Infrastructure', value: 25000, percentage: 20 },
            { category: 'Marketing & Acquisition', value: 37500, percentage: 30 },
            { category: 'Development', value: 31250, percentage: 25 },
            { category: 'Support & Operations', value: 18750, percentage: 15 },
            { category: 'Administrative', value: 12500, percentage: 10 }
          ],
          profitMargin: {
            current: 35,
            projected: {
              oneYear: 40,
              threeYear: 45,
              fiveYear: 50
            }
          }
        };

        setRevenueData(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching revenue data:', error);
        setLoading(false);
      }
    };

    fetchRevenueData();
  }, [timeRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  // Get revenue metrics
  const getRevenueMetrics = () => {
    if (!revenueData) return [];

    return [
      {
        name: 'Monthly Revenue',
        value: formatCurrency(revenueData.revenueMetrics.monthlyRevenue),
        change: revenueData.revenueMetrics.revenueGrowthRate,
        trend: 'up',
        icon: CurrencyRupeeIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Annual Revenue',
        value: formatCurrency(revenueData.revenueMetrics.totalRevenue),
        change: revenueData.revenueMetrics.revenueGrowthRate,
        trend: 'up',
        icon: CurrencyRupeeIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      },
      {
        name: 'Revenue Growth',
        value: formatPercentage(revenueData.revenueMetrics.revenueGrowthRate),
        change: revenueData.revenueMetrics.revenueGrowthRate,
        trend: 'up',
        icon: ChartBarIcon,
        color: 'rgba(37, 211, 102, 0.1)',
        iconColor: '#25D366'
      },
      {
        name: 'Profit Margin',
        value: formatPercentage(revenueData.profitMargin.current),
        change: revenueData.profitMargin.projected.oneYear - revenueData.profitMargin.current,
        trend: 'up',
        icon: ChartBarIcon,
        color: 'rgba(7, 94, 84, 0.1)',
        iconColor: '#075E54'
      }
    ];
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Revenue Analytics</div>
          <h1 className="welcome-title">
            Revenue <span style={{ color: '#25D366' }}>Performance</span>
          </h1>
          <p className="welcome-subtitle">
            Comprehensive analytics on revenue streams, growth, and projections.
          </p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Time Range and Export Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                timeRange === 'month'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('month')}
            >
              Month
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium ${
                timeRange === 'quarter'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('quarter')}
            >
              Quarter
            </button>
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                timeRange === 'year'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setTimeRange('year')}
            >
              Year
            </button>
          </div>

          <button
            className="btn btn-outline flex items-center"
          >
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Report
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading revenue analytics...</p>
          </div>
        ) : (
          <>
            {/* Revenue Metrics */}
            <div className="stats-grid">
              {revenueData && getRevenueMetrics().map((item) => (
                <div key={item.name} className="stat-card">
                  <div className="stat-icon-container" style={{ backgroundColor: item.color }}>
                    <item.icon style={{ color: item.iconColor, width: '24px', height: '24px' }} aria-hidden="true" />
                  </div>
                  <div className="stat-content">
                    <h3 className="stat-label">{item.name}</h3>
                    <p className="stat-value">{item.value}</p>
                    <div className="stat-change">
                      {item.trend === 'up' ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-sm ${item.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                        {item.change > 0 ? '+' : ''}{item.change}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Revenue Trend Chart */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Revenue Trend</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div style={{ height: '300px', position: 'relative' }}>
                  {/* This would be a real chart in production */}
                  <div className="bg-gray-100 h-full w-full rounded-lg flex items-center justify-center">
                    <div className="flex items-end h-64 w-full px-8">
                      {revenueData.revenueHistory.monthly.concat(revenueData.revenueProjections.monthly).map((item, index) => {
                        const isProjected = index >= revenueData.revenueHistory.monthly.length;
                        const maxValue = Math.max(
                          ...revenueData.revenueHistory.monthly.concat(revenueData.revenueProjections.monthly).map(i => i.value)
                        );
                        const height = (item.value / maxValue) * 240;

                        return (
                          <div key={index} className="flex-1 flex flex-col items-center">
                            <div
                              className={`w-12 rounded-t-md ${isProjected ? 'border border-dashed border-green-500' : ''}`}
                              style={{
                                height: `${height}px`,
                                backgroundColor: isProjected ? 'rgba(37, 211, 102, 0.2)' : '#25D366'
                              }}
                            ></div>
                            <div className="text-xs text-gray-500 mt-2">{item.month}</div>
                            <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
                <div className="flex justify-center mt-4">
                  <div className="flex items-center mr-4">
                    <div className="w-4 h-4 bg-green-500 mr-2"></div>
                    <span className="text-sm text-gray-600">Actual</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-green-200 border border-dashed border-green-500 mr-2"></div>
                    <span className="text-sm text-gray-600">Projected</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue Distribution */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Revenue Distribution</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">By Subscription Plan</h3>
                  <div className="space-y-4">
                    {revenueData.revenueByPlan.map((plan) => (
                      <div key={plan.plan} className="flex items-center">
                        <div className="w-32 text-sm font-medium text-gray-900">{plan.plan}</div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full"
                              style={{
                                width: `${plan.percentage}%`,
                                backgroundColor: plan.plan === 'Standard' ? '#25D366' : plan.plan === 'Gold' ? '#FFD700' : '#075E54'
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-24 text-right text-sm font-medium text-gray-900">{formatCurrency(plan.value)}</div>
                        <div className="w-16 text-right text-xs text-gray-500">
                          {formatPercentage(plan.percentage)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">By Geographic Location</h3>
                  <div className="space-y-4">
                    {revenueData.revenueByLocation.map((location) => (
                      <div key={location.city} className="flex items-center">
                        <div className="w-32 text-sm font-medium text-gray-900">{location.city}</div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full bg-accent"
                              style={{
                                width: `${(location.value / revenueData.revenueMetrics.monthlyRevenue) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-24 text-right text-sm font-medium text-gray-900">{formatCurrency(location.value)}</div>
                        <div className="w-16 text-right text-xs text-gray-500">
                          {formatPercentage(Math.round((location.value / revenueData.revenueMetrics.monthlyRevenue) * 100))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue Projections */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">5-Year Revenue Projections</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Annual Revenue Growth</h3>
                    <div style={{ height: '250px', position: 'relative' }}>
                      <div className="flex items-end h-full">
                        {revenueData.revenueProjections.fiveYear.map((item, index) => {
                          const maxValue = Math.max(...revenueData.revenueProjections.fiveYear.map(i => i.value));
                          const height = (item.value / maxValue) * 220;

                          return (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div
                                className={`w-16 rounded-t-md ${index > 0 ? 'border border-dashed border-green-500' : ''}`}
                                style={{
                                  height: `${height}px`,
                                  backgroundColor: index === 0 ? '#25D366' : 'rgba(37, 211, 102, 0.2)'
                                }}
                              ></div>
                              <div className="text-xs text-gray-500 mt-2">{item.year}</div>
                              <div className="text-xs font-medium text-gray-900">{formatCurrency(item.value)}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Profit Margin Projections</h3>
                    <div className="space-y-6">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-sm text-gray-500">Current Profit Margin</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(revenueData.profitMargin.current)}</p>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${revenueData.profitMargin.current}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-sm text-gray-500">1-Year Projection</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(revenueData.profitMargin.projected.oneYear)}</p>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${revenueData.profitMargin.projected.oneYear}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-sm text-gray-500">3-Year Projection</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(revenueData.profitMargin.projected.threeYear)}</p>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${revenueData.profitMargin.projected.threeYear}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <p className="text-sm text-gray-500">5-Year Projection</p>
                          <p className="text-lg font-bold text-gray-900">{formatPercentage(revenueData.profitMargin.projected.fiveYear)}</p>
                        </div>
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${revenueData.profitMargin.projected.fiveYear}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Expense Breakdown */}
            <div className="mt-8">
              <div className="section-header">
                <h2 className="section-title">Expense Breakdown</h2>
              </div>
              <div className="card" style={{ padding: '24px' }}>
                <div className="space-y-4">
                  {revenueData.expenseBreakdown.map((expense) => (
                    <div key={expense.category} className="flex items-center">
                      <div className="w-48 text-sm font-medium text-gray-900">{expense.category}</div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full bg-accent"
                            style={{
                              width: `${expense.percentage}%`
                            }}
                          ></div>
                        </div>
                      </div>
                      <div className="w-24 text-right text-sm font-medium text-gray-900">{formatCurrency(expense.value)}</div>
                      <div className="w-16 text-right text-xs text-gray-500">
                        {formatPercentage(expense.percentage)}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Total Monthly Expenses</p>
                      <p className="text-xl font-bold text-gray-900">
                        {formatCurrency(revenueData.expenseBreakdown.reduce((acc, curr) => acc + curr.value, 0))}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Monthly Profit</p>
                      <p className="text-xl font-bold text-green-600">
                        {formatCurrency(revenueData.revenueMetrics.monthlyRevenue - revenueData.expenseBreakdown.reduce((acc, curr) => acc + curr.value, 0))}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
