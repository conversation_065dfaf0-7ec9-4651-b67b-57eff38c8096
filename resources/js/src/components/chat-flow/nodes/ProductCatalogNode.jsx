import React, { memo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useStore } from 'reactflow';
import { PencilIcon, TrashIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import { productAPI } from '../../../services/api';

/**
 * ProductCatalogNode component for the Chat Flow Builder
 * Represents a product catalog message in the flow
 */
const ProductCatalogNode = ({ data, selected, isConnectable, id }) => {
  // Simple function to replace the BusinessTypeContext
  const getTermFor = (term) => {
    return term; // Just return the term as is for now
  };
  const {
    content,
    header,
    footer,
    buttons,
    isFirst,
    categoryFilter,
    onEdit,
    onDelete
  } = data;

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Get connected edges from React Flow store
  const edges = useStore((state) => state.edges);

  // Calculate connection status for buttons
  const getConnectionStatus = () => {
    if (!buttons || buttons.length === 0) return { total: 0, connected: 0 };

    const quickReplyButtons = buttons.filter(button => button.type === 'quick_reply');
    const connectedEdges = edges.filter(edge => edge.source === id);

    return {
      total: quickReplyButtons.length,
      connected: connectedEdges.length
    };
  };

  // Fetch product count when the node is created or category filter changes
  useEffect(() => {
    const fetchProductCount = async () => {
      setLoading(true);
      try {
        // Build query parameters
        const params = new URLSearchParams();

        if (categoryFilter && categoryFilter !== 'all') {
          params.append('category', categoryFilter);
        }

        // Fetch products just to get the count
        const response = await productAPI.getAllProducts(params.toString());
        setProducts(response.data.products); // Store all products to get the count
      } catch (err) {
//         // Removed console.error
      } finally {
        setLoading(false);
      }
    };

    fetchProductCount();
  }, [categoryFilter]);

  // Handle edit button click
  const handleEdit = (e) => {
    e.stopPropagation();
    if (onEdit) {
      // Make sure to pass the type property so the editor knows it's a product catalog
      onEdit({
        ...data,
        type: 'product_catalog'
      });
    }
  };

  // Handle delete button click
  const handleDelete = (e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(data.id);
    }
  };

  // Get connection status
  const connectionStatus = getConnectionStatus();

  return (
    <div
      className={`bg-white rounded-lg shadow-md w-[280px] overflow-hidden transition-all group ${
        selected ? 'ring-2 ring-green-500' : 'ring-1 ring-gray-200'
      }`}
    >
      {/* Header */}
      <div className="bg-gray-100 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
          <div className="text-sm font-medium text-gray-700 flex items-center">
            <ShoppingCartIcon className="h-4 w-4 mr-1" />
            {isFirst ? 'Start Catalog' : 'Product Catalog'}
          </div>
        </div>
        <div className="flex space-x-1">
          <button
            onClick={handleEdit}
            className="p-1 text-gray-500 hover:text-green-500 transition-colors"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={handleDelete}
            className="p-1 text-gray-500 hover:text-red-500 transition-colors"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {header && (
          <div className="text-xs font-semibold text-green-600 mb-1 p-1 bg-green-50 rounded">{header}</div>
        )}

        <div className="text-sm text-gray-800 mb-2 whitespace-pre-wrap">
          {content || 'Browse our products'}
        </div>

        {/* Product catalog info box - replacing the product preview grid */}
        <div className="border border-gray-200 rounded-lg p-3 mb-2 bg-gray-50">
          <div className="flex items-center justify-center mb-2">
            <ShoppingCartIcon className="h-5 w-5 text-green-600 mr-2" />
            <span className="text-sm font-medium">{getTermFor('Product Catalog')}</span>
          </div>

          <div className="text-xs text-center text-gray-500 mb-2">
            This message will display your store's {getTermFor('product catalog')} when customers view it.
          </div>

          <div className="text-xs text-center font-medium text-blue-600 mb-2">
            {getTermFor('Products')} will be shown in the actual store chat, not in this builder.
          </div>

          <div className="text-xs text-center font-medium text-red-600 bg-red-50 p-2 rounded">
            IMPORTANT: Add a "{getTermFor('Product Details Handler')}" template from the template library to handle {getTermFor('product')} selection!
          </div>
        </div>

        <div className="text-xs bg-blue-50 text-blue-600 p-1 rounded mb-2">
          Category: {categoryFilter === 'all' ? getTermFor('All Products') : categoryFilter}
        </div>

        <div className="text-xs bg-green-50 text-green-600 p-1 rounded mb-2">
          {getTermFor('Products')} found: {loading ? 'Loading...' : products.length}
        </div>

        {footer && (
          <div className="text-xs italic text-gray-500 mb-2 p-1 bg-gray-50 rounded">{footer}</div>
        )}

        {/* Home button info */}
        <div className="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-100">
          <div className="text-xs font-medium text-blue-700 mb-1">
            Home Button
          </div>
          <div className="text-xs text-gray-600">
            The "Home" button will restart the chat flow when clicked.
          </div>
        </div>

        {/* Connection info */}
        <div className="mt-2 p-2 bg-green-50 rounded-lg border border-green-100">
          <div className="text-xs font-medium text-green-700 mb-1">
            Product Details Connection
          </div>
          <div className="text-xs text-gray-600 mb-1">
            Connect this node to a product details message to show product information.
          </div>
          <div className="flex items-center">
            <span className="text-xs text-gray-600">Status: </span>
            <span className={`ml-1 text-xs font-medium ${
              connectionStatus.connected > 0
                ? 'text-green-600'
                : 'text-amber-500'
            }`}>
              {connectionStatus.connected > 0
                ? '✓ Connected to product details'
                : '⚠ Not connected'}
            </span>
          </div>
        </div>
      </div>

      {/* Input Handle - Only for non-first nodes */}
      {!isFirst && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          className="w-8 h-8 rounded-full bg-green-500 border-2 border-white -left-4 socket-handle"
          isConnectable={isConnectable}
        >
          <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        </Handle>
      )}

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-8 h-8 rounded-full bg-green-500 border-2 border-white -right-4 socket-handle"
        isConnectable={isConnectable}
      >
        <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
      </Handle>
    </div>
  );
};

export default memo(ProductCatalogNode);
