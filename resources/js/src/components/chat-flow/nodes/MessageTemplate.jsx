import React from 'react';
import { ArrowTopRightOnSquareIcon, PhoneIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

/**
 * MessageTemplate component for the Chat Flow Builder
 * Displays a WhatsApp-style message template with CTA buttons
 */
const MessageTemplate = ({ content, buttons = [] }) => {
  // Get button icon based on type
  const getButtonIcon = (type) => {
    switch (type) {
      case 'quick_reply':
        return <ArrowPathIcon className="w-4 h-4 mr-2" />;
      case 'url':
        return <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />;
      case 'call':
        return <PhoneIcon className="w-4 h-4 mr-2" />;
      default:
        return null;
    }
  };

  return (
    <div className="message-template">
      {/* Message content */}
      <div className="template-content">
        {content}
      </div>

      {/* CTA Buttons */}
      {buttons.map((button, index) => (
        <React.Fragment key={index}>
          <div className="template-divider"></div>
          <div className="template-button">
            <div className="button-content">
              {getButtonIcon(button.type)}
              <span>{button.text}</span>
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

export default MessageTemplate;
