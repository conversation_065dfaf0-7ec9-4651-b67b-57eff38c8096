import { createContext, useContext, useState, useEffect } from 'react';
import { storeAPI } from '../services/api';
import { useAuth } from './AuthContext';

const StoreContext = createContext();

export function useStore() {
  return useContext(StoreContext);
}

export function StoreProvider({ children }) {
  const { currentUser } = useAuth();
  const [storeData, setStoreData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Define fetchStoreData function
  const fetchStoreData = async () => {
    try {
      setLoading(true);
      const response = await storeAPI.getStoreInfo();
      setStoreData(response.data.store);
      setError(null);
    } catch (err) {
      setError('Failed to load store data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch store data when the component mounts or when the user changes
  useEffect(() => {
    if (currentUser && currentUser.userType === 'vendor') {
      fetchStoreData();

      // If the user already has store data in their profile, use it initially
      if (currentUser.store) {
        setStoreData(currentUser.store);
      }
    } else {
      setStoreData(null);
      setLoading(false);
    }
  }, [currentUser]);



  // Update store data
  const updateStoreData = (newData) => {
    setStoreData(prevData => ({
      ...prevData,
      ...newData
    }));
  };

  const value = {
    storeData,
    loading,
    error,
    fetchStoreData,
    updateStoreData
  };

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
}
