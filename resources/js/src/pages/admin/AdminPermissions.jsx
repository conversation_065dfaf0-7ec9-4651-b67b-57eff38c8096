import { useState, useEffect } from 'react';
import { 
  ShieldCheckIcon,
  PlusIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  UserGroupIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import '../../styles/vendor-dashboard.css';

export default function AdminPermissions() {
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [isAddingRole, setIsAddingRole] = useState(false);
  const [isEditingRole, setIsEditingRole] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: []
  });

  // Fetch roles and permissions
  useEffect(() => {
    const fetchRolesAndPermissions = async () => {
      setLoading(true);
      try {
        // In a real implementation, this would be an API call
        // For now, we'll use mock data
        const mockPermissions = [
          { id: 1, name: 'view_dashboard', description: 'View dashboard' },
          { id: 2, name: 'manage_users', description: 'Create, edit, and delete users' },
          { id: 3, name: 'manage_vendors', description: 'Manage vendor accounts' },
          { id: 4, name: 'manage_products', description: 'Manage products' },
          { id: 5, name: 'manage_orders', description: 'Manage orders' },
          { id: 6, name: 'manage_subscriptions', description: 'Manage subscription plans' },
          { id: 7, name: 'view_analytics', description: 'View analytics data' },
          { id: 8, name: 'manage_templates', description: 'Manage message templates' },
          { id: 9, name: 'manage_academy', description: 'Manage academy content' },
          { id: 10, name: 'manage_settings', description: 'Manage platform settings' },
          { id: 11, name: 'manage_permissions', description: 'Manage roles and permissions' }
        ];
        
        const mockRoles = [
          {
            id: 1,
            name: 'Super Admin',
            description: 'Full access to all features',
            permissions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
          },
          {
            id: 2,
            name: 'Admin',
            description: 'Administrative access with some restrictions',
            permissions: [1, 2, 3, 4, 5, 7, 8, 9]
          },
          {
            id: 3,
            name: 'Content Manager',
            description: 'Manages content and templates',
            permissions: [1, 8, 9]
          },
          {
            id: 4,
            name: 'Support Staff',
            description: 'Customer support access',
            permissions: [1, 3, 5]
          }
        ];
        
        setPermissions(mockPermissions);
        setRoles(mockRoles);
      } catch (error) {
        console.error('Error fetching roles and permissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRolesAndPermissions();
  }, []);

  // Handle role selection
  const handleSelectRole = (role) => {
    setSelectedRole(role);
    setIsAddingRole(false);
    setIsEditingRole(false);
  };

  // Handle add role
  const handleAddRole = () => {
    setSelectedRole(null);
    setIsAddingRole(true);
    setIsEditingRole(false);
    setFormData({
      name: '',
      description: '',
      permissions: []
    });
  };

  // Handle edit role
  const handleEditRole = () => {
    if (!selectedRole) return;
    
    setIsAddingRole(false);
    setIsEditingRole(true);
    setFormData({
      name: selectedRole.name,
      description: selectedRole.description,
      permissions: [...selectedRole.permissions]
    });
  };

  // Handle delete role
  const handleDeleteRole = () => {
    if (!selectedRole) return;
    
    if (window.confirm(`Are you sure you want to delete the "${selectedRole.name}" role?`)) {
      // In a real implementation, this would be an API call
      const updatedRoles = roles.filter(role => role.id !== selectedRole.id);
      setRoles(updatedRoles);
      setSelectedRole(null);
    }
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId) => {
    const updatedPermissions = [...formData.permissions];
    
    if (updatedPermissions.includes(permissionId)) {
      // Remove permission
      const index = updatedPermissions.indexOf(permissionId);
      updatedPermissions.splice(index, 1);
    } else {
      // Add permission
      updatedPermissions.push(permissionId);
    }
    
    setFormData({
      ...formData,
      permissions: updatedPermissions
    });
  };

  // Handle save role
  const handleSaveRole = () => {
    // Validate form
    if (!formData.name) {
      alert('Role name is required');
      return;
    }
    
    if (isAddingRole) {
      // Add new role
      const newRole = {
        id: Date.now(),
        name: formData.name,
        description: formData.description,
        permissions: formData.permissions
      };
      
      setRoles([...roles, newRole]);
      setSelectedRole(newRole);
    } else if (isEditingRole && selectedRole) {
      // Update existing role
      const updatedRoles = roles.map(role => 
        role.id === selectedRole.id ? 
        { ...role, name: formData.name, description: formData.description, permissions: formData.permissions } : 
        role
      );
      
      setRoles(updatedRoles);
      setSelectedRole({ ...selectedRole, name: formData.name, description: formData.description, permissions: formData.permissions });
    }
    
    setIsAddingRole(false);
    setIsEditingRole(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setIsAddingRole(false);
    setIsEditingRole(false);
  };

  // Get permission name by ID
  const getPermissionName = (permissionId) => {
    const permission = permissions.find(p => p.id === permissionId);
    return permission ? permission.description : '';
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Access Control</div>
          <h1 className="welcome-title">
            Roles & <span style={{ color: '#25D366' }}>Permissions</span>
          </h1>
          <p className="welcome-subtitle">
            Manage user roles and permissions to control access to different features of the platform.
          </p>
        </div>
      </div>

      {/* Roles and Permissions Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">User Roles</h2>
          <button 
            className="btn btn-primary"
            onClick={handleAddRole}
          >
            <PlusIcon style={{ width: '16px', height: '16px' }} />
            Add New Role
          </button>
        </div>

        {loading ? (
          <div className="card" style={{ padding: '24px', textAlign: 'center' }}>
            <p>Loading roles and permissions...</p>
          </div>
        ) : (
          <div className="flex flex-col md:flex-row gap-6">
            {/* Roles List */}
            <div className="w-full md:w-1/3">
              <div className="card" style={{ padding: '0', height: '100%' }}>
                <div className="p-4 border-b">
                  <h3 className="text-lg font-medium text-gray-900">Available Roles</h3>
                </div>
                <div className="overflow-y-auto" style={{ maxHeight: '500px' }}>
                  <ul className="divide-y divide-gray-200">
                    {roles.map(role => (
                      <li 
                        key={role.id}
                        className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedRole?.id === role.id ? 'bg-gray-50' : ''}`}
                        onClick={() => handleSelectRole(role)}
                      >
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <UserGroupIcon className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{role.name}</p>
                            <p className="text-sm text-gray-500">{role.description}</p>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Role Details or Form */}
            <div className="w-full md:w-2/3">
              {isAddingRole || isEditingRole ? (
                <div className="card" style={{ padding: '24px' }}>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    {isAddingRole ? 'Add New Role' : 'Edit Role'}
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Role Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="e.g. Content Manager"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Brief description of this role"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Permissions
                      </label>
                      <div className="space-y-2 max-h-80 overflow-y-auto p-2 border border-gray-200 rounded-md">
                        {permissions.map(permission => (
                          <div key={permission.id} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`permission-${permission.id}`}
                              checked={formData.permissions.includes(permission.id)}
                              onChange={() => handlePermissionToggle(permission.id)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                            <label htmlFor={`permission-${permission.id}`} className="ml-2 block text-sm text-gray-900">
                              {permission.description}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleSaveRole}
                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                      >
                        {isAddingRole ? 'Add Role' : 'Save Changes'}
                      </button>
                    </div>
                  </div>
                </div>
              ) : selectedRole ? (
                <div className="card" style={{ padding: '24px' }}>
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-medium text-gray-900">{selectedRole.name}</h3>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleEditRole}
                        className="p-2 text-indigo-600 hover:text-indigo-900 rounded-full hover:bg-gray-100"
                        title="Edit Role"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={handleDeleteRole}
                        className="p-2 text-red-600 hover:text-red-900 rounded-full hover:bg-gray-100"
                        title="Delete Role"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Description</h4>
                    <p className="text-sm text-gray-900">{selectedRole.description}</p>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-3">Permissions</h4>
                    <div className="bg-gray-50 rounded-md p-4">
                      <ul className="space-y-2">
                        {selectedRole.permissions.map(permissionId => (
                          <li key={permissionId} className="flex items-center text-sm">
                            <CheckIcon className="h-5 w-5 text-green-500 mr-2" />
                            {getPermissionName(permissionId)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="card flex flex-col items-center justify-center" style={{ padding: '48px 24px', height: '100%' }}>
                  <ShieldCheckIcon className="h-16 w-16 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Role Details</h3>
                  <p className="text-sm text-gray-500 text-center mb-4">
                    Select a role from the list to view its details, or create a new role to define custom permissions.
                  </p>
                  <button 
                    className="btn btn-primary"
                    onClick={handleAddRole}
                  >
                    <PlusIcon style={{ width: '16px', height: '16px' }} />
                    Add New Role
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* All Permissions Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Available Permissions</h2>
        </div>
        <div className="card" style={{ padding: '0' }}>
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Permission</th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500">Description</th>
              </tr>
            </thead>
            <tbody>
              {permissions.map(permission => (
                <tr key={permission.id} className="border-b hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <LockClosedIcon className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{permission.name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500">{permission.description}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
