import { useState } from 'react';
import {
  PlayIcon,
  ClockIcon,
  AcademicCapIcon,
  BookOpenIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';
import { StarIcon } from '@heroicons/react/24/solid';
import '../../styles/vendor-dashboard.css';

export default function VendorAcademy() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedVideo, setExpandedVideo] = useState(null);

  // Categories for filtering videos
  const categories = [
    { id: 'all', name: 'All Tutorials' },
    { id: 'getting-started', name: 'Getting Started' },
    { id: 'store-setup', name: 'Store Setup' },
    { id: 'product-management', name: 'Product Management' },
    { id: 'chat-flow', name: 'Chat Flow Builder' },
    { id: 'orders', name: 'Order Management' },
    { id: 'marketing', name: 'Marketing & Growth' }
  ];

  // Tutorial videos data
  const tutorials = [
    {
      id: 1,
      title: 'Welcome to <PERSON>ham<PERSON>',
      description: 'Learn about <PERSON><PERSON><PERSON> and how it can help you grow your business on WhatsApp.',
      thumbnail: '/academy/welcome-thumbnail.jpg',
      duration: '3:45',
      category: 'getting-started',
      featured: true,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 2,
      title: 'Creating Your Account',
      description: 'Step-by-step guide to creating and setting up your Whamart vendor account.',
      thumbnail: '/academy/account-setup-thumbnail.jpg',
      duration: '5:20',
      category: 'getting-started',
      featured: false,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 3,
      title: 'Setting Up Your Store',
      description: 'Learn how to customize your WhatsApp store with your brand information.',
      thumbnail: '/academy/store-setup-thumbnail.jpg',
      duration: '7:15',
      category: 'store-setup',
      featured: true,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 4,
      title: 'Adding Products to Your Store',
      description: 'How to add products, set prices, and manage your product catalog.',
      thumbnail: '/academy/add-products-thumbnail.jpg',
      duration: '6:30',
      category: 'product-management',
      featured: true,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 5,
      title: 'Creating Your First Chat Flow',
      description: 'Learn how to create automated chat flows for your customers.',
      thumbnail: '/academy/chat-flow-thumbnail.jpg',
      duration: '8:45',
      category: 'chat-flow',
      featured: true,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 6,
      title: 'Managing Orders',
      description: 'How to process orders, update status, and communicate with customers.',
      thumbnail: '/academy/orders-thumbnail.jpg',
      duration: '5:50',
      category: 'orders',
      featured: false,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 7,
      title: 'Retail Store Best Practices',
      description: 'Tips and strategies for retail businesses selling products on WhatsApp.',
      thumbnail: '/academy/retail-thumbnail.jpg',
      duration: '7:20',
      category: 'marketing',
      featured: false,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    },
    {
      id: 8,
      title: 'Food Business Guide',
      description: 'Specialized guide for restaurants and food businesses on Whamart.',
      thumbnail: '/academy/food-thumbnail.jpg',
      duration: '6:15',
      category: 'marketing',
      featured: false,
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    }
  ];

  // Filter tutorials based on selected category
  const filteredTutorials = activeCategory === 'all'
    ? tutorials
    : tutorials.filter(tutorial => tutorial.category === activeCategory);

  // Featured tutorials
  const featuredTutorials = tutorials.filter(tutorial => tutorial.featured);

  // Toggle video expansion
  const toggleVideoExpansion = (id) => {
    setExpandedVideo(expandedVideo === id ? null : id);
  };

  return (
    <div className="vendor-dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-decoration welcome-decoration-1"></div>
        <div className="welcome-decoration welcome-decoration-2"></div>
        <div className="welcome-content">
          <div className="welcome-badge">Learning Center</div>
          <h1 className="welcome-title">
            Whamart <span style={{ color: '#25D366' }}>Academy</span>
          </h1>
          <p className="welcome-subtitle">
            Learn how to set up and grow your WhatsApp business with our video tutorials and guides.
            Find category-specific tips and best practices for your business type.
          </p>
        </div>
      </div>

      {/* Featured Tutorials Section */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Featured Tutorials</h2>
        </div>
        <div className="stats-grid">
          {featuredTutorials.map(tutorial => (
            <div key={tutorial.id} className="stat-card" style={{ flexDirection: 'column', padding: 0, overflow: 'hidden' }}>
              <div style={{ position: 'relative', width: '100%', height: '160px', overflow: 'hidden' }}>
                <img
                  src={tutorial.thumbnail || '/academy/default-thumbnail.jpg'}
                  alt={tutorial.title}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  onError={(e) => { e.target.src = '/academy/default-thumbnail.jpg' }}
                />
                <div style={{
                  position: 'absolute',
                  bottom: '8px',
                  right: '8px',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <ClockIcon style={{ width: '12px', height: '12px' }} />
                  {tutorial.duration}
                </div>
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  backgroundColor: 'rgba(37, 211, 102, 0.9)',
                  borderRadius: '50%',
                  width: '48px',
                  height: '48px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleVideoExpansion(tutorial.id)}
                >
                  <PlayIcon style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>
              <div style={{ padding: '16px' }}>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>{tutorial.title}</h3>
                <p style={{ fontSize: '14px', color: 'var(--gray)', marginBottom: '8px' }}>{tutorial.description}</p>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => toggleVideoExpansion(tutorial.id)}
                  style={{ width: '100%' }}
                >
                  Watch Tutorial
                </button>
              </div>

              {/* Expanded video player */}
              {expandedVideo === tutorial.id && (
                <div style={{ padding: '0 16px 16px' }}>
                  <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden', borderRadius: '8px' }}>
                    <iframe
                      src={tutorial.videoUrl}
                      style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', border: 'none' }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      title={tutorial.title}
                    ></iframe>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Business Category Guides */}
      <div className="stats-section">
        <div className="section-header">
          <h2 className="section-title">Business Category Guides</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Retail Guide */}
          <div className="card" style={{ padding: '24px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{
                backgroundColor: 'rgba(37, 211, 102, 0.1)',
                borderRadius: '12px',
                width: '56px',
                height: '56px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <ShoppingBagIcon style={{ width: '28px', height: '28px', color: 'var(--primary)' }} />
              </div>
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '4px' }}>Retail & E-commerce</h3>
                <p style={{ fontSize: '14px', color: 'var(--gray)' }}>3 video guides</p>
              </div>
            </div>
            <p style={{ fontSize: '14px', color: 'var(--gray)' }}>
              Learn how to set up your retail store on WhatsApp, manage product catalog, and process orders efficiently.
            </p>
            <button className="btn btn-outline" style={{ marginTop: 'auto' }}>
              View Guides
            </button>
          </div>

          {/* Food & Restaurant Guide */}
          <div className="card" style={{ padding: '24px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{
                backgroundColor: 'rgba(37, 211, 102, 0.1)',
                borderRadius: '12px',
                width: '56px',
                height: '56px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{ width: '28px', height: '28px', color: 'var(--primary)' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8.25v-1.5m0 1.5c-1.355 0-2.697.056-4.024.166C6.845 8.51 6 9.473 6 10.608v2.513m6-4.87c1.355 0 2.697.055 4.024.165C17.155 8.51 18 9.473 18 10.608v2.513m-3-4.87v-1.5m-6 1.5v-1.5m12 9.75-1.5.75a3.354 3.354 0 0 1-3 0 3.354 3.354 0 0 0-3 0 3.354 3.354 0 0 1-3 0 3.354 3.354 0 0 0-3 0 3.354 3.354 0 0 1-3 0L3 16.5m15-3.38a48.474 48.474 0 0 0-6-.37c-2.032 0-4.034.125-6 .37m12 0c.39.049.777.102 1.163.16 1.07.16 1.837 1.094 1.837 2.175v5.17c0 .62-.504 1.124-1.125 1.124H4.125A1.125 1.125 0 0 1 3 20.625v-5.17c0-1.08.768-2.014 1.837-2.174A47.78 47.78 0 0 1 6 13.12M12.265 3.11a.375.375 0 1 1 .53 0L12.53 3.43l.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 1 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0l.265.32.265-.32a.375.375 0 0 1 .53 0" />
                </svg>
              </div>
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '4px' }}>Food & Restaurants</h3>
                <p style={{ fontSize: '14px', color: 'var(--gray)' }}>4 video guides</p>
              </div>
            </div>
            <p style={{ fontSize: '14px', color: 'var(--gray)' }}>
              Specialized guides for restaurants, cafes, and food delivery businesses using WhatsApp for orders.
            </p>
            <button className="btn btn-outline" style={{ marginTop: 'auto' }}>
              View Guides
            </button>
          </div>

          {/* Services Guide */}
          <div className="card" style={{ padding: '24px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{
                backgroundColor: 'rgba(37, 211, 102, 0.1)',
                borderRadius: '12px',
                width: '56px',
                height: '56px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{ width: '28px', height: '28px', color: 'var(--primary)' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
                </svg>
              </div>
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '4px' }}>Services</h3>
                <p style={{ fontSize: '14px', color: 'var(--gray)' }}>3 video guides</p>
              </div>
            </div>
            <p style={{ fontSize: '14px', color: 'var(--gray)' }}>
              Learn how to offer and manage services through WhatsApp, handle bookings, and provide customer support.
            </p>
            <button className="btn btn-outline" style={{ marginTop: 'auto' }}>
              View Guides
            </button>
          </div>
        </div>
      </div>

      {/* All Tutorials Section */}
      <div className="stats-section">
        <div className="section-header" style={{ marginBottom: '16px' }}>
          <h2 className="section-title">All Tutorials</h2>
          <div className="section-header-actions" style={{ display: 'flex', gap: '8px', overflowX: 'auto', paddingBottom: '8px' }}>
            {categories.map(category => (
              <button
                key={category.id}
                className={`btn ${activeCategory === category.id ? 'btn-primary' : 'btn-outline'} btn-sm`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTutorials.map(tutorial => (
            <div key={tutorial.id} className="card" style={{ overflow: 'hidden' }}>
              <div style={{ position: 'relative', width: '100%', height: '180px', overflow: 'hidden' }}>
                <img
                  src={tutorial.thumbnail || '/academy/default-thumbnail.jpg'}
                  alt={tutorial.title}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  onError={(e) => { e.target.src = '/academy/default-thumbnail.jpg' }}
                />
                <div style={{
                  position: 'absolute',
                  bottom: '8px',
                  right: '8px',
                  backgroundColor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <ClockIcon style={{ width: '12px', height: '12px' }} />
                  {tutorial.duration}
                </div>
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  backgroundColor: 'rgba(37, 211, 102, 0.9)',
                  borderRadius: '50%',
                  width: '48px',
                  height: '48px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleVideoExpansion(tutorial.id)}
                >
                  <PlayIcon style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>
              <div style={{ padding: '16px' }}>
                <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>{tutorial.title}</h3>
                <p style={{ fontSize: '14px', color: 'var(--gray)', marginBottom: '8px' }}>{tutorial.description}</p>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => toggleVideoExpansion(tutorial.id)}
                  style={{ width: '100%' }}
                >
                  Watch Tutorial
                </button>
              </div>

              {/* Expanded video player */}
              {expandedVideo === tutorial.id && (
                <div style={{ padding: '0 16px 16px' }}>
                  <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden', borderRadius: '8px' }}>
                    <iframe
                      src={tutorial.videoUrl}
                      style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', border: 'none' }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      title={tutorial.title}
                    ></iframe>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
